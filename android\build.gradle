buildscript {
    ext.kotlin_version = '1.7.10'
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.0.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.0'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url 'https://jitpack.io' }
    }
    
    // Add this to handle namespace issues for all subprojects
    plugins.withId('com.android.library') {
        android {
            def manifestFile = project.file('src/main/AndroidManifest.xml')
            if (manifestFile.exists()) {
                def manifestContent = manifestFile.text
                def packageNameMatch = manifestContent =~ /package="([^"]+)"/
                if (packageNameMatch.find()) {
                    namespace packageNameMatch.group(1)
                } else if (!hasProperty('android.namespace')) {
                    namespace "com.example.${project.name.replace('-', '_')}"
                }
            }
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
