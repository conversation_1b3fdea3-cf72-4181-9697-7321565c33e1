import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import '../models/breeding_record_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/breeding_handler.dart';
import '../../Cattle/services/cattle_handler.dart';


/// Controller state enum
enum ControllerState { initial, loading, loaded, error, empty }

class BreedingController extends ChangeNotifier {
  final BreedingHandler _breedingHandler = GetIt.instance<BreedingHandler>();
  final CattleHandler _cattleHandler = GetIt.instance<CattleHandler>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;
  
  // Data
  List<BreedingRecordIsar> _breedingRecords = [];
  List<PregnancyRecordIsar> _pregnancyRecords = [];
  List<CattleIsar> _cattle = [];
  
  // Business ID - Generate unique ID for this controller instance
  final String businessId = const Uuid().v4();

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  List<BreedingRecordIsar> get breedingRecords => List.unmodifiable(_breedingRecords);
  List<PregnancyRecordIsar> get pregnancyRecords => List.unmodifiable(_pregnancyRecords);
  List<CattleIsar> get cattle => List.unmodifiable(_cattle);
  
  // Analytics data
  int get totalBreedingRecords => _breedingRecords.length;
  int get totalPregnancyRecords => _pregnancyRecords.length;
  int get activePregnancies => _pregnancyRecords.where((p) => p.status?.toLowerCase() == 'active').length;
  int get completedPregnancies => _pregnancyRecords.where((p) => p.status?.toLowerCase() == 'completed').length;
  int get femaleCattle => _cattle.where((c) => c.gender?.toLowerCase() == 'female').length;
  
  Map<String, int> get breedingByStatus {
    final Map<String, int> statusCount = {};
    for (final record in _breedingRecords) {
      final status = record.status ?? 'Unknown';
      statusCount[status] = (statusCount[status] ?? 0) + 1;
    }
    return statusCount;
  }

  Map<String, int> get pregnancyByStatus {
    final Map<String, int> statusCount = {};
    for (final record in _pregnancyRecords) {
      final status = record.status ?? 'Unknown';
      statusCount[status] = (statusCount[status] ?? 0) + 1;
    }
    return statusCount;
  }

  // Initialize and load data
  Future<void> loadData() async {
    try {
      _setState(ControllerState.loading);

      await Future.wait([
        _loadBreedingRecords(),
        _loadPregnancyRecords(),
        _loadCattle(),
      ]);

      _setState(ControllerState.loaded);
    } catch (e, stackTrace) {
      debugPrint('Error loading breeding data: $e\n$stackTrace');
      _setError('Failed to load breeding data: ${e.toString()}');
    }
  }

  Future<void> refresh() async {
    await loadData();
  }

  Future<void> _loadBreedingRecords() async {
    try {
      final breedingData = await _breedingHandler.getAllBreedingRecords();
      _breedingRecords = breedingData;
    } catch (e) {
      debugPrint('Error loading breeding records: $e');
      throw Exception('Failed to load breeding records');
    }
  }

  Future<void> _loadPregnancyRecords() async {
    try {
      final pregnancyData = await _breedingHandler.getAllPregnancyRecords();
      _pregnancyRecords = pregnancyData;
    } catch (e) {
      debugPrint('Error loading pregnancy records: $e');
      throw Exception('Failed to load pregnancy records');
    }
  }

  Future<void> _loadCattle() async {
    try {
      final cattleData = await _cattleHandler.getAllCattle();
      _cattle = cattleData;
    } catch (e) {
      debugPrint('Error loading cattle: $e');
      throw Exception('Failed to load cattle');
    }
  }

  // Add new breeding record
  Future<void> addBreedingRecord(BreedingRecordIsar record) async {
    try {
      await _breedingHandler.addBreedingRecord(record);
      _breedingRecords.add(record);
      _breedingRecords.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding breeding record: $e');
      throw Exception('Failed to add breeding record');
    }
  }

  // Add new pregnancy record
  Future<void> addPregnancyRecord(PregnancyRecordIsar record) async {
    try {
      final addedRecord = await _breedingHandler.managePregnancyRecord(record);
      _pregnancyRecords.add(addedRecord);
      _pregnancyRecords.sort((a, b) => (b.startDate ?? DateTime.now()).compareTo(a.startDate ?? DateTime.now()));
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding pregnancy record: $e');
      throw Exception('Failed to add pregnancy record');
    }
  }

  // Update breeding record
  void updateBreedingRecord(BreedingRecordIsar updatedRecord) {
    final index = _breedingRecords.indexWhere((r) => r.businessId == updatedRecord.businessId);
    if (index >= 0) {
      _breedingRecords[index] = updatedRecord;
      _breedingRecords.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
      notifyListeners();
    }
  }

  // Update pregnancy record
  void updatePregnancyRecord(PregnancyRecordIsar updatedRecord) {
    final index = _pregnancyRecords.indexWhere((r) => r.businessId == updatedRecord.businessId);
    if (index >= 0) {
      _pregnancyRecords[index] = updatedRecord;
      _pregnancyRecords.sort((a, b) => (b.startDate ?? DateTime.now()).compareTo(a.startDate ?? DateTime.now()));
      notifyListeners();
    }
  }

  // Helper methods
  CattleIsar? getCattle(String? cattleId) {
    if (cattleId == null) return null;
    try {
      return _cattle.firstWhere(
        (cattle) => cattle.businessId == cattleId,
      );
    } catch (e) {
      return null;
    }
  }

  String getCattleName(String? cattleId) {
    final cattle = getCattle(cattleId);
    return cattle?.name ?? 'Unknown Cattle';
  }

  List<CattleIsar> get femaleCattleList {
    return _cattle.where((c) => c.gender?.toLowerCase() == 'female').toList();
  }

  // Filter methods
  List<BreedingRecordIsar> getBreedingRecordsByCattle(String cattleId) {
    return _breedingRecords.where((r) => r.cattleId == cattleId).toList();
  }

  List<PregnancyRecordIsar> getPregnancyRecordsByCattle(String cattleId) {
    return _pregnancyRecords.where((r) => r.cattleId == cattleId).toList();
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }
}
