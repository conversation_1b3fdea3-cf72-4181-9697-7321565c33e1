import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:intl/intl.dart';
// Removed unused imports
import '../../../services/database/database_helper.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/breeding_record_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../../utils/message_utils.dart';

final _logger = Logger('BreedingFormDialog');

class BreedingFormDialog extends StatefulWidget {
  final BreedingRecordIsar? record;
  final String? initialCattleId;
  final List<CattleIsar>? preloadedCattle;
  final Map<String, AnimalTypeIsar>? preloadedAnimalTypes;

  const BreedingFormDialog({
    Key? key,
    this.record,
    this.initialCattleId,
    this.preloadedCattle,
    this.preloadedAnimalTypes,
  }) : super(key: key);

  @override
  State<BreedingFormDialog> createState() => _BreedingFormDialogState();
}

class _BreedingFormDialogState extends State<BreedingFormDialog> {
  static const _inputDecorationConstraints = BoxConstraints(maxHeight: 56);
  static const _inputContentPadding =
      EdgeInsets.symmetric(horizontal: 12, vertical: 8);
  static const _animationDuration = Duration(milliseconds: 200);

  final _formKey = GlobalKey<FormState>();
  final _databaseHelper = DatabaseHelper.instance;
  final _scrollController = ScrollController();
  final TextEditingController _notesController = TextEditingController();

  List<CattleIsar> _allCattle = [];
  Map<String, AnimalTypeIsar> _animalTypes = {};
  bool _isLoading = true;
  bool _isSaving = false;
  String? _selectedCattleId;
  String? _selectedBullId;
  DateTime _breedingDate = DateTime.now();
  String _status = 'Pending';
  String _method = 'AI';
  DateTime? _expectedDate;

  // Status options with icons and colors
  final Map<String, Map<String, dynamic>> _statusInfo = {
    'Pending': {
      'icon': Icons.pending_actions,
      'color': Colors.blue,
    },
    'Confirmed': {
      'icon': Icons.check_circle,
      'color': const Color(0xFF2E7D32), // Green
    },
    'Completed': {
      'icon': Icons.task_alt,
      'color': const Color(0xFF9C27B0), // Purple
    },
    'Failed': {
      'icon': Icons.cancel,
      'color': const Color(0xFFD32F2F), // Red
    },
  };

  // Method options with icons and colors
  final Map<String, Map<String, dynamic>> _methodInfo = {
    'AI': {
      'icon': Icons.science,
      'color': Colors.teal,
    },
    'Natural': {
      'icon': Icons.pets,
      'color': Colors.brown,
    },
    'ET': {
      'icon': Icons.biotech,
      'color': Colors.indigo,
    },
    'Other': {
      'icon': Icons.more_horiz,
      'color': Colors.blue,
    },
  };

  @override
  void initState() {
    super.initState();
    _logger.info('initState called');
    _loadData();

    // If editing, populate form with existing data
    if (widget.record != null) {
      _selectedCattleId = widget.record!.cattleId;
      _selectedBullId = widget.record!.bullIdOrType;
      _breedingDate = widget.record!.date ?? DateTime.now();

      // Ensure the status is valid and exists in our status options
      final recordStatus = widget.record!.status ?? 'Pending';
      final validStatuses = ['Pending', 'Confirmed', 'Completed', 'Failed'];
      _status = validStatuses.contains(recordStatus) ? recordStatus : 'Pending';

      // Ensure the method is valid and exists in our method options
      final recordMethod = widget.record!.method ?? 'AI';
      final validMethods = ['AI', 'Natural', 'ET', 'Other'];
      _method = validMethods.contains(recordMethod) ? recordMethod : 'AI';
      _notesController.text = widget.record!.notes ?? '';
      _expectedDate = widget.record!.expectedDate;

      if (_expectedDate != null) {}
    } else if (widget.initialCattleId != null) {
      _selectedCattleId = widget.initialCattleId;
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  // Method to select breeding date
  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _breedingDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null && mounted) {
      setState(() {
        _breedingDate = picked;
        _updateExpectedDate();
      });
    }
  }

  // This is a placeholder - the actual implementation is below

  Future<void> _loadData() async {
    _logger.info('_loadData called');
    try {
      // Use preloaded data if available
      if (widget.preloadedCattle != null && widget.preloadedAnimalTypes != null) {
        _logger.info('Using preloaded data');
        // Only show female cattle
        final femaleCattle =
            widget.preloadedCattle!.where((c) => c.gender?.toLowerCase() == 'female').toList();

        if (mounted) {
          setState(() {
            _allCattle = femaleCattle;
            _animalTypes = widget.preloadedAnimalTypes!;

            // Validate selected cattle ID exists in the filtered list
            if (_selectedCattleId != null &&
                !femaleCattle.any((c) => c.tagId == _selectedCattleId)) {
              _selectedCattleId = null;
            }

            // If there's no pre-selected cattle, use the first one if available
            if (_selectedCattleId == null && femaleCattle.isNotEmpty) {
              _selectedCattleId = femaleCattle.first.tagId;
            }

            _updateExpectedDate();
            _isLoading = false;
          });
        }
      } else {
        // Fetch data from database if preloaded data is not available
        _logger.info('Fetching data from database');
        final cattle = await _databaseHelper.cattleHandler.getAllCattle();
        _logger.info('allCattle loaded: ${cattle.length}');
        final animalTypes =
            await _databaseHelper.farmSetupHandler.getAllAnimalTypes();

        // Only show female cattle
        final femaleCattle =
            cattle.where((c) => c.gender?.toLowerCase() == 'female').toList();

        if (mounted) {
          setState(() {
            _allCattle = femaleCattle;
            _animalTypes = {
              for (var type in animalTypes) type.businessId ?? '': type
            };

            // Validate selected cattle ID exists in the filtered list
            if (_selectedCattleId != null &&
                !femaleCattle.any((c) => c.tagId == _selectedCattleId)) {
              _selectedCattleId = null;
            }

            // If there's no pre-selected cattle, use the first one if available
            if (_selectedCattleId == null && femaleCattle.isNotEmpty) {
              _selectedCattleId = femaleCattle.first.tagId;
            }

            _updateExpectedDate();
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showError(context, 'Error loading cattle: $e');
        setState(() => _isLoading = false);
      }
    }
  }

  void _updateExpectedDate() {
    if (_selectedCattleId != null) {
      final selectedCattle = _allCattle.firstWhere(
        (cattle) => cattle.tagId == _selectedCattleId,
        orElse: () => _allCattle.first,
      );

      final animalType = _animalTypes[selectedCattle.animalTypeId ?? ''];
      if (animalType != null) {
        setState(() {
          _expectedDate = _breedingDate.add(
            Duration(days: animalType.defaultGestationDays ?? 283),
          );
        });
      }
    }
  }

  Future<void> _saveRecord() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();

      setState(() {
        _isSaving = true;
      });

      try {
        // Create or update a BreedingRecordIsar object
        final BreedingRecordIsar breedingRecord;

        if (widget.record != null) {
          // Update existing record
          breedingRecord = widget.record!.copyWith(
            cattleId: _selectedCattleId,
            bullIdOrType: _selectedBullId ?? '',
            date: _breedingDate,
            method: _method,
            status: _status,
            expectedDate: _expectedDate,
            notes: _notesController.text,
          );
        } else {
          // Create new record
          breedingRecord = BreedingRecordIsar.create(
            cattleId: _selectedCattleId ?? '',
            bullIdOrType: _selectedBullId ?? '',
            date: _breedingDate,
            method: _method,
            status: _status,
            expectedDate: _expectedDate,
            notes: _notesController.text,
          );
        }

        // Return the record to the caller
        Navigator.of(context).pop(breedingRecord);
      } catch (e) {
        BreedingMessageUtils.showError(context, 'Error saving record: $e');
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    const mainColor = Color(0xFF2E7D32); // Primary green color

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: AnimatedContainer(
        duration: _animationDuration,
        width: 500,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              // ignore: prefer_const_constructors
              decoration: BoxDecoration(
                color: mainColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Text(
                widget.record == null
                    ? 'Add Breeding Record'
                    : 'Edit Breeding Record',
                style: theme.textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            // Content
            Flexible(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        padding: const EdgeInsets.all(16),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Cattle Dropdown
                              DropdownButtonFormField<String>(
                                value: _allCattle.isEmpty 
                                    ? null 
                                    : _selectedCattleId != null && _allCattle.any((c) => c.tagId == _selectedCattleId)
                                        ? _selectedCattleId
                                        : _allCattle.first.tagId,
                                decoration: InputDecoration(
                                  labelText: 'Cattle',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  prefixIcon: const Icon(
                                    Icons.pets,
                                    color: Colors.brown,
                                  ),
                                ),
                                items: _allCattle
                                    .where((cattle) => cattle.tagId != null && cattle.tagId!.isNotEmpty)
                                    .map((cattle) => DropdownMenuItem<String>(
                                          value: cattle.tagId!,
                                          child: Text(
                                            '${cattle.name ?? 'Unnamed'} (${cattle.tagId})',
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ))
                                    .toList(),
                                onChanged: _allCattle.isEmpty 
                                    ? null 
                                    : (value) {
                                        if (value != null) {
                                          setState(() {
                                            _selectedCattleId = value;
                                            _updateExpectedDate();
                                          });
                                        }
                                      },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'Please select a cattle';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),

                              // Bull/Semen selection field
                              TextFormField(
                                initialValue: _selectedBullId,
                                decoration: InputDecoration(
                                  labelText: 'Bull/Semen ID',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                  prefixIcon: const Icon(
                                    Icons.male,
                                    color: Colors.blue,
                                  ),
                                ),
                                onChanged: (value) {
                                  setState(() {
                                    _selectedBullId = value;
                                  });
                                },
                                // No validator - field is optional
                                validator: null,
                              ),
                              const SizedBox(height: 16),

                              // Breeding date picker
                              InkWell(
                                onTap: () => _selectDate(context),
                                child: InputDecorator(
                                  decoration: InputDecoration(
                                    labelText: 'Breeding Date',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    contentPadding: _inputContentPadding,
                                    constraints: _inputDecorationConstraints,
                                    prefixIcon: const Icon(
                                      Icons.calendar_today,
                                      color: Colors.purple,
                                    ),
                                  ),
                                  child: Text(
                                    DateFormat('MMMM dd, yyyy')
                                        .format(_breedingDate),
                                  ),
                                ),
                              ),
                              const SizedBox(height: 16),

                              // Method selection
                              DropdownButtonFormField<String>(
                                decoration: InputDecoration(
                                  labelText: 'Breeding Method',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                ),
                                value: _method,
                                items: ['AI', 'Natural', 'ET', 'Other']
                                    .map((method) {
                                  final methodData = _methodInfo[method];
                                  return DropdownMenuItem<String>(
                                    value: method,
                                    child: Row(
                                      children: [
                                        Icon(
                                          methodData?['icon'] as IconData? ?? Icons.circle,
                                          color: methodData?['color'] as Color? ?? Colors.grey,
                                          size: 24,
                                        ),
                                        const SizedBox(width: 12),
                                        Text(method),
                                      ],
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _method = value;
                                    });
                                  }
                                },
                              ),
                              const SizedBox(height: 16),

                              // Status selection
                              DropdownButtonFormField<String>(
                                decoration: InputDecoration(
                                  labelText: 'Status',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: _inputContentPadding,
                                  constraints: _inputDecorationConstraints,
                                ),
                                value: _status,
                                items: [
                                  'Pending',
                                  'Confirmed',
                                  'Completed',
                                  'Failed'
                                ].map((status) {
                                  final statusData = _statusInfo[status];
                                  return DropdownMenuItem<String>(
                                    value: status,
                                    child: Row(
                                      children: [
                                        Icon(
                                          statusData?['icon'] as IconData? ?? Icons.circle,
                                          color: statusData?['color'] as Color? ?? Colors.grey,
                                          size: 24,
                                        ),
                                        const SizedBox(width: 12),
                                        Text(status),
                                      ],
                                    ),
                                  );
                                }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    setState(() {
                                      _status = value;
                                    });
                                  }
                                },
                              ),
                              const SizedBox(height: 16),

                              // Expected calving date display
                              if (_expectedDate != null)
                                InputDecorator(
                                  decoration: InputDecoration(
                                    labelText: 'Expected Calving Date',
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    contentPadding: _inputContentPadding,
                                    constraints: _inputDecorationConstraints,
                                    prefixIcon: const Icon(
                                      Icons.event_available,
                                      color: Colors.green,
                                    ),
                                  ),
                                  child: Text(
                                    DateFormat('MMMM dd, yyyy')
                                        .format(_expectedDate!),
                                  ),
                                ),
                              if (_expectedDate != null)
                                const SizedBox(height: 16),

                              // Notes field
                              TextFormField(
                                controller: _notesController,
                                decoration: InputDecoration(
                                  labelText: 'Notes',
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  contentPadding: const EdgeInsets.all(16),
                                  prefixIcon: const Icon(
                                    Icons.note_alt,
                                    color: Colors.deepOrange,
                                  ),
                                ),
                                maxLines: 3,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
            ),
            // Action Buttons
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed:
                          _isSaving ? null : () => Navigator.pop(context),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isSaving ? null : _saveRecord,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2E7D32),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: _isSaving
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                color: Colors.white,
                              ),
                            )
                          : const Text('Save'),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
