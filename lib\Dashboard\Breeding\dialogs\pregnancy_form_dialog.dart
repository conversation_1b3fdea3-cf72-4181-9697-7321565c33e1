import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import 'package:intl/intl.dart';
import 'package:flutter/scheduler.dart';
import '../../../services/database/database_helper.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../../utils/message_utils.dart';

final _logger = Logger('PregnancyFormDialog');

class PregnancyFormDialog extends StatefulWidget {
  final PregnancyRecordIsar? record;
  final String? initialCattleId;
  final String? breedingRecordId;
  final List<CattleIsar>? preloadedCattle;
  final Map<String, AnimalTypeIsar>? preloadedAnimalTypes;

  const PregnancyFormDialog({
    Key? key,
    this.record,
    this.initialCattleId,
    this.breedingRecordId,
    this.preloadedCattle,
    this.preloadedAnimalTypes,
  }) : super(key: key);

  @override
  State<PregnancyFormDialog> createState() => _PregnancyFormDialogState();
}

class _PregnancyFormDialogState extends State<PregnancyFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _databaseHelper = DatabaseHelper.instance;
  final TextEditingController _notesController = TextEditingController();

  List<CattleIsar> _allCattle = [];
  Map<String, dynamic> _animalTypes = {};
  bool _isLoading = true;
  String? _selectedCattleId;
  DateTime _startDate = DateTime.now();
  DateTime _deliveryDate = DateTime.now();
  String _status = 'Confirmed';
  DateTime? _expectedCalvingDate;
  DateTime? _originalStartDate;
  bool _autoCalculated = false;

  final List<String> _statusOptions = [
    'Confirmed',
    'Completed',
    'Abortion',
  ];

  // Add status descriptions and colors
  final Map<String, Map<String, dynamic>> _statusInfo = {
    'Confirmed': {
      'description': 'Pregnancy confirmed via examination',
      'color': const Color(0xFF2E7D32), // Green
      'icon': Icons.check_circle,
    },
    'Completed': {
      'description': 'Successful calving completed',
      'color': const Color(0xFF9C27B0), // Purple
      'icon': Icons.child_care,
    },
    'Abortion': {
      'description': 'Pregnancy lost or miscarriage',
      'color': const Color(0xFFD32F2F), // Red
      'icon': Icons.cancel,
    },
  };

  @override
  void initState() {
    super.initState();
    _logger.info('initState called');
    _loadData();

    // If editing, populate form with existing data
    if (widget.record != null) {
      _selectedCattleId = widget.record!.cattleId;
      _startDate = widget.record!.startDate ?? DateTime.now();
      _originalStartDate = _startDate;
      _status = widget.record!.status ?? 'Confirmed';
      _notesController.text = widget.record!.notes ?? '';
      _expectedCalvingDate = widget.record!.expectedCalvingDate;

      // For completed records, set delivery date based on actualCalvingDate or start date + gestation
      if (_status == 'Completed') {
        _deliveryDate = widget.record!.actualCalvingDate ?? _startDate;
        _autoCalculated = true;
      }
    } else if (widget.initialCattleId != null) {
      _selectedCattleId = widget.initialCattleId;
    }
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    _logger.info('_loadData called');
    try {
      // Use preloaded data if available
      if (widget.preloadedCattle != null && widget.preloadedAnimalTypes != null) {
        _logger.info('Using preloaded data');
        // Only show female cattle
        final femaleCattle =
            widget.preloadedCattle!.where((c) => c.gender?.toLowerCase() == 'female').toList();

        if (mounted) {
          setState(() {
            _allCattle = femaleCattle;
            _animalTypes = widget.preloadedAnimalTypes!;

            // If there's no pre-selected cattle, use the first one if available
            if (_selectedCattleId == null && femaleCattle.isNotEmpty) {
              _selectedCattleId = femaleCattle.first.tagId;
            }

            _updateExpectedCalvingDate();
            _isLoading = false;
          });
        }
      } else {
        // Fetch data from database if preloaded data is not available
        _logger.info('Fetching data from database');
        final cattle = await _databaseHelper.cattleHandler.getAllCattle();
        _logger.info('allCattle loaded: ${cattle.length}');
        final animalTypes = await _databaseHelper.farmSetupHandler.getAllAnimalTypes();

        // Only show female cattle
        final femaleCattle =
            cattle.where((c) => c.gender?.toLowerCase() == 'female').toList();

        if (mounted) {
          setState(() {
            _allCattle = femaleCattle;
            _animalTypes = {for (var type in animalTypes) type.businessId ?? '': type};

            // If there's no pre-selected cattle, use the first one if available
            if (_selectedCattleId == null && femaleCattle.isNotEmpty) {
              _selectedCattleId = femaleCattle.first.tagId;
            }

            _updateExpectedCalvingDate();
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showError(context, 'Error loading cattle: $e');
        setState(() => _isLoading = false);
      }
    }
  }

  void _updateExpectedCalvingDate() {
    if (_selectedCattleId != null) {
      final selectedCattle = _allCattle.firstWhere(
        (cattle) => cattle.tagId == _selectedCattleId,
        orElse: () => _allCattle.first,
      );

      final animalType = _animalTypes[selectedCattle.animalTypeId ?? ''];
      if (animalType != null) {
        setState(() {
          _expectedCalvingDate = _startDate.add(
            Duration(days: animalType.defaultGestationDays),
          );
        });
      }
    }
  }

  // Calculate start date based on delivery date for completed pregnancies
  void _calculateStartDateFromDelivery() {
    if (_selectedCattleId != null && _status == 'Completed') {
      final selectedCattle = _allCattle.firstWhere(
        (cattle) => cattle.tagId == _selectedCattleId,
        orElse: () => _allCattle.first,
      );

      final animalType = _animalTypes[selectedCattle.animalTypeId ?? ''];
      if (animalType != null) {
        setState(() {
          // Calculate the start date by subtracting gestation period from delivery date
          _startDate = _deliveryDate.subtract(
            Duration(days: animalType.defaultGestationDays),
          );
          _autoCalculated = true;
        });
      }
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    // Store the context before async operation

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _status == 'Completed' ? _deliveryDate : _startDate,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );
    if (picked != null) {
      setState(() {
        if (_status == 'Completed') {
          _deliveryDate = picked;
          _calculateStartDateFromDelivery();
        } else {
          _startDate = picked;
          _updateExpectedCalvingDate();
        }
      });
    }
  }

  void _submit() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        // Create or update a PregnancyRecordIsar object
        final PregnancyRecordIsar pregnancyRecord;

        if (widget.record != null) {
          // Update existing record
          pregnancyRecord = widget.record!.copyWith(
            cattleId: _selectedCattleId,
            startDate: _startDate,
            status: _status,
            expectedCalvingDate: _expectedCalvingDate,
            actualCalvingDate: _status == 'Completed' ? _deliveryDate : null,
            breedingRecordId: widget.breedingRecordId ?? widget.record!.breedingRecordId,
            notes: _notesController.text,
          );
        } else {
          // Create new record
          pregnancyRecord = PregnancyRecordIsar.create(
            cattleId: _selectedCattleId!,
            startDate: _startDate,
            status: _status,
            expectedCalvingDate: _expectedCalvingDate,
            actualCalvingDate: _status == 'Completed' ? _deliveryDate : null,
            breedingRecordId: widget.breedingRecordId,
            notes: _notesController.text,
          );
        }

        if (mounted) {
          SchedulerBinding.instance.addPostFrameCallback((_) {
            if (mounted && Navigator.of(context).canPop()) {
              Navigator.of(context).pop(pregnancyRecord);
            }
          });
        }
      } catch (e) {
        // If there's an error, reset loading state
        if (mounted) {
          setState(() {
            _isLoading = false;
          });

          // Show error message
          BreedingMessageUtils.showError(context, 'Error: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    const mainColor = Color(0xFF2E7D32); // Primary green color
    const animationDuration = Duration(milliseconds: 200);

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: AnimatedContainer(
        duration: animationDuration,
        width: 500,
        constraints: const BoxConstraints(maxHeight: 700),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                color: mainColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Text(
                widget.record == null
                    ? 'Add Pregnancy Record'
                    : 'Edit Pregnancy Record',
                style: theme.textTheme.titleLarge?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            // Content
            Flexible(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Form(
                        key: _formKey,
                        child: SingleChildScrollView(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // Cattle selection
                      DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: 'Cattle',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                          prefixIcon: const Icon(
                            Icons.pets,
                            color: Colors.brown,
                          ),
                        ),
                        value: _selectedCattleId,
                        items: _allCattle.map((cattle) {
                          return DropdownMenuItem<String>(
                            value: cattle.tagId,
                            child: Text(
                                '${cattle.name ?? 'No name'} (${cattle.tagId ?? 'No ID'})'),
                          );
                        }).toList(),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please select a cattle';
                          }
                          return null;
                        },
                        onChanged: (value) {
                          setState(() {
                            _selectedCattleId = value;
                            _updateExpectedCalvingDate();
                          });
                        },
                        isExpanded: true,
                      ),
                      const SizedBox(height: 24),

                      // Status selection
                      DropdownButtonFormField<String>(
                        decoration: InputDecoration(
                          labelText: 'Status',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                        ),
                        value: _status,
                        items: _statusOptions.map((status) {
                          final statusData = _statusInfo[status];
                          return DropdownMenuItem<String>(
                            value: status,
                            child: Row(
                              children: [
                                Icon(
                                  statusData?['icon'] as IconData? ?? Icons.circle,
                                  color: statusData?['color'] as Color? ?? Colors.grey,
                                  size: 24,
                                ),
                                const SizedBox(width: 12),
                                Text(status),
                              ],
                            ),
                          );
                        }).toList(),
                        onChanged: (value) {
                          setState(() {
                            _status = value!;
                            // Reset auto calculation flag when status changes
                            _autoCalculated = false;
                            // Additional logic when status changes to/from Completed
                            if (_status == 'Completed') {
                              _deliveryDate = DateTime.now();
                              _calculateStartDateFromDelivery();
                            } else {
                              // If changing from Completed to another status, restore original date
                              if (_originalStartDate != null) {
                                _startDate = _originalStartDate!;
                              }
                              _updateExpectedCalvingDate();
                            }
                          });
                        },
                        isExpanded: true,
                      ),
                      const SizedBox(height: 24),

                      // Date field (changes based on status)
                      GestureDetector(
                        onTap: () => _selectDate(context),
                        child: AbsorbPointer(
                          child: TextFormField(
                            decoration: InputDecoration(
                              labelText: _status == 'Completed'
                                  ? 'Calving Date'
                                  : 'Start Date',
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              prefixIcon: Icon(
                                Icons.calendar_today,
                                color: _status == 'Completed' ? Colors.green : Colors.orange,
                              ),
                              contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                            ),
                            controller: TextEditingController(
                              text: DateFormat('MMMM dd, yyyy').format(
                                _status == 'Completed' ? _deliveryDate : _startDate,
                              ),
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'Please select a date';
                              }
                              return null;
                            },
                          ),
                        ),
                      ),
                      if (_status == 'Completed' && _autoCalculated)
                        Padding(
                          padding: const EdgeInsets.only(top: 8, left: 12),
                          child: Text(
                            'Calculated start date: ${DateFormat('MMMM dd, yyyy').format(_startDate)}',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      const SizedBox(height: 24),

                      // Expected calving date (only visible for Confirmed status)
                      if (_status == 'Confirmed') ...[
                        TextFormField(
                          enabled: false,
                          decoration: InputDecoration(
                            labelText: 'Expected Calving Date',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            prefixIcon: const Icon(
                              Icons.event_available,
                              color: Colors.green,
                            ),
                            contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                          ),
                          controller: TextEditingController(
                            text: _expectedCalvingDate != null
                                ? DateFormat('MMMM dd, yyyy')
                                    .format(_expectedCalvingDate!)
                                : 'Calculating...',
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],

                      // Notes
                      TextFormField(
                        controller: _notesController,
                        decoration: InputDecoration(
                          labelText: 'Notes',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          alignLabelWithHint: true,
                          prefixIcon: const Icon(
                            Icons.note_alt,
                            color: Colors.deepOrange,
                          ),
                          contentPadding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
                        ),
                        maxLines: 3,
                        minLines: 3,
                      ),
                    ],
                  ),
                ),
              ),
                    ),
            ),
            // Action Buttons
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: TextButton(
                      onPressed: _isLoading
                          ? null
                          : () {
                              if (mounted) {
                                SchedulerBinding.instance.addPostFrameCallback((_) {
                                  if (mounted && Navigator.of(context).canPop()) {
                                    Navigator.of(context).pop();
                                  }
                                });
                              }
                            },
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Cancel'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _submit,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: mainColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(
                        widget.record == null ? 'Add Record' : 'Update Record',
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
