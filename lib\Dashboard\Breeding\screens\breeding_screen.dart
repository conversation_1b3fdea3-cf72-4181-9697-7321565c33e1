import 'package:flutter/material.dart';

import '../../../widgets/reusable_tab_bar.dart';
import '../controllers/breeding_controller.dart';
import '../tabs/breeding_records_tab.dart';
import '../tabs/pregnancy_records_tab.dart';
import '../tabs/delivery_records_tab.dart';
import '../tabs/breeding_analytics_tab.dart';
import '../tabs/breeding_insights_tab.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart'; // Import Universal Components

class BreedingScreen extends StatefulWidget {
  const BreedingScreen({super.key});

  @override
  State<BreedingScreen> createState() => _BreedingScreenState();
}

class _BreedingScreenState extends State<BreedingScreen>
    with TickerProviderStateMixin, UniversalScreenState {
  late TabController _tabController;
  late BreedingController _breedingController;

  final List<TabItem> _tabs = const [
    TabItem(icon: Icons.analytics, label: 'Analytics', color: UniversalEmptyStateTheme.breeding),
    TabItem(icon: Icons.favorite, label: 'Breeding', color: UniversalEmptyStateTheme.breeding),
    TabItem(icon: Icons.pregnant_woman, label: 'Pregnancy', color: UniversalEmptyStateTheme.breeding),
    TabItem(icon: Icons.child_care, label: 'Delivery', color: UniversalEmptyStateTheme.breeding),
    TabItem(icon: Icons.lightbulb, label: 'Insights', color: UniversalEmptyStateTheme.breeding),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _breedingController = BreedingController();
    _breedingController.loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _breedingController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Breeding Management'),
        backgroundColor: UniversalEmptyStateTheme.breeding,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.breedingReport,
            ),
            tooltip: 'View Breeding Reports',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => executeWithLoading(() => _breedingController.refresh()),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar with multicolor support
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
            useMulticolor: true,
            indicatorColor: UniversalEmptyStateTheme.breeding,
          ),
          // TabBarView
          Expanded(
            child: ListenableBuilder(
              listenable: _breedingController,
              builder: (context, child) {
                final controllerState = _getScreenStateFromController();

                return UniversalStateBuilder(
                  state: controllerState,
                  errorMessage: _breedingController.errorMessage,
                  onRetry: () => executeWithLoading(() => _breedingController.refresh()),
                  moduleColor: UniversalEmptyStateTheme.breeding,
                  loadingWidget: UniversalLoadingIndicator.breeding(),
                  errorWidget: UniversalErrorIndicator.breeding(
                    message: _breedingController.errorMessage ?? 'Failed to load breeding data',
                    onRetry: () => executeWithLoading(() => _breedingController.refresh()),
                  ),
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      BreedingAnalyticsTab(controller: _breedingController),
                      BreedingRecordsTab(controller: _breedingController),
                      PregnancyRecordsTab(controller: _breedingController),
                      DeliveryRecordsTab(controller: _breedingController),
                      BreedingInsightsTab(controller: _breedingController),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  // Helper method to sync controller state with Universal Screen State
  ScreenState _getScreenStateFromController() {
    switch (_breedingController.state) {
      case ControllerState.initial:
        return ScreenState.initial;
      case ControllerState.loading:
        return ScreenState.loading;
      case ControllerState.loaded:
        return ScreenState.loaded;
      case ControllerState.error:
        return ScreenState.error;
      case ControllerState.empty:
        return ScreenState.empty;
    }
  }
}
