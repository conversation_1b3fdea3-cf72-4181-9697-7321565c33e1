import 'dart:convert';
import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';

import '../models/breeding_record_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../models/breeding_event_isar.dart';
import '../models/delivery_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';
import '../../../services/streams/stream_service.dart';

/// Exception thrown when a database operation fails
class DatabaseOperationException extends DatabaseException {
  DatabaseOperationException(String message, [String details = ''])
      : super(message, details);
}

/// Exception for when cattle are not found
class CattleNotFoundException extends RecordNotFoundException {
  CattleNotFoundException(String message) : super(message);
}

/// Consolidated handler for all Breeding module database operations
class BreedingHandler {
  final Logger _logger = Logger('BreedingHandler');
  final IsarService _isarService;

  // Singleton instance
  static final BreedingHandler _instance = BreedingHandler._internal();
  static BreedingHandler get instance => _instance;

  // Private constructor
  BreedingHandler._internal() : _isarService = GetIt.instance<IsarService>() {
    _logger.info('BreedingHandler initialized');
  }

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== BREEDING RECORDS ===//

  /// Get all breeding records
  Future<List<BreedingRecordIsar>> getAllBreedingRecords() async {
    try {
      return await _isar.breedingRecordIsars.where().sortByDateDesc().findAll();
    } catch (e) {
      _logger.severe('Error getting all breeding records: $e');
      throw DatabaseException(
          'Failed to retrieve breeding records', e.toString());
    }
  }

  /// Get breeding records for a specific cattle
  Future<List<BreedingRecordIsar>> getBreedingRecordsForCattle(
      String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.breedingRecordIsars
          .filter()
          .cattleIdEqualTo(cattleId)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting breeding records for cattle $cattleId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve breeding records', e.toString());
    }
  }

  /// Get the next breeding sequence number for a cattle
  Future<int> getNextBreedingNumber(String cattleId) async {
    try {
      final breedingRecords = await getBreedingRecordsForCattle(cattleId);
      return breedingRecords.length + 1;
    } catch (e) {
      _logger.severe('Error getting next breeding number: $e');
      return 1;
    }
  }

  /// Add a new breeding record
  Future<void> addBreedingRecord(BreedingRecordIsar record,
      {bool createPregnancyRecord = false}) async {
    try {
      await _validateBreedingRecord(record);

      // Ensure the record has a properly formatted ID
      if (record.businessId == null ||
          !record.businessId!.contains('-Breeding-')) {
        // Get the next sequence number
        final sequenceNumber = await getNextBreedingNumber(record.cattleId!);

        // Create a formatted ID
        final formattedId = BreedingRecordIsar.generateFormattedId(
            record.cattleId!, sequenceNumber);

        // Update the record with the formatted ID
        record = record.copyWith(businessId: formattedId);
      }

      // Create a variable to store the pregnancy record if one is created
      PregnancyRecordIsar? createdPregnancyRecord;

      await _isar.writeTxn(() async {
        // Save the breeding record
        await _isar.breedingRecordIsars.put(record);

        // Create pregnancy record if needed
        if (createPregnancyRecord &&
            (record.status == 'Confirmed' || record.status == 'Completed')) {
          // Get the next sequence number for the pregnancy record ID
          final sequenceNumber = await getNextPregnancyNumber(record.cattleId!);

          // Create a formatted ID for the pregnancy record
          final formattedId = PregnancyRecordIsar.generateFormattedId(
              record.cattleId!, sequenceNumber);

          // Create the pregnancy record with the formatted ID
          final pregnancyRecord = PregnancyRecordIsar.create(
            cattleId: record.cattleId!,
            startDate: record.date!,
            status: record.status == 'Completed' ? 'Completed' : 'Confirmed',
            breedingRecordId: record.businessId,
            expectedCalvingDate: record.expectedDate,
            notes: 'Created from breeding record: ${record.businessId}',
            businessId: formattedId,
          );

          // Save the pregnancy record
          await _isar.pregnancyRecordIsars.put(pregnancyRecord);

          // Store the created pregnancy record for later notification
          createdPregnancyRecord = pregnancyRecord;

          // Update cattle breeding status
          await _updateCattleBreedingStatus(record);
        }

        // Create breeding events
        await _createBreedingEvents(record);
      });

      // Notify any listeners about the breeding record change
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyBreedingChange({
        'action': 'add',
        'record': record.toMap(),
        'cattleId': record.cattleId,
        'id': record.businessId,
        'recordId': record.businessId,
      });

      // If a pregnancy record was created, also notify about that
      if (createdPregnancyRecord != null) {
        final pregnancyId = createdPregnancyRecord?.businessId ?? 'unknown';
        final pregnancyCattleId = createdPregnancyRecord?.cattleId ?? '';

        _logger.info(
            'Also notifying about created pregnancy record: $pregnancyId');

        // Create a map with the pregnancy record data
        final Map<String, dynamic> pregnancyData = {
          'action': 'add',
          'cattleId': pregnancyCattleId,
          'id': pregnancyId,
          'recordId': pregnancyId,
        };

        // Add the record data if available
        try {
          pregnancyData['record'] = createdPregnancyRecord?.toMap() ??
              {
                'id': pregnancyId,
                'cattleId': pregnancyCattleId,
                'status': 'Confirmed',
              };
        } catch (e) {
          _logger.warning('Error converting pregnancy record to map: $e');
          // Create a minimal record map
          pregnancyData['record'] = {
            'id': pregnancyId,
            'cattleId': pregnancyCattleId,
            'status': 'Confirmed',
          };
        }

        // Notify about the pregnancy record
        streamService.notifyPregnancyChange(pregnancyData);
      }

      _logger.info('Successfully added breeding record: ${record.businessId}');
    } catch (e) {
      _logger.severe('Error adding breeding record: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to add breeding record', e.toString());
    }
  }

  /// Update an existing breeding record
  Future<void> updateBreedingRecord(BreedingRecordIsar record,
      {bool updatePregnancyRecord = false}) async {
    try {
      await _validateBreedingRecord(record);

      // First, get the existing record to ensure we have the correct Isar ID
      final existingRecord = await _isar.breedingRecordIsars
          .filter()
          .businessIdEqualTo(record.businessId!)
          .findFirst();

      if (existingRecord == null) {
        throw RecordNotFoundException(
            'Breeding record not found for update: ${record.businessId}');
      }

      // Copy the Isar ID from the existing record to ensure proper update
      record.id = existingRecord.id;
      record.updatedAt = DateTime.now();

      await _isar.writeTxn(() async {
        // Update the breeding record with the correct Isar ID
        await _isar.breedingRecordIsars.put(record);

        // Update related pregnancy record if needed
        if (updatePregnancyRecord) {
          await _updateRelatedPregnancyRecord(record);
        }

        // Update breeding events
        await _updateBreedingEvents(record);

        // Update cattle breeding status
        await _updateCattleBreedingStatus(record);
      });

      // Notify any listeners about the breeding record change
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyBreedingChange({
        'action': 'update',
        'record': record.toMap(),
        'cattleId': record.cattleId,
        'id': record.businessId,
        'recordId': record.businessId,
      });

      _logger
          .info('Successfully updated breeding record: ${record.businessId}');
    } catch (e) {
      _logger.severe('Error updating breeding record: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to update breeding record', e.toString());
    }
  }

  /// Delete a breeding record with full cascading deletion
  Future<void> deleteBreedingRecord(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Breeding record ID is required');
      }

      // Get the breeding record first to extract the cattle ID
      final record = await _isar.breedingRecordIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();

      if (record == null) {
        throw RecordNotFoundException(
            'Breeding record not found: $businessId');
      }

      final cattleId = record.cattleId;

      // Store IDs for notifications after transaction
      final List<String> deletedPregnancyIds = [];
      final List<String> deletedDeliveryIds = [];

      await _isar.writeTxn(() async {
        // Delete related pregnancy records
        final pregnancyRecords = await _isar.pregnancyRecordIsars
            .filter()
            .breedingRecordIdEqualTo(businessId)
            .findAll();

        for (final pregnancyRecord in pregnancyRecords) {
          // Find any delivery records associated with this pregnancy
          final deliveryRecords = await _isar.deliveryRecordIsars
              .filter()
              .pregnancyIdEqualTo(pregnancyRecord.businessId)
              .findAll();

          // Delete associated delivery records
          for (final deliveryRecord in deliveryRecords) {
            await _isar.deliveryRecordIsars.delete(deliveryRecord.id);
            if (deliveryRecord.businessId != null) {
              deletedDeliveryIds.add(deliveryRecord.businessId!);
            }
            _logger.info('Cascade deleted delivery record: ${deliveryRecord.businessId}');
          }

          // Delete the pregnancy record
          await _isar.pregnancyRecordIsars.delete(pregnancyRecord.id);
          if (pregnancyRecord.businessId != null) {
            deletedPregnancyIds.add(pregnancyRecord.businessId!);
          }
          _logger.info('Cascade deleted pregnancy record: ${pregnancyRecord.businessId}');
        }

        // Delete related breeding events
        final events = await _isar.breedingEventIsars
            .filter()
            .relatedRecordIdEqualTo(businessId)
            .findAll();

        for (final event in events) {
          await _isar.breedingEventIsars.delete(event.id);
        }

        // Delete the breeding record
        await _isar.breedingRecordIsars.delete(record.id);

        // Reset cattle breeding status if needed
        if (cattleId != null) {
          await _resetCattleBreedingStatus(cattleId);
        }
      });

      // Notify listeners about all deletions after transaction is complete
      final streamService = GetIt.instance<StreamService>();

      // 1. Notify about breeding record deletion
      streamService.notifyBreedingChange({
        'action': 'delete',
        'id': businessId, // Use 'id' to match what the view is expecting
        'recordId': businessId, // Keep recordId for backward compatibility
        'cattleId': cattleId,
      });

      // 2. Notify about pregnancy record deletions
      for (final pregnancyId in deletedPregnancyIds) {
        streamService.notifyPregnancyChange({
          'action': 'delete',
          'id': pregnancyId,
          'recordId': pregnancyId,
          'cattleId': cattleId,
        });
      }

      // 3. Notify about delivery record deletions
      for (final deliveryId in deletedDeliveryIds) {
        streamService.notifyDeliveryChange({
          'action': 'delete',
          'id': deliveryId,
          'recordId': deliveryId,
          'cattleId': cattleId,
        });
      }

      _logger.info('Successfully deleted breeding record: $businessId');
    } catch (e) {
      _logger.severe('Error deleting breeding record: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete breeding record', e.toString());
    }
  }

  /// Get a breeding record by ID
  Future<BreedingRecordIsar?> getBreedingRecordById(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Breeding record ID is required');
      }

      // Get all breeding records
      final allRecords =
          await _isar.collection<BreedingRecordIsar>().where().findAll();

      // Find the one with matching business ID
      try {
        return allRecords
            .firstWhere((record) => record.businessId == businessId);
      } catch (_) {
        // Not found
        return null;
      }
    } catch (e) {
      _logger.severe('Error getting breeding record by ID $businessId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve breeding record', e.toString());
    }
  }

  //=== PREGNANCY RECORDS ===//

  /// Get all pregnancy records
  Future<List<PregnancyRecordIsar>> getAllPregnancyRecords() async {
    try {
      return await _isar.pregnancyRecordIsars
          .where()
          .sortByStartDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting all pregnancy records: $e');
      throw DatabaseException(
          'Failed to retrieve pregnancy records', e.toString());
    }
  }

  /// Get pregnancy records for a specific cattle
  Future<List<PregnancyRecordIsar>> getPregnancyRecordsForCattle(
      String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      final isarService = GetIt.instance<IsarService>();
      // Get all pregnancy records
      final allRecords = await isarService.isar
          .collection<PregnancyRecordIsar>()
          .filter()
          .cattleIdEqualTo(cattleId)
          .findAll();

      return allRecords;
    } catch (e) {
      _logger
          .severe('Error getting pregnancy records for cattle $cattleId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve pregnancy records', e.toString());
    }
  }

  /// Get the next pregnancy sequence number for a cattle
  Future<int> getNextPregnancyNumber(String cattleId) async {
    try {
      final pregnancyRecords = await getPregnancyRecordsForCattle(cattleId);
      return pregnancyRecords.length + 1;
    } catch (e) {
      _logger.severe('Error getting next pregnancy number: $e');
      return 1;
    }
  }

  /// Create a breeding record from a pregnancy record
  Future<BreedingRecordIsar?> _createBreedingFromPregnancy(
      PregnancyRecordIsar pregnancyRecord) async {
    try {
      if (pregnancyRecord.cattleId == null || pregnancyRecord.cattleId!.isEmpty) {
        _logger.warning('Cannot create breeding record: Cattle ID is missing');
        return null;
      }

      // First check if this pregnancy already has a breeding record ID
      if (pregnancyRecord.breedingRecordId != null && pregnancyRecord.breedingRecordId!.isNotEmpty) {
        // Try to find the existing breeding record
        final existingRecord = await _isar.breedingRecordIsars
            .filter()
            .businessIdEqualTo(pregnancyRecord.breedingRecordId!)
            .findFirst();

        if (existingRecord != null) {
          _logger.info('Found existing breeding record ${existingRecord.businessId} linked to pregnancy ${pregnancyRecord.businessId}');
          return existingRecord;
        }
      }

      // Check if a breeding record already exists for this cattle with the same date
      final existingRecords = await _isar.breedingRecordIsars
          .filter()
          .cattleIdEqualTo(pregnancyRecord.cattleId!)
          .findAll();

      // Look for a breeding record with a date close to the pregnancy start date
      final startDate = pregnancyRecord.startDate ?? DateTime.now();
      for (final record in existingRecords) {
        if (record.date != null) {
          final difference = record.date!.difference(startDate).inDays.abs();
          // If we find a breeding record within 3 days of the pregnancy start date, use that
          if (difference <= 3) {
            _logger.info('Found existing breeding record ${record.businessId} with similar date for pregnancy ${pregnancyRecord.businessId}');
            return record;
          }
        }
      }

      // Get the next sequence number for the breeding record
      final sequenceNumber = await getNextBreedingNumber(pregnancyRecord.cattleId!);

      // Create a formatted ID for the breeding record
      final formattedId = BreedingRecordIsar.generateFormattedId(
          pregnancyRecord.cattleId!, sequenceNumber);

      // Create the breeding record
      final breedingRecord = BreedingRecordIsar.create(
        cattleId: pregnancyRecord.cattleId!,
        date: pregnancyRecord.startDate ?? DateTime.now(),
        status: 'Confirmed', // Set to confirmed since we're creating from a pregnancy record
        method: 'Unknown', // Default method
        bullIdOrType: 'Unknown', // Default bull/semen
        expectedDate: pregnancyRecord.expectedCalvingDate,
        notes: 'Created from pregnancy record: ${pregnancyRecord.businessId}',
        businessId: formattedId,
      );

      // Save the breeding record
      await _isar.writeTxn(() async {
        await _isar.breedingRecordIsars.put(breedingRecord);
      });

      _logger.info('Successfully created breeding record ${breedingRecord.businessId} from pregnancy record ${pregnancyRecord.businessId}');

      return breedingRecord;
    } catch (e) {
      _logger.warning('Error creating breeding record from pregnancy: $e');
      return null; // Return null instead of throwing to allow pregnancy record creation to continue
    }
  }

  /// Comprehensive method to add or update a pregnancy record and handle all related updates
  /// This centralizes the synchronization logic between pregnancy records, breeding records and cattle status
  Future<PregnancyRecordIsar> managePregnancyRecord(
      PregnancyRecordIsar record) async {
    try {
      await _validatePregnancyRecord(record);

      // Ensure the record has a properly formatted ID
      if (record.businessId == null ||
          !record.businessId!.contains('-Pregnancy-')) {
        // Get the next sequence number
        final sequenceNumber = await getNextPregnancyNumber(record.cattleId!);

        // Create a formatted ID
        final formattedId = PregnancyRecordIsar.generateFormattedId(
            record.cattleId!, sequenceNumber);

        // Update the record with the formatted ID
        record = record.copyWith(businessId: formattedId);
      }

      // Check if we need to create a breeding record
      BreedingRecordIsar? createdBreedingRecord;
      if (record.breedingRecordId == null) {
        // No breeding record is linked, create one
        createdBreedingRecord = await _createBreedingFromPregnancy(record);

        // Update the pregnancy record with the new breeding record ID
        if (createdBreedingRecord != null) {
          record = record.copyWith(breedingRecordId: createdBreedingRecord.businessId);
        }
      }

      await _isar.writeTxn(() async {
        // Save the pregnancy record
        await _isar.pregnancyRecordIsars.put(record);

        // Update breeding record if needed
        if (record.breedingRecordId != null) {
          await _updateLinkedBreedingRecord(record);
        }

        // Update cattle breeding status
        if (record.cattleId != null) {
          await _updateCattlePregnancyStatus(record);
        }
      });

      _logger
          .info('Successfully managed pregnancy record: ${record.businessId}');

      // Notify any listeners about the change
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyPregnancyChange({
        'action': record.businessId == null ? 'add' : 'update',
        'record': record.toMap(),
        'cattleId': record.cattleId,
      });

      // If we created a breeding record, notify about that too
      if (createdBreedingRecord != null) {
        _logger.info('Also notifying about created breeding record: ${createdBreedingRecord.businessId}');
        streamService.notifyBreedingChange({
          'action': 'add',
          'record': createdBreedingRecord.toMap(),
          'cattleId': createdBreedingRecord.cattleId,
          'id': createdBreedingRecord.businessId,
          'recordId': createdBreedingRecord.businessId,
        });
      }

      return record;
    } catch (e) {
      _logger.severe('Error managing pregnancy record: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to manage pregnancy record', e.toString());
    }
  }

  /// Update pregnancy status and handle all related updates
  /// This centralizes the status change logic across records
  Future<PregnancyRecordIsar> updatePregnancyStatus(
      PregnancyRecordIsar record, String newStatus) async {
    try {
      // Update the record status
      record.status = newStatus;
      record.updatedAt = DateTime.now();

      // If status is 'Completed', set actual calving date
      if (newStatus == 'Completed' && record.actualCalvingDate == null) {
        record.actualCalvingDate = DateTime.now();
      }

      // Use the comprehensive method to handle all updates
      return await managePregnancyRecord(record);
    } catch (e) {
      _logger.severe('Error updating pregnancy status: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to update pregnancy status', e.toString());
    }
  }

  /// Delete a pregnancy record with full cascading deletion
  Future<void> deletePregnancyRecord(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Pregnancy record ID is required');
      }

      // Get the pregnancy record
      final record = await _isar.pregnancyRecordIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();

      if (record == null) {
        throw RecordNotFoundException(
            'Pregnancy record not found: $businessId');
      }

      final cattleId = record.cattleId;
      final breedingRecordId = record.breedingRecordId;

      // Store IDs for notifications after transaction
      final List<String> deletedDeliveryIds = [];
      String? deletedBreedingRecordId;

      await _isar.writeTxn(() async {
        // 1. Delete associated delivery records first
        final deliveryRecords = await _isar.deliveryRecordIsars
            .filter()
            .pregnancyIdEqualTo(businessId)
            .findAll();

        for (final deliveryRecord in deliveryRecords) {
          await _isar.deliveryRecordIsars.delete(deliveryRecord.id);
          if (deliveryRecord.businessId != null) {
            deletedDeliveryIds.add(deliveryRecord.businessId!);
          }
          _logger.info('Cascade deleted delivery record: ${deliveryRecord.businessId}');
        }

        // 2. Delete the pregnancy record
        await _isar.pregnancyRecordIsars.delete(record.id);

        // 3. Delete the associated breeding record for full cascading deletion
        if (breedingRecordId != null && breedingRecordId.isNotEmpty) {
          final breedingRecord = await _isar.breedingRecordIsars
              .filter()
              .businessIdEqualTo(breedingRecordId)
              .findFirst();

          if (breedingRecord != null) {
            // Delete the breeding record
            await _isar.breedingRecordIsars.delete(breedingRecord.id);
            deletedBreedingRecordId = breedingRecordId;
            _logger.info('Cascade deleted breeding record: $breedingRecordId');
          }
        }

        // 4. Reset cattle pregnancy status and lastCalvingDate if needed
        if (cattleId != null) {
          await _resetCattlePregnancyStatus(cattleId);
          await _updateCattleLastCalvingDate(cattleId);
        }
      });

      // Notify listeners about all deletions after transaction is complete
      final streamService = GetIt.instance<StreamService>();

      // Notify about delivery record deletions
      for (final deliveryId in deletedDeliveryIds) {
        streamService.notifyDeliveryChange({
          'action': 'delete',
          'id': deliveryId,
          'recordId': deliveryId,
          'cattleId': cattleId,
        });
      }

      // Notify about pregnancy record deletion
      streamService.notifyPregnancyChange({
        'action': 'delete',
        'id': businessId, // Use 'id' to match what the view is expecting
        'recordId': businessId, // Keep recordId for backward compatibility
        'cattleId': cattleId,
      });

      // Notify about breeding record deletion if it was deleted
      if (deletedBreedingRecordId != null) {
        streamService.notifyBreedingChange({
          'action': 'delete',
          'id': deletedBreedingRecordId,
          'recordId': deletedBreedingRecordId,
          'cattleId': cattleId,
        });
      }

      _logger.info('Successfully deleted pregnancy record: $businessId');
    } catch (e) {
      _logger.severe('Error deleting pregnancy record: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException(
          'Failed to delete pregnancy record', e.toString());
    }
  }



  /// Create a pregnancy record from a breeding record
  Future<PregnancyRecordIsar> createPregnancyFromBreeding(
      String breedingRecordId) async {
    try {
      // Get the breeding record
      final breedingRecord = await _isar.breedingRecordIsars
          .filter()
          .businessIdEqualTo(breedingRecordId)
          .findFirst();

      if (breedingRecord == null) {
        throw RecordNotFoundException(
            'Breeding record not found: $breedingRecordId');
      }

      // Check if a pregnancy record already exists for this breeding
      final existingPregnancy = await _isar.pregnancyRecordIsars
          .filter()
          .breedingRecordIdEqualTo(breedingRecordId)
          .findFirst();

      if (existingPregnancy != null) {
        // Return the existing record instead of creating a duplicate
        return existingPregnancy;
      }

      // Get cattle details for gestation period
      final cattleId = breedingRecord.cattleId;
      if (cattleId == null) {
        throw ValidationException('Breeding record has no associated cattle');
      }

      // Get cattle to find animal type
      final allCattle = await _isar.collection<CattleIsar>().where().findAll();
      final cattle = allCattle.firstWhere(
        (c) => c.businessId == cattleId,
        orElse: () =>
            throw RecordNotFoundException('Cattle not found: $cattleId'),
      );

      // Get animal type for gestation period
      final animalTypeId = cattle.animalTypeId;
      final animalTypes = await GetIt.instance<IsarService>()
          .isar
          .collection<AnimalTypeIsar>()
          .where()
          .findAll();

      // Calculate gestation period
      int gestationDays = 283; // Default to cow gestation

      if (animalTypeId != null) {
        try {
          AnimalTypeIsar? animalType;
          try {
            animalType = animalTypes.firstWhere(
              (type) => type.businessId == animalTypeId,
            );
          } catch (_) {
            // Use default if not found
            animalType = null;
          }
          gestationDays = animalType?.defaultGestationDays ?? 283;
        } catch (_) {
          // Use default if not found
        }
      }

      // Calculate expected calving date
      final expectedCalvingDate =
          breedingRecord.date?.add(Duration(days: gestationDays));

      // Create new pregnancy record
      final pregnancyRecord = PregnancyRecordIsar.create(
        cattleId: cattleId,
        startDate: breedingRecord.date ?? DateTime.now(),
        status: breedingRecord.status == 'Completed' ? 'Completed' : 'Confirmed',
        expectedCalvingDate: expectedCalvingDate,
        breedingRecordId: breedingRecordId,
        notes: 'Created from breeding record: $breedingRecordId',
      );

      // Use our comprehensive method to handle all the updates
      return await managePregnancyRecord(pregnancyRecord);
    } catch (e) {
      _logger.severe(
          'Error creating pregnancy from breeding: $breedingRecordId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException(
          'Failed to create pregnancy record', e.toString());
    }
  }

  /// Synchronize all breeding and pregnancy records
  /// This ensures data consistency across the system
  Future<void> syncBreedingAndPregnancyRecords() async {
    try {
      // Get all breeding records with "Completed" status
      final completedBreedingRecords = await _isar.breedingRecordIsars
          .filter()
          .statusEqualTo('Completed')
          .findAll();

      // Get all pregnancy records
      final pregnancyRecords =
          await _isar.pregnancyRecordIsars.where().findAll();

      // Find breeding records without associated pregnancy records
      for (final breedingRecord in completedBreedingRecords) {
        final breedingId = breedingRecord.businessId;

        // Check if this breeding record has an associated pregnancy record
        final hasPregnancyRecord =
            pregnancyRecords.any((pr) => pr.breedingRecordId == breedingId);

        if (!hasPregnancyRecord && breedingId != null) {
          // Create a pregnancy record from this breeding record
          await createPregnancyFromBreeding(breedingId);
        }
      }

      _logger.info('Successfully synchronized breeding and pregnancy records');
    } catch (e) {
      _logger.severe('Error syncing breeding and pregnancy records: $e');
      throw DatabaseException(
          'Failed to sync breeding and pregnancy records', e.toString());
    }
  }

  /// Update a pregnancy record
  Future<void> updatePregnancyRecord(PregnancyRecordIsar record,
      {bool updateLinkedBreedingRecord = false}) async {
    try {
      await _validatePregnancyRecord(record);

      // First, get the existing record to ensure we have the correct Isar ID
      final existingRecord = await _isar.pregnancyRecordIsars
          .filter()
          .businessIdEqualTo(record.businessId!)
          .findFirst();

      if (existingRecord == null) {
        throw RecordNotFoundException(
            'Pregnancy record not found for update: ${record.businessId}');
      }

      // Copy the Isar ID from the existing record to ensure proper update
      record.id = existingRecord.id;
      record.updatedAt = DateTime.now();

      await _isar.writeTxn(() async {
        // Update the pregnancy record with the correct Isar ID
        await _isar.pregnancyRecordIsars.put(record);

        // Update related breeding record if needed
        if (updateLinkedBreedingRecord && record.breedingRecordId != null) {
          await _updateLinkedBreedingRecord(record);
        }

        // Update cattle breeding status
        if (record.cattleId != null) {
          await _updateCattlePregnancyStatus(record);
        }
      });

      // Notify any listeners about the pregnancy record change
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyPregnancyChange({
        'action': 'update',
        'record': record.toMap(),
        'cattleId': record.cattleId,
        'id': record.businessId,
        'recordId': record.businessId,
      });

      _logger
          .info('Successfully updated pregnancy record: ${record.businessId}');
    } catch (e) {
      _logger.severe('Error updating pregnancy record: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException(
          'Failed to update pregnancy record', e.toString());
    }
  }

  /// Get a pregnancy record by the breeding record ID
  Future<PregnancyRecordIsar?> getPregnancyRecordByBreedingId(
      String breedingRecordId) async {
    try {
      if (breedingRecordId.isEmpty) {
        throw ValidationException('Breeding record ID is required');
      }

      return await _isar.pregnancyRecordIsars
          .filter()
          .breedingRecordIdEqualTo(breedingRecordId)
          .findFirst();
    } catch (e) {
      _logger.severe(
          'Error getting pregnancy record by breeding ID $breedingRecordId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve pregnancy record', e.toString());
    }
  }

  /// Update a linked breeding record based on a pregnancy record
  Future<void> _updateLinkedBreedingRecord(
      PregnancyRecordIsar pregnancyRecord) async {
    if (pregnancyRecord.breedingRecordId == null) return;

    try {
      // Get the breeding record
      final breedingRecord = await _isar.breedingRecordIsars
          .filter()
          .businessIdEqualTo(pregnancyRecord.breedingRecordId!)
          .findFirst();

      if (breedingRecord == null) return;

      // Update status based on pregnancy status (case-insensitive)
      final status = pregnancyRecord.status?.toLowerCase() ?? '';
      if (status == 'confirmed') {
        breedingRecord.status = 'Confirmed';
      } else if (status == 'failed' || status == 'abortion') {
        breedingRecord.status = 'Failed';
      } else if (status == 'completed') {
        breedingRecord.status = 'Completed';
      }

      // Update expected date
      breedingRecord.expectedDate = pregnancyRecord.expectedCalvingDate;

      // Save the updated breeding record
      await _isar.breedingRecordIsars.put(breedingRecord);
    } catch (e) {
      _logger.warning('Error updating linked breeding record: $e');
      // Don't rethrow - we want the pregnancy record update to succeed even if this fails
    }
  }

  /// Update cattle pregnancy status based on a pregnancy record
  Future<void> _updateCattlePregnancyStatus(
      PregnancyRecordIsar pregnancyRecord) async {
    if (pregnancyRecord.cattleId == null) return;

    try {
      // Get all cattle
      final allCattle = await _isar.collection<CattleIsar>().where().findAll();

      // Find the cattle with matching business ID
      CattleIsar? cattle;
      try {
        cattle = allCattle
            .firstWhere((c) => c.businessId == pregnancyRecord.cattleId);
      } catch (_) {
        // No cattle found
        return;
      }

      // Update breeding status
      cattle.breedingStatus ??= BreedingStatus();

      // Update status based on pregnancy record status (case-insensitive)
      final status = pregnancyRecord.status?.toLowerCase() ?? '';
      if (status == 'confirmed') {
        cattle.breedingStatus!.isPregnant = true;
        cattle.breedingStatus!.status = 'Pregnant';
        cattle.breedingStatus!.expectedCalvingDate =
            pregnancyRecord.expectedCalvingDate;
      } else if (status == 'failed' || status == 'abortion') {
        cattle.breedingStatus!.isPregnant = false;
        cattle.breedingStatus!.status = 'Open';
        cattle.breedingStatus!.expectedCalvingDate = null;
      } else if (status == 'completed') {
        cattle.breedingStatus!.isPregnant = false;
        cattle.breedingStatus!.status = 'Fresh';
        cattle.breedingStatus!.lastCalvingDate = DateTime.now();
      } else if (status == 'pending') {
        cattle.breedingStatus!.isPregnant = false;
        cattle.breedingStatus!.status = 'Open';
        cattle.breedingStatus!.expectedCalvingDate = null;
      }

      // Save the updated cattle (using ! to assert non-null since we checked above)
      await _isar.writeTxn(() async {
        await _isar.collection<CattleIsar>().put(cattle!);
      });
    } catch (e) {
      _logger.warning('Error updating cattle pregnancy status: $e');
      // Don't rethrow - we want the pregnancy record update to succeed even if this fails
    }
  }

  /// Validate a pregnancy record
  Future<void> _validatePregnancyRecord(PregnancyRecordIsar record) async {
    if (record.cattleId == null || record.cattleId!.isEmpty) {
      throw ValidationException('Cattle ID is required');
    }
    if (record.startDate == null) {
      throw ValidationException('Start date is required');
    }
    if (record.status == null || record.status!.isEmpty) {
      throw ValidationException('Status is required');
    }
  }

  //=== PRIVATE HELPER METHODS ===//

  /// Validate breeding record
  Future<void> _validateBreedingRecord(BreedingRecordIsar record) async {
    if (record.cattleId == null || record.cattleId!.isEmpty) {
      throw ValidationException('Cattle ID is required');
    }

    if (record.date == null) {
      throw ValidationException('Breeding date is required');
    }

    if (record.method == null || record.method!.isEmpty) {
      throw ValidationException('Breeding method is required');
    }

    if (record.status == null || record.status!.isEmpty) {
      throw ValidationException('Breeding status is required');
    }
  }

  /// Update cattle breeding status based on the breeding record
  Future<void> _updateCattleBreedingStatus(BreedingRecordIsar record) async {
    if (record.cattleId == null || record.cattleId!.isEmpty) {
      _logger.warning('Cannot update breeding status: Cattle ID is missing');
      return;
    }

    try {
      final cattleId = record.cattleId!;

      // Get all cattle
      final allCattle = await _isar.collection<CattleIsar>().where().findAll();

      // Find the matching cattle
      CattleIsar? foundCattle;
      try {
        foundCattle = allCattle.firstWhere((c) => c.businessId == cattleId);
      } catch (_) {
        // No cattle found
        return;
      }

      // Create a local variable that we can safely use with non-null assertion
      final cattle = foundCattle;

      cattle.breedingStatus ??= BreedingStatus();

      // Update breeding status based on record status
      final status = record.status?.toLowerCase() ?? '';

      if (status == 'confirmed') {
        cattle.breedingStatus!.status = 'Pregnant';
        cattle.breedingStatus!.isPregnant = true;

        // Ensure we have an expected calving date
        if (record.expectedDate == null && record.date != null) {
          // Get animal type for this cattle to calculate expected calving date
          try {
            final animalTypes =
                await _isar.collection<AnimalTypeIsar>().where().findAll();
            AnimalTypeIsar? animalType;
            try {
              animalType = animalTypes.firstWhere(
                (type) => type.businessId == cattle.animalTypeId,
              );
            } catch (_) {
              animalType = null;
            }

            // Calculate expected calving date using the animal's gestation period
            final gestationDays = animalType?.defaultGestationDays ??
                283; // Default to cow gestation
            record.expectedDate =
                record.date!.add(Duration(days: gestationDays));

            // Update the breeding record with the calculated expected date
            await _isar.breedingRecordIsars.put(record);
          } catch (e) {
            _logger.warning('Error calculating expected calving date: $e');
          }
        }
      } else if (status == 'completed') {
        cattle.breedingStatus!.status = 'Open';
        cattle.breedingStatus!.isPregnant = false;
      } else {
        cattle.breedingStatus!.status = record.status ?? 'Open';
        cattle.breedingStatus!.isPregnant = false;
      }

      cattle.breedingStatus!.breedingDate = record.date;
      cattle.breedingStatus!.expectedCalvingDate = record.expectedDate;
      cattle.breedingStatus!.lastBreedingMethod = record.method;
      cattle.updatedAt = DateTime.now();

      // Update the cattle record
      await _isar.writeTxn(() async {
        await _isar.collection<CattleIsar>().put(cattle);
      });

      // Notify listeners about the cattle update
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyCattleChange({
        'action': 'update',
        'record': cattle.toMap(),
        'cattleId': cattle.businessId,
      });

      _logger.info(
          'Updated cattle breeding status for ${record.cattleId} to ${cattle.breedingStatus!.status}');
    } catch (e) {
      _logger.severe(
          'Error updating breeding status for cattle ${record.cattleId}: $e');
      // Don't rethrow - we want the breeding record update to succeed even if this fails
    }
  }

  /// Reset breeding status for a cattle after deleting a breeding record
  /// This method should be called within an existing transaction
  Future<void> _resetCattleBreedingStatus(String cattleId) async {
    try {
      // Get all cattle
      final isarService = GetIt.instance<IsarService>();
      final allCattle =
          await isarService.isar.collection<CattleIsar>().where().findAll();

      // Find the matching cattle
      CattleIsar? cattle;
      try {
        cattle = allCattle.firstWhere((c) => c.businessId == cattleId);
      } catch (_) {
        // No cattle found
        return;
      }

      // Reset breeding status to open
      cattle.breedingStatus ??= BreedingStatus();
      cattle.breedingStatus!.status = 'Open';
      cattle.breedingStatus!.isPregnant = false;
      cattle.updatedAt = DateTime.now();

      // Save the updated cattle (this should be called within an existing transaction)
      await isarService.cattleIsars.put(cattle);
    } catch (e) {
      _logger.warning('Error resetting cattle breeding status: $e');
      // Don't rethrow - we want the breeding record delete to succeed even if this fails
    }
  }

  /// Update cattle's lastCalvingDate based on remaining delivery records
  /// This method should be called within an existing transaction
  Future<void> _updateCattleLastCalvingDate(String cattleId) async {
    try {
      // Get all remaining delivery records for this cattle
      final deliveryRecords = await getDeliveryRecordsForCattle(cattleId);

      // Get all cattle
      final allCattle = await _isar.collection<CattleIsar>().where().findAll();

      // Find the matching cattle by tagId (not businessId)
      CattleIsar? cattle;
      try {
        cattle = allCattle.firstWhere((c) => c.tagId == cattleId);
      } catch (_) {
        _logger.warning('No cattle found with tag ID: $cattleId');
        return;
      }

      // Create a copy of the cattle to modify
      final updatedCattle = cattle.copyWith();

      // Initialize breeding status if it doesn't exist
      updatedCattle.breedingStatus ??= BreedingStatus();

      // Find the most recent delivery date
      DateTime? mostRecentDeliveryDate;
      if (deliveryRecords.isNotEmpty) {
        // Sort delivery records by date, most recent first
        deliveryRecords.sort((a, b) {
          final dateA = DateTime.parse(a['deliveryDate']);
          final dateB = DateTime.parse(b['deliveryDate']);
          return dateB.compareTo(dateA);
        });

        // Get the most recent delivery date
        mostRecentDeliveryDate = DateTime.parse(deliveryRecords.first['deliveryDate']);
        _logger.info('Found ${deliveryRecords.length} delivery records for cattle $cattleId, most recent: $mostRecentDeliveryDate');
      } else {
        // No delivery records found - clear the lastCalvingDate to make cattle eligible for breeding
        mostRecentDeliveryDate = null;
        _logger.info('No delivery records found for cattle $cattleId, clearing lastCalvingDate to make cattle eligible for breeding');
      }

      // Update the lastCalvingDate
      updatedCattle.breedingStatus!.lastCalvingDate = mostRecentDeliveryDate;

      // Save the updated cattle (this should be called within an existing transaction)
      await _isar.cattleIsars.put(updatedCattle);

      _logger.info('Updated cattle $cattleId lastCalvingDate to: $mostRecentDeliveryDate');
    } catch (e) {
      _logger.severe('Error updating cattle lastCalvingDate for $cattleId: $e');
    }
  }

  /// Reset cattle pregnancy status
  /// This method should be called within an existing transaction
  Future<void> _resetCattlePregnancyStatus(String cattleId) async {
    try {
      final records = await getPregnancyRecordsForCattle(cattleId);

      // Get all cattle
      final allCattle = await _isar.collection<CattleIsar>().where().findAll();

      // Find the matching cattle by tagId (not businessId)
      CattleIsar? cattle;
      try {
        cattle = allCattle.firstWhere((c) => c.tagId == cattleId);
      } catch (_) {
        _logger.warning('No cattle found with tag ID: $cattleId');
        return;
      }

      // Create a copy of the cattle to modify
      // We've already checked that cattle is not null above, so it's safe to use the ! operator
      final updatedCattle = cattle.copyWith();

      if (records.isEmpty) {
        // No pregnancy records, reset to not pregnant
        updatedCattle.breedingStatus ??= BreedingStatus();
        updatedCattle.breedingStatus!.isPregnant = false;
        updatedCattle.breedingStatus!.status = 'Open';
        updatedCattle.breedingStatus!.expectedCalvingDate = null;
        updatedCattle.updatedAt = DateTime.now();

        _logger.info('Resetting cattle $cattleId to not pregnant');
      } else {
        // Find the most recent confirmed pregnancy
        final confirmedPregnancies = records
            .where((pr) => pr.status?.toLowerCase() == 'confirmed')
            .toList();

        if (confirmedPregnancies.isEmpty) {
          // No confirmed pregnancies, reset to not pregnant
          updatedCattle.breedingStatus ??= BreedingStatus();
          updatedCattle.breedingStatus!.isPregnant = false;
          updatedCattle.breedingStatus!.status = 'Open';
          updatedCattle.breedingStatus!.expectedCalvingDate = null;
          _logger.info(
              'No confirmed pregnancies found, resetting cattle $cattleId to not pregnant');
        } else {
          // Update with most recent confirmed pregnancy
          confirmedPregnancies.sort((a, b) => (b.startDate ?? DateTime.now())
              .compareTo(a.startDate ?? DateTime.now()));

          final mostRecentPregnancy = confirmedPregnancies.first;
          updatedCattle.breedingStatus ??= BreedingStatus();
          updatedCattle.breedingStatus!.isPregnant = true;
          updatedCattle.breedingStatus!.status = 'Pregnant';
          updatedCattle.breedingStatus!.expectedCalvingDate =
              mostRecentPregnancy.expectedCalvingDate;
          _logger.info('Updated cattle $cattleId with most recent pregnancy');
        }
      }

      // Save the updated cattle (this should be called within an existing transaction)
      await _isar.collection<CattleIsar>().put(updatedCattle);

      // Notify listeners about the cattle update
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyCattleChange({
        'action': 'update',
        'record': updatedCattle.toMap(),
      });
    } catch (e) {
      _logger.severe('Error resetting cattle pregnancy status: $e');
      // Don't rethrow - we want the pregnancy record delete to succeed even if this fails
    }
  }

  /// Update related pregnancy record
  Future<void> _updateRelatedPregnancyRecord(BreedingRecordIsar record) async {
    // Get all pregnancy records
    final allPregnancyRecords =
        await _isar.collection<PregnancyRecordIsar>().where().findAll();

    // Find matching pregnancy records
    PregnancyRecordIsar? pregnancyRecord;
    try {
      pregnancyRecord = allPregnancyRecords
          .firstWhere((pr) => pr.breedingRecordId == record.businessId);
    } catch (_) {
      // No pregnancy record found
      // Create one if needed for confirmed or completed pregnancies
      if (record.status == 'Confirmed' || record.status == 'Completed') {
        final newPregnancyRecord = PregnancyRecordIsar.create(
          cattleId: record.cattleId!,
          startDate: record.date!,
          status: record.status == 'Completed' ? 'Completed' : 'Confirmed',
          breedingRecordId: record.businessId,
          expectedCalvingDate: record.expectedDate,
          actualCalvingDate:
              record.status == 'Completed' ? DateTime.now() : null,
          notes: 'Created from updated breeding record: ${record.businessId}',
        );

        await _isar.writeTxn(() async {
          await _isar.collection<PregnancyRecordIsar>().put(newPregnancyRecord);
        });
      }
      return;
    }

    // Update existing pregnancy record
    // We've already checked that pregnancyRecord is not null
    pregnancyRecord.expectedCalvingDate = record.expectedDate;
    pregnancyRecord.updatedAt = DateTime.now();

    if (record.status == 'Completed') {
      pregnancyRecord.status = 'Completed';
      pregnancyRecord.actualCalvingDate = DateTime.now();
    } else if (record.status == 'Failed') {
      pregnancyRecord.status = 'Failed';
    }

    await _isar.writeTxn(() async {
      if (pregnancyRecord != null) {
        // This check is technically redundant but required by type system
        await _isar.collection<PregnancyRecordIsar>().put(pregnancyRecord);
      }
    });
  }

  /// Create breeding events
  Future<void> _createBreedingEvents(BreedingRecordIsar record) async {
    // Create breeding event
    final breedingEvent = BreedingEventIsar.create(
      cattleId: record.cattleId!,
      cattleName: await _getCattleName(record.cattleId!),
      eventType: 'Breeding',
      date: record.date!,
      status: record.status,
      details: 'Method: ${record.method}',
      relatedRecordId: record.businessId,
    );

    await _isar.breedingEventIsars.put(breedingEvent);

    // Create calving event if expected date is set
    if (record.expectedDate != null) {
      final calvingEvent = BreedingEventIsar.create(
        cattleId: record.cattleId!,
        cattleName: await _getCattleName(record.cattleId!),
        eventType: 'Expected Calving',
        date: record.expectedDate!,
        status: 'Pending',
        details:
            'Expected calving from breeding on ${record.date?.toIso8601String()}',
        relatedRecordId: record.businessId,
      );

      await _isar.breedingEventIsars.put(calvingEvent);
    }
  }

  /// Update breeding events
  Future<void> _updateBreedingEvents(BreedingRecordIsar record) async {
    // Delete existing events
    final existingEvents = await _isar.breedingEventIsars
        .filter()
        .relatedRecordIdEqualTo(record.businessId)
        .findAll();

    for (final event in existingEvents) {
      await _isar.breedingEventIsars.delete(event.id);
    }

    // Create new events
    await _createBreedingEvents(record);
  }

  /// Get cattle name
  Future<String> _getCattleName(String cattleId) async {
    try {
      // Get all cattle
      final allCattle = await _isar.collection<CattleIsar>().where().findAll();

      // Find the matching cattle
      CattleIsar? cattle;
      try {
        cattle = allCattle.firstWhere((c) => c.businessId == cattleId);
      } catch (_) {
        return 'Unknown';
      }

      // We know cattle is not null here
      return cattle.name ?? 'Unknown';
    } catch (e) {
      _logger.warning('Error getting cattle name: $e');
      return 'Unknown';
    }
  }

  /// Update cattle status after pregnancy check
  Future<bool> updateCattlePregnancyStatus(String cattleId, bool isPregnant,
      {DateTime? expectedDueDate}) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      await _isar.writeTxn(() async {
        // Get all cattle
        final allCattle =
            await _isar.collection<CattleIsar>().where().findAll();

        // Find the matching cattle
        CattleIsar? cattle;
        try {
          cattle = allCattle.firstWhere((c) => c.businessId == cattleId);
        } catch (_) {
          throw RecordNotFoundException('Cattle not found: $cattleId');
        }

        cattle.breedingStatus ??= BreedingStatus();
        cattle.breedingStatus!.isPregnant = isPregnant;
        cattle.breedingStatus!.status = isPregnant ? 'Pregnant' : 'Open';

        if (isPregnant && expectedDueDate != null) {
          cattle.breedingStatus!.expectedCalvingDate = expectedDueDate;
        }

        cattle.updatedAt = DateTime.now();

        // Use non-null assertion since we know cattle is not null at this point
        await _isar.collection<CattleIsar>().put(cattle);
      });

      return true;
    } catch (e) {
      _logger.severe('Error updating cattle pregnancy status: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseOperationException(
          'Failed to update cattle pregnancy status', e.toString());
    }
  }

  /// Update or create a breeding status for a cattle
  Future<void> updateBreedingStatus(
      String cattleId, BreedingStatus status) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      await _isar.writeTxn(() async {
        // Get all cattle
        final allCattle =
            await _isar.collection<CattleIsar>().where().findAll();

        // Find the matching cattle
        CattleIsar? cattle;
        try {
          cattle = allCattle.firstWhere((c) => c.businessId == cattleId);
        } catch (_) {
          throw RecordNotFoundException('Cattle not found: $cattleId');
        }

        cattle.breedingStatus = status;
        cattle.updatedAt = DateTime.now();

        await _isar.collection<CattleIsar>().put(cattle);
      });

      _logger
          .info('Successfully updated breeding status for cattle: $cattleId');
    } catch (e) {
      _logger.severe('Error updating breeding status for cattle $cattleId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to update breeding status', e.toString());
    }
  }

  /// Get the current breeding status for a cattle
  Future<BreedingStatus?> getBreedingStatus(String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      // Get all cattle
      final allCattle = await _isar.collection<CattleIsar>().where().findAll();

      // Find the matching cattle
      final cattle = allCattle.firstWhere(
        (c) => c.businessId == cattleId,
        orElse: () =>
            throw RecordNotFoundException('Cattle not found: $cattleId'),
      );

      return cattle.breedingStatus;
    } catch (e) {
      _logger.severe('Error getting breeding status for cattle $cattleId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to get breeding status', e.toString());
    }
  }

  /// Gets all pregnant cattle
  Future<List<CattleIsar>> getPregnantCattle() async {
    try {
      final isar = _isarService.isar;
      // Get all cattle
      final allCattle = await isar.collection<CattleIsar>().where().findAll();

      // Filter for pregnant ones
      return allCattle
          .where((cattle) => cattle.breedingStatus?.isPregnant == true)
          .toList();
    } catch (e) {
      _logger.severe('Error getting pregnant cattle: $e');
      throw DatabaseOperationException(
          'Failed to get pregnant cattle', e.toString());
    }
  }

  /// Gets all cattle due for calving within the next n days
  Future<List<CattleIsar>> getCattleDueForCalving(int daysThreshold) async {
    try {
      final isar = _isarService.isar;
      final now = DateTime.now();
      final thresholdDate = now.add(Duration(days: daysThreshold));

      // Get all cattle
      final allCattle = await isar.collection<CattleIsar>().where().findAll();

      // Filter for pregnant ones with expected calving date in the threshold
      return allCattle.where((cattle) {
        final status = cattle.breedingStatus;
        if (status == null || !status.isPregnant) return false;

        final calvingDate = status.expectedCalvingDate;
        if (calvingDate == null) return false;

        return calvingDate.isAfter(now) && calvingDate.isBefore(thresholdDate);
      }).toList();
    } catch (e) {
      _logger.severe('Error getting cattle due for calving: $e');
      throw DatabaseOperationException(
          'Failed to get cattle due for calving', e.toString());
    }
  }

  /// Get all cattle needing breeding
  Future<List<CattleIsar>> getCattleNeedingBreeding() async {
    try {
      // Get all cattle
      final allCattle = await _isar.collection<CattleIsar>().where().findAll();

      // Filter for female cattle that are not pregnant
      return allCattle
          .where((cattle) =>
              cattle.gender == 'Female' &&
              (cattle.breedingStatus == null ||
                  cattle.breedingStatus!.isPregnant != true))
          .toList();
    } catch (e) {
      _logger.severe('Error getting cattle needing breeding: $e');
      throw DatabaseOperationException(
          'Failed to get cattle needing breeding', e.toString());
    }
  }

  /// Get cattle info for breeding
  Future<CattleIsar?> getCattleInfo(String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      // Get all cattle
      final allCattle = await _isar.collection<CattleIsar>().where().findAll();

      // Find the one with matching ID
      try {
        return allCattle.firstWhere((c) => c.businessId == cattleId);
      } catch (_) {
        // Not found
        return null;
      }
    } catch (e) {
      _logger.severe('Error getting cattle info: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to get cattle info', e.toString());
    }
  }

  //=== BREEDING EVENTS ===//

  /// Get all breeding events
  Future<List<BreedingEventIsar>> getAllBreedingEvents() async {
    try {
      return await _isar.breedingEventIsars.where().sortByDateDesc().findAll();
    } catch (e) {
      _logger.severe('Error getting all breeding events: $e');
      throw DatabaseException(
          'Failed to retrieve breeding events', e.toString());
    }
  }

  /// Get all delivery records
  Future<List<Map<String, dynamic>>> getAllDeliveryRecords() async {
    try {
      final isarService = GetIt.instance<IsarService>();
      final records = await isarService.isar
          .collection<DeliveryRecordIsar>()
          .where()
          .sortByDeliveryDateDesc()
          .findAll();

      // Convert DeliveryRecordIsar objects to Map<String, dynamic>
      return records.map((record) => record.toMap()).toList();
    } catch (e) {
      _logger.severe('Error getting all delivery records: $e');
      throw DatabaseException(
          'Failed to retrieve delivery records', e.toString());
    }
  }

  /// Get all delivery records for a specific cattle
  Future<List<Map<String, dynamic>>> getDeliveryRecordsForCattle(
      String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      final isarService = GetIt.instance<IsarService>();
      final records = await isarService.isar
          .collection<DeliveryRecordIsar>()
          .filter()
          .cattleIdEqualTo(cattleId)
          .sortByDeliveryDateDesc()
          .findAll();

      // Convert DeliveryRecordIsar objects to Map<String, dynamic>
      return records.map((record) => record.toMap()).toList();
    } catch (e) {
      _logger.severe('Error getting delivery records for cattle $cattleId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve delivery records', e.toString());
    }
  }

  /// Add a pregnancy record from a map
  Future<PregnancyRecordIsar> addPregnancyRecordFromMap(
      Map<String, dynamic> map) async {
    try {
      // Validate required fields
      if (map['cattleId'] == null || (map['cattleId'] as String).isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      // Parse dates
      DateTime? startDate;
      if (map['startDate'] != null) {
        if (map['startDate'] is String) {
          startDate = DateTime.parse(map['startDate']);
        } else if (map['startDate'] is DateTime) {
          startDate = map['startDate'];
        }
      }

      DateTime? expectedCalvingDate;
      if (map['expectedCalvingDate'] != null) {
        if (map['expectedCalvingDate'] is String) {
          expectedCalvingDate = DateTime.parse(map['expectedCalvingDate']);
        } else if (map['expectedCalvingDate'] is DateTime) {
          expectedCalvingDate = map['expectedCalvingDate'];
        }
      }

      // Create the pregnancy record
      final record = PregnancyRecordIsar.create(
        cattleId: map['cattleId'],
        startDate: startDate ?? DateTime.now(),
        status: map['status'] ?? 'Confirmed',
        breedingRecordId: map['breedingRecordId'],
        expectedCalvingDate: expectedCalvingDate,
        notes: map['notes'],
      );

      // Save the record
      await _isar.writeTxn(() async {
        await _isar.pregnancyRecordIsars.put(record);
      });

      return record;
    } catch (e) {
      _logger.severe('Error adding pregnancy record from map: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to add pregnancy record', e.toString());
    }
  }

  /// Get the next delivery sequence number for a cattle
  Future<int> getNextDeliveryNumber(String cattleId) async {
    try {
      final deliveryRecords = await getDeliveryRecordsForCattle(cattleId);
      return deliveryRecords.length + 1;
    } catch (e) {
      _logger.severe('Error getting next delivery number: $e');
      return 1;
    }
  }

  /// Add a delivery record from a map
  Future<void> addDeliveryRecordFromMap(Map<String, dynamic> map) async {
    try {
      // Validate required fields
      if (map['motherTagId'] == null ||
          (map['motherTagId'] as String).isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      final cattleId = map['motherTagId'] as String;
      final deliveryDate = map['deliveryDate'] != null
          ? DateTime.parse(map['deliveryDate'] as String)
          : DateTime.now();
      final deliveryType = map['deliveryType'] as String? ?? 'Normal';

      // Get the next sequence number for this cattle
      final sequenceNumber = await getNextDeliveryNumber(cattleId);

      // Create a formatted ID
      final formattedId =
          DeliveryRecordIsar.generateFormattedId(cattleId, sequenceNumber);

      // Create the delivery record
      final deliveryRecord = DeliveryRecordIsar.create(
        cattleId: cattleId,
        deliveryDate: deliveryDate,
        deliveryType: deliveryType,
        pregnancyId: map['pregnancyId'] as String?,
        calfCount: map['numberOfCalves'] as int? ?? 1,
        notes: map['notes'] as String?,
        calfIds: map['calfDetails'] != null
            ? (map['calfDetails'] as List).map((c) => c['tagId']).join(',')
            : null,
        calfDetails: map['calfDetails'] != null
            ? List<Map<String, dynamic>>.from(map['calfDetails'] as List)
            : null,
        businessId: formattedId,
      );

      // Save the record
      await _isar.writeTxn(() async {
        await _isar.deliveryRecordIsars.put(deliveryRecord);
      });

      _logger.info('Successfully added delivery record: $formattedId');
    } catch (e) {
      _logger.severe('Error adding delivery record from map: $e');
      throw DatabaseException('Failed to add delivery record', e.toString());
    }
  }

  /// Update a delivery record from a map
  Future<void> updateDeliveryRecordFromMap(Map<String, dynamic> map) async {
    try {
      // Validate required fields
      if (map['id'] == null || (map['id'] as String).isEmpty) {
        throw ValidationException('Delivery record ID is required');
      }

      final businessId = map['id'] as String;

      // Find the existing record
      final existingRecord = await _isar.deliveryRecordIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();

      if (existingRecord == null) {
        throw RecordNotFoundException('Delivery record not found: $businessId');
      }

      // Update the record fields
      final deliveryDate = map['deliveryDate'] != null
          ? DateTime.parse(map['deliveryDate'] as String)
          : existingRecord.deliveryDate;
      final deliveryType =
          map['deliveryType'] as String? ?? existingRecord.deliveryType;

      // Preserve the existing ID format
      existingRecord.deliveryDate = deliveryDate;
      existingRecord.deliveryType = deliveryType;
      existingRecord.calfCount =
          map['numberOfCalves'] as int? ?? existingRecord.calfCount;
      existingRecord.notes = map['notes'] as String? ?? existingRecord.notes;
      existingRecord.updatedAt = DateTime.now();

      if (map['calfDetails'] != null) {
        existingRecord.calfIds =
            (map['calfDetails'] as List).map((c) => c['tagId']).join(',');
        existingRecord.calfDetailsJson = jsonEncode(map['calfDetails']);
      }

      // Save the updated record
      await _isar.writeTxn(() async {
        await _isar.deliveryRecordIsars.put(existingRecord);
      });

      _logger.info('Successfully updated delivery record: $businessId');
    } catch (e) {
      _logger.severe('Error updating delivery record from map: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to update delivery record', e.toString());
    }
  }

  /// Update a delivery record by ID and map (for delivery records screen)
  Future<void> updateDeliveryRecord(String businessId, Map<String, dynamic> map) async {
    try {
      // Add the ID to the map and use the existing method
      final updatedMap = Map<String, dynamic>.from(map);
      updatedMap['id'] = businessId;

      await updateDeliveryRecordFromMap(updatedMap);
    } catch (e) {
      _logger.severe('Error updating delivery record: $e');
      throw DatabaseException('Failed to update delivery record', e.toString());
    }
  }

  /// Delete a delivery record with full cascading deletion
  Future<void> deleteDeliveryRecord(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Delivery record ID is required');
      }

      // Get the delivery record
      final record = await _isar.deliveryRecordIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();

      if (record == null) {
        throw RecordNotFoundException(
            'Delivery record not found: $businessId');
      }

      final cattleId = record.cattleId;
      final pregnancyId = record.pregnancyId;

      // Store IDs for notifications after transaction
      String? deletedPregnancyId;
      String? deletedBreedingRecordId;

      await _isar.writeTxn(() async {
        // 1. Delete the delivery record
        await _isar.deliveryRecordIsars.delete(record.id);
        _logger.info('Deleted delivery record: $businessId');

        // 2. If there's a linked pregnancy record, delete it (which will cascade to breeding record)
        if (pregnancyId != null && pregnancyId.isNotEmpty) {
          _logger.info('Found linked pregnancy record: $pregnancyId, proceeding with cascade deletion');

          // Get the pregnancy record
          final pregnancyRecord = await _isar.pregnancyRecordIsars
              .filter()
              .businessIdEqualTo(pregnancyId)
              .findFirst();

          if (pregnancyRecord != null) {
            // Get the breeding record ID before deleting the pregnancy record
            final breedingRecordId = pregnancyRecord.breedingRecordId;
            deletedPregnancyId = pregnancyId;

            // Delete the pregnancy record
            await _isar.pregnancyRecordIsars.delete(pregnancyRecord.id);

            _logger.info('Cascade deleted pregnancy record: $pregnancyId');

            // 3. If there's a linked breeding record, delete it
            if (breedingRecordId != null && breedingRecordId.isNotEmpty) {
              _logger.info('Found linked breeding record: $breedingRecordId, proceeding with deletion');

              final breedingRecord = await _isar.breedingRecordIsars
                  .filter()
                  .businessIdEqualTo(breedingRecordId)
                  .findFirst();

              if (breedingRecord != null) {
                // Delete the breeding record
                await _isar.breedingRecordIsars.delete(breedingRecord.id);
                deletedBreedingRecordId = breedingRecordId;

                _logger.info('Cascade deleted breeding record: $breedingRecordId');
              } else {
                _logger.warning('Breeding record not found: $breedingRecordId');
              }
            } else {
              _logger.info('No linked breeding record found for pregnancy: $pregnancyId');
            }
          } else {
            _logger.warning('Pregnancy record not found: $pregnancyId');
          }
        } else {
          _logger.info('No linked pregnancy record found for delivery: $businessId');
        }

        // 4. Reset cattle breeding status and lastCalvingDate if needed
        if (cattleId != null) {
          await _resetCattleBreedingStatus(cattleId);
          await _updateCattleLastCalvingDate(cattleId);
        }
      });

      // Notify listeners about all deletions after transaction is complete
      final streamService = GetIt.instance<StreamService>();

      // Notify about delivery record deletion
      streamService.notifyDeliveryChange({
        'action': 'delete',
        'id': businessId,
        'recordId': businessId,
        'cattleId': cattleId,
      });

      // Notify about pregnancy record deletion if it was deleted
      if (deletedPregnancyId != null) {
        streamService.notifyPregnancyChange({
          'action': 'delete',
          'id': deletedPregnancyId,
          'recordId': deletedPregnancyId,
          'cattleId': cattleId,
        });
      }

      // Notify about breeding record deletion if it was deleted
      if (deletedBreedingRecordId != null) {
        streamService.notifyBreedingChange({
          'action': 'delete',
          'id': deletedBreedingRecordId,
          'recordId': deletedBreedingRecordId,
          'cattleId': cattleId,
        });
      }

      _logger.info('Successfully deleted delivery record: $businessId');
    } catch (e) {
      _logger.severe('Error deleting delivery record: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete delivery record', e.toString());
    }
  }

  /// Update a pregnancy record from a map
  Future<void> updatePregnancyRecordFromMap(Map<String, dynamic> map,
      {bool updateLinkedBreedingRecord = false}) async {
    try {
      // Convert the map to a PregnancyRecordIsar object
      final record = _convertMapToPregnancyRecord(map);

      // Update the record
      await updatePregnancyRecord(record,
          updateLinkedBreedingRecord: updateLinkedBreedingRecord);
    } catch (e) {
      _logger.severe('Error updating pregnancy record from map: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to update pregnancy record from map', e.toString());
    }
  }

  /// Convert a map to a PregnancyRecordIsar object
  PregnancyRecordIsar _convertMapToPregnancyRecord(Map<String, dynamic> map) {
    try {
      // Validate required fields
      if (map['cattleId'] == null || (map['cattleId'] as String).isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      // Parse dates
      DateTime? startDate;
      if (map['startDate'] != null) {
        startDate = map['startDate'] is String
            ? DateTime.parse(map['startDate'])
            : map['startDate'] as DateTime;
      }

      DateTime? expectedCalvingDate;
      if (map['expectedCalvingDate'] != null) {
        expectedCalvingDate = map['expectedCalvingDate'] is String
            ? DateTime.parse(map['expectedCalvingDate'])
            : map['expectedCalvingDate'] as DateTime;
      }

      DateTime? endDate;
      if (map['endDate'] != null) {
        endDate = map['endDate'] is String
            ? DateTime.parse(map['endDate'])
            : map['endDate'] as DateTime;
      }

      DateTime? createdAt;
      if (map['createdAt'] != null) {
        createdAt = map['createdAt'] is String
            ? DateTime.parse(map['createdAt'])
            : map['createdAt'] as DateTime;
      } else {
        createdAt = DateTime.now();
      }

      DateTime? updatedAt;
      if (map['updatedAt'] != null) {
        updatedAt = map['updatedAt'] is String
            ? DateTime.parse(map['updatedAt'])
            : map['updatedAt'] as DateTime;
      } else {
        updatedAt = DateTime.now();
      }

      // Create a new PregnancyRecordIsar object or find existing one
      PregnancyRecordIsar record;
      if (map['id'] != null) {
        record = PregnancyRecordIsar()..businessId = map['id'] as String?;
      } else {
        record = PregnancyRecordIsar();
      }

      // Set all properties
      record.cattleId = map['cattleId'] as String?;
      record.breedingRecordId = map['breedingRecordId'] as String?;
      record.startDate = startDate;
      record.expectedCalvingDate = expectedCalvingDate;
      record.endDate = endDate;
      record.status = map['status'] as String?;
      record.notes = map['notes'] as String?;
      record.createdAt = createdAt;
      record.updatedAt = updatedAt;

      return record;
    } catch (e) {
      _logger.severe('Error converting map to pregnancy record: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to convert map to pregnancy record', e.toString());
    }
  }

  /// Update a breeding record from a map
  Future<void> updateBreedingRecordFromMap(Map<String, dynamic> map,
      {bool updateLinkedPregnancyRecord = false}) async {
    try {
      // Convert the map to a BreedingRecordIsar object
      final record = _convertMapToBreedingRecord(map);

      // Update the record
      await updateBreedingRecord(record,
          updatePregnancyRecord: updateLinkedPregnancyRecord);
    } catch (e) {
      _logger.severe('Error updating breeding record from map: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to update breeding record from map', e.toString());
    }
  }

  /// Convert a map to a BreedingRecordIsar object
  BreedingRecordIsar _convertMapToBreedingRecord(Map<String, dynamic> map) {
    try {
      // Validate required fields
      if (map['cattleId'] == null || (map['cattleId'] as String).isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      // Parse dates
      DateTime? date;
      if (map['date'] != null) {
        date = map['date'] is String
            ? DateTime.parse(map['date'])
            : map['date'] as DateTime;
      }

      DateTime? expectedDate;
      if (map['expectedDate'] != null) {
        expectedDate = map['expectedDate'] is String
            ? DateTime.parse(map['expectedDate'])
            : map['expectedDate'] as DateTime;
      }

      DateTime? createdAt;
      if (map['createdAt'] != null) {
        createdAt = map['createdAt'] is String
            ? DateTime.parse(map['createdAt'])
            : map['createdAt'] as DateTime;
      } else {
        createdAt = DateTime.now();
      }

      DateTime? updatedAt;
      if (map['updatedAt'] != null) {
        updatedAt = map['updatedAt'] is String
            ? DateTime.parse(map['updatedAt'])
            : map['updatedAt'] as DateTime;
      } else {
        updatedAt = DateTime.now();
      }

      // Parse cost
      double? cost;
      if (map['cost'] != null) {
        cost = map['cost'] is num
            ? (map['cost'] as num).toDouble()
            : double.tryParse(map['cost'].toString());
      }

      // Create a new BreedingRecordIsar object or find existing one
      BreedingRecordIsar record;
      if (map['id'] != null) {
        record = BreedingRecordIsar()..businessId = map['id'] as String?;
      } else {
        record = BreedingRecordIsar();
      }

      // Set all properties
      record.cattleId = map['cattleId'] as String?;
      record.date = date;
      record.bullIdOrType = map['bullIdOrType'] as String?;
      record.method = map['method'] as String?;
      record.status = map['status'] as String?;
      record.expectedDate = expectedDate;
      record.cost = cost;
      record.notes = map['notes'] as String?;
      record.createdAt = createdAt;
      record.updatedAt = updatedAt;

      return record;
    } catch (e) {
      _logger.severe('Error converting map to breeding record: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to convert map to breeding record', e.toString());
    }
  }

  /// Get all upcoming calving events
  Future<List<Map<String, dynamic>>> getUpcomingCalvingEvents(
      int daysAhead) async {
    try {
      final now = DateTime.now();
      final cutoff = now.add(Duration(days: daysAhead));

      // Get all cattle
      final allCattle = await _isar.collection<CattleIsar>().where().findAll();

      // Filter for pregnant cattle with expected calving dates within the range
      final cattleDueForCalving = allCattle.where((cattle) {
        if (cattle.breedingStatus == null ||
            !cattle.breedingStatus!.isPregnant) {
          return false;
        }

        final calvingDate = cattle.breedingStatus!.expectedCalvingDate;
        if (calvingDate == null) {
          return false;
        }

        return calvingDate.isAfter(now) && calvingDate.isBefore(cutoff);
      }).toList();

      // Convert to event format
      return cattleDueForCalving
          .map((cattle) => {
                'cattleId': cattle.businessId,
                'tagId': cattle.tagId,
                'name': cattle.name,
                'dueDate': cattle.breedingStatus!.expectedCalvingDate,
                'daysUntilDue': cattle.breedingStatus!.expectedCalvingDate!
                    .difference(now)
                    .inDays,
                'type': 'Calving',
              })
          .toList();
    } catch (e) {
      _logger.severe('Error getting upcoming calving events: $e');
      throw DatabaseOperationException(
          'Failed to get upcoming calving events', e.toString());
    }
  }

  /// Get delivery record by ID
  Future<Map<String, dynamic>?> getDeliveryRecordById(String recordId) async {
    try {
      if (recordId.isEmpty) {
        throw ValidationException('Record ID is required');
      }

      // Placeholder for actual implementation - this would normally retrieve a delivery record by ID
      return null;
    } catch (e) {
      _logger.severe('Error getting delivery record by ID $recordId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve delivery record', e.toString());
    }
  }

  /// Get all pregnancy check events
  Future<List<Map<String, dynamic>>> getPregnancyCheckEvents(
      int daysAhead) async {
    try {
      final now = DateTime.now();
      final cutoff = now.add(Duration(days: daysAhead));

      // Get all active breeding records
      final allBreedingRecords =
          await _isar.collection<BreedingRecordIsar>().where().findAll();

      // Filter for records needing pregnancy check
      final recordsNeedingCheck = allBreedingRecords.where((record) {
        if (record.status != 'Pending' && record.status != 'Inseminated') {
          return false;
        }

        // Calculate check date (typically 30-45 days after breeding)
        if (record.date == null) {
          return false;
        }

        final checkDate = record.date!.add(const Duration(days: 35));
        return checkDate.isAfter(now) && checkDate.isBefore(cutoff);
      }).toList();

      // Convert to event format
      List<Map<String, dynamic>> events = [];
      for (final record in recordsNeedingCheck) {
        if (record.cattleId == null) continue;

        // Get cattle info
        final allCattle =
            await _isar.collection<CattleIsar>().where().findAll();
        CattleIsar? cattle;
        try {
          cattle = allCattle.firstWhere((c) => c.businessId == record.cattleId);
        } catch (_) {
          continue;
        }

        final checkDate = record.date!.add(const Duration(days: 35));
        events.add({
          'cattleId': record.cattleId,
          'tagId': cattle.tagId,
          'name': cattle.name,
          'dueDate': checkDate,
          'daysUntilDue': checkDate.difference(now).inDays,
          'type': 'Pregnancy Check',
          'breedingRecordId': record.businessId,
        });
      }

      return events;
    } catch (e) {
      _logger.severe('Error getting pregnancy check events: $e');
      throw DatabaseOperationException(
          'Failed to get pregnancy check events', e.toString());
    }
  }
}
