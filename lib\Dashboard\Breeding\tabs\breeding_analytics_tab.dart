import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../controllers/breeding_controller.dart';
import '../../widgets/index.dart';
import '../../../constants/app_tabs.dart';

class BreedingAnalyticsTab extends StatelessWidget {
  final BreedingController controller;

  const BreedingAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Cards
          _buildSummaryCards(context),
          const SizedBox(height: 24),
          
          // Breeding Status Chart
          _buildBreedingStatusChart(context),
          const SizedBox(height: 24),
          
          // Pregnancy Status Chart
          _buildPregnancyStatusChart(context),
          const SizedBox(height: 24),
          
          // Quick Stats
          _buildQuickStats(context),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            context,
            'Breeding Records',
            controller.totalBreedingRecords.toString(),
            Icons.favorite,
            UniversalEmptyStateTheme.breeding,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            context,
            'Pregnancies',
            controller.totalPregnancyRecords.toString(),
            Icons.pregnant_woman,
            Colors.purple,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            context,
            'Female Cattle',
            controller.femaleCattle.toString(),
            Icons.female,
            Colors.pink,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBreedingStatusChart(BuildContext context) {
    final statusData = controller.breedingByStatus;
    
    if (statusData.isEmpty || controller.totalBreedingRecords == 0) {
      return UniversalEmptyState.breeding(
        title: 'No Data Available',
        message: 'No breeding status data available',
      );
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Breeding Status Distribution',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: _buildBreedingPieChartSections(statusData),
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildLegend(statusData, _getBreedingStatusColors()),
          ],
        ),
      ),
    );
  }

  Widget _buildPregnancyStatusChart(BuildContext context) {
    final statusData = controller.pregnancyByStatus;
    
    if (statusData.isEmpty || controller.totalPregnancyRecords == 0) {
      return UniversalEmptyState.breeding(
        title: 'No Data Available',
        message: 'No pregnancy status data available',
      );
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Pregnancy Status Distribution',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: _buildPregnancyPieChartSections(statusData),
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildLegend(statusData, _getPregnancyStatusColors()),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Statistics',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatRow('Total Breeding Records', controller.totalBreedingRecords.toString()),
            _buildStatRow('Total Pregnancy Records', controller.totalPregnancyRecords.toString()),
            _buildStatRow('Active Pregnancies', controller.activePregnancies.toString()),
            _buildStatRow('Completed Pregnancies', controller.completedPregnancies.toString()),
            _buildStatRow('Female Cattle', controller.femaleCattle.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildBreedingPieChartSections(Map<String, int> data) {
    final colors = _getBreedingStatusColors();
    return data.entries.map((entry) {
      final percentage = (entry.value / controller.totalBreedingRecords) * 100;
      return PieChartSectionData(
        color: colors[entry.key] ?? Colors.grey,
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  List<PieChartSectionData> _buildPregnancyPieChartSections(Map<String, int> data) {
    final colors = _getPregnancyStatusColors();
    return data.entries.map((entry) {
      final percentage = (entry.value / controller.totalPregnancyRecords) * 100;
      return PieChartSectionData(
        color: colors[entry.key] ?? Colors.grey,
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildLegend(Map<String, int> data, Map<String, Color> colors) {
    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: data.entries.map((entry) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: colors[entry.key] ?? Colors.grey,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text('${entry.key} (${entry.value})'),
          ],
        );
      }).toList(),
    );
  }

  Map<String, Color> _getBreedingStatusColors() {
    return {
      'Active': Colors.orange,
      'Completed': Colors.green,
      'Pending': Colors.blue,
      'Failed': Colors.red,
      'Unknown': Colors.grey,
    };
  }

  Map<String, Color> _getPregnancyStatusColors() {
    return {
      'Active': Colors.purple,
      'Completed': Colors.green,
      'Confirmed': Colors.blue,
      'Failed': Colors.red,
      'Unknown': Colors.grey,
    };
  }
}
