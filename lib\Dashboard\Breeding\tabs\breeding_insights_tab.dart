import 'package:flutter/material.dart';
import '../controllers/breeding_controller.dart';
import '../../widgets/index.dart';
import '../../../constants/app_tabs.dart';

class BreedingInsightsTab extends StatelessWidget {
  final BreedingController controller;

  const BreedingInsightsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Breeding Management Insights',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'AI-powered recommendations and insights for your cattle breeding program',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // Insights Cards
          ..._buildInsightCards(context),
        ],
      ),
    );
  }

  List<Widget> _buildInsightCards(BuildContext context) {
    final insights = _generateInsights();
    
    if (insights.isEmpty) {
      return [
        UniversalEmptyState.breeding(
          title: 'No Insights Available',
          message: 'Add more breeding records to get personalized insights',
        ),
      ];
    }

    return insights.map((insight) => _buildInsightCard(context, insight)).toList();
  }

  Widget _buildInsightCard(BuildContext context, BreedingInsight insight) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  insight.icon,
                  color: insight.color,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    insight.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: insight.priority.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: insight.priority.color.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    insight.priority.label,
                    style: TextStyle(
                      color: insight.priority.color,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              insight.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (insight.recommendations.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Recommendations:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...insight.recommendations.map((rec) => Padding(
                padding: const EdgeInsets.only(left: 16, bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
                    Expanded(child: Text(rec)),
                  ],
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  List<BreedingInsight> _generateInsights() {
    final insights = <BreedingInsight>[];
    
    // Breeding Program Overview
    if (controller.totalBreedingRecords == 0) {
      insights.add(BreedingInsight(
        title: 'Start Your Breeding Program',
        description: 'You haven\'t recorded any breeding activities yet. A systematic breeding program is essential for herd improvement and productivity.',
        icon: Icons.favorite,
        color: UniversalEmptyStateTheme.breeding,
        priority: InsightPriority.high,
        recommendations: [
          'Record all breeding activities with dates',
          'Track breeding methods (natural vs AI)',
          'Monitor breeding success rates',
          'Plan breeding schedules for optimal timing',
        ],
      ));
    } else if (controller.totalBreedingRecords < 5) {
      insights.add(BreedingInsight(
        title: 'Building Breeding History',
        description: 'You have ${controller.totalBreedingRecords} breeding records. Continue building comprehensive breeding histories for better herd management.',
        icon: Icons.trending_up,
        color: Colors.blue,
        priority: InsightPriority.medium,
        recommendations: [
          'Maintain consistent record keeping',
          'Track breeding outcomes',
          'Monitor conception rates',
        ],
      ));
    }

    // Female Cattle Analysis
    final femaleCattleInsight = _analyzeFemaleHerd();
    if (femaleCattleInsight != null) {
      insights.add(femaleCattleInsight);
    }

    // Pregnancy Management
    final pregnancyInsight = _analyzePregnancies();
    if (pregnancyInsight != null) {
      insights.add(pregnancyInsight);
    }

    // Breeding Success Rate
    final successRateInsight = _analyzeBreedingSuccess();
    if (successRateInsight != null) {
      insights.add(successRateInsight);
    }

    // Best Practices
    insights.add(BreedingInsight(
      title: 'Breeding Management Best Practices',
      description: 'Follow these best practices to optimize your breeding program and improve herd genetics.',
      icon: Icons.star,
      color: Colors.green,
      priority: InsightPriority.medium,
      recommendations: [
        'Maintain detailed breeding records',
        'Monitor heat cycles and breeding windows',
        'Track conception and calving rates',
        'Plan breeding for optimal calving seasons',
        'Consider genetic diversity in breeding decisions',
        'Regular pregnancy checks by veterinarian',
        'Maintain proper nutrition during breeding',
      ],
    ));

    return insights;
  }

  BreedingInsight? _analyzeFemaleHerd() {
    final totalCattle = controller.cattle.length;
    final femaleCattle = controller.femaleCattle;
    
    if (totalCattle == 0) return null;

    final femalePercentage = (femaleCattle / totalCattle) * 100;

    if (femaleCattle == 0) {
      return BreedingInsight(
        title: 'No Female Cattle Detected',
        description: 'Your herd has no female cattle. Female cattle are essential for breeding and herd expansion.',
        icon: Icons.female,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Consider acquiring breeding females',
          'Plan herd composition for breeding goals',
          'Evaluate replacement heifer needs',
        ],
      );
    } else if (femalePercentage < 60) {
      return BreedingInsight(
        title: 'Low Female Cattle Ratio',
        description: 'Only ${femalePercentage.toStringAsFixed(1)}% of your herd is female. Consider increasing female cattle for better breeding potential.',
        icon: Icons.female,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Evaluate optimal female-to-male ratio',
          'Consider retaining more replacement heifers',
          'Plan breeding program expansion',
        ],
      );
    }

    return null;
  }

  BreedingInsight? _analyzePregnancies() {
    if (controller.totalPregnancyRecords == 0 && controller.totalBreedingRecords > 0) {
      return BreedingInsight(
        title: 'Missing Pregnancy Records',
        description: 'You have breeding records but no pregnancy records. Tracking pregnancies is crucial for breeding success monitoring.',
        icon: Icons.pregnant_woman,
        color: Colors.orange,
        priority: InsightPriority.high,
        recommendations: [
          'Record pregnancy confirmations after breeding',
          'Schedule regular pregnancy checks',
          'Track pregnancy outcomes',
          'Monitor calving dates and success rates',
        ],
      );
    }

    final activePregnancies = controller.activePregnancies;
    if (activePregnancies > 0) {
      return BreedingInsight(
        title: 'Active Pregnancies Monitoring',
        description: 'You have $activePregnancies active pregnancies. Ensure proper monitoring and care during this critical period.',
        icon: Icons.monitor_heart,
        color: Colors.purple,
        priority: InsightPriority.medium,
        recommendations: [
          'Schedule regular veterinary checkups',
          'Provide proper nutrition for pregnant cattle',
          'Monitor for signs of complications',
          'Prepare for upcoming calving dates',
        ],
      );
    }

    return null;
  }

  BreedingInsight? _analyzeBreedingSuccess() {
    if (controller.totalBreedingRecords == 0 || controller.totalPregnancyRecords == 0) {
      return null;
    }

    final successRate = (controller.totalPregnancyRecords / controller.totalBreedingRecords) * 100;

    if (successRate < 50) {
      return BreedingInsight(
        title: 'Low Breeding Success Rate',
        description: 'Your breeding success rate is ${successRate.toStringAsFixed(1)}%. This may indicate issues with breeding management.',
        icon: Icons.warning,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Review breeding timing and methods',
          'Consult with veterinarian about fertility',
          'Evaluate bull fertility if using natural breeding',
          'Consider nutrition and body condition factors',
          'Review heat detection accuracy',
        ],
      );
    } else if (successRate > 80) {
      return BreedingInsight(
        title: 'Excellent Breeding Success',
        description: 'Congratulations! Your breeding success rate is ${successRate.toStringAsFixed(1)}%. This indicates excellent breeding management.',
        icon: Icons.celebration,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Continue current breeding practices',
          'Share successful methods with other farmers',
          'Consider expanding breeding program',
        ],
      );
    }

    return null;
  }
}

class BreedingInsight {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final InsightPriority priority;
  final List<String> recommendations;

  BreedingInsight({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.priority,
    this.recommendations = const [],
  });
}

enum InsightPriority {
  low(Colors.green, 'Low'),
  medium(Colors.orange, 'Medium'),
  high(Colors.red, 'High');

  const InsightPriority(this.color, this.label);
  final Color color;
  final String label;
}
