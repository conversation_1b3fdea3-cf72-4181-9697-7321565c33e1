import 'package:flutter/material.dart';
import '../dialogs/breeding_form_dialog.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Cattle/widgets/breeding_history_card.dart';
import '../../Cattle/widgets/eligibility_card.dart';
import '../../../services/database/database_helper.dart';
import '../models/breeding_record_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../controllers/breeding_controller.dart';
import 'dart:async'; // Add this for StreamSubscription
import '../../../constants/app_colors.dart';
import 'package:intl/intl.dart';
import '../../../utils/message_utils.dart';
import 'package:collection/collection.dart'; // For firstWhereOrNull
import '../../widgets/index.dart';
// Import for type reference only

class BreedingRecordsTab extends StatelessWidget {
  final BreedingController controller;

  const BreedingRecordsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final records = controller.breedingRecords;

    if (records.isEmpty) {
      return UniversalEmptyState.breeding(
        title: 'No Breeding Records',
        message: 'Add your first breeding record to start tracking',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: records.length,
      itemBuilder: (context, index) {
        final record = records[index];
        final cattle = controller.getCattle(record.cattleId);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: const CircleAvatar(
              backgroundColor: UniversalEmptyStateTheme.breeding,
              child: Icon(
                Icons.favorite,
                color: Colors.white,
              ),
            ),
            title: Text(cattle?.name ?? 'Unknown Cattle'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Date: ${_formatDate(record.date)}'),
                if (record.method != null)
                  Text('Method: ${record.method}'),
                if (record.status != null)
                  Text('Status: ${record.status}'),
              ],
            ),
            trailing: Icon(
              _getStatusIcon(record.status),
              color: _getStatusColor(record.status),
            ),
          ),
        );
      },
    );
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Icons.pending;
      case 'completed':
        return Icons.check_circle;
      case 'failed':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Colors.orange;
      case 'completed':
        return Colors.green;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}

class BreedingRecordsScreen extends StatefulWidget {
  const BreedingRecordsScreen({Key? key}) : super(key: key);

  @override
  State<BreedingRecordsScreen> createState() => _BreedingRecordsScreenState();
}

class _BreedingRecordsScreenState extends State<BreedingRecordsScreen> {
  // Use dependency injection instead of singleton
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final TextEditingController _searchController = TextEditingController();

  List<BreedingRecordIsar> _breedingRecords = [];
  List<BreedingRecordIsar> _filteredBreedingRecords = [];
  Map<String, CattleIsar> _cattleMap = {};
  List<dynamic> _animalTypes = [];
  bool _isLoading = true;
  String _searchQuery = '';
  StreamSubscription? _breedingRecordSubscription;

  // Filter variables
  String _selectedAnimalType = 'All';
  String _selectedCattleId = 'All';
  String _selectedDateRange = 'All Time';
  List<CattleIsar> _filteredCattle = [];
  Map<String, String> _animalTypeIdToName =
      {}; // Changed from final to regular variable

  @override
  void initState() {
    super.initState();
    _loadData();
    _subscribeToBreedingRecordUpdates();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _breedingRecordSubscription?.cancel();
    super.dispose();
  }

  // Subscribe to breeding record updates
  void _subscribeToBreedingRecordUpdates() {
    // Use DatabaseHelper to subscribe to updates
    _breedingRecordSubscription =
        _databaseHelper.breedingRecordStream.listen((event) {
      if (mounted) {
        // Handle the event based on the action type
        final action = event['action'] as String?;
        final recordData = event['data'] as Map<String, dynamic>?;

        if (action != null && recordData != null) {
          final recordId = recordData['id'] as String?;

          if (recordId != null) {
            switch (action) {
              case 'add':
                // A new record was added - add it to our local state if not already present
                final newRecord = BreedingRecordIsar.fromMap(recordData);
                setState(() {
                  if (!_breedingRecords.any((r) => r.businessId == recordId)) {
                    _breedingRecords.add(newRecord);
                    _filterRecords();
                  }
                });
                break;

              case 'update':
                // A record was updated - update it in our local state
                final updatedRecord = BreedingRecordIsar.fromMap(recordData);
                setState(() {
                  final index = _breedingRecords
                      .indexWhere((r) => r.businessId == recordId);
                  if (index >= 0) {
                    _breedingRecords[index] = updatedRecord;
                    _filterRecords();
                  }
                });
                break;

              case 'delete':
                // A record was deleted - remove it from our local state
                setState(() {
                  _breedingRecords.removeWhere((r) => r.businessId == recordId);
                  _filterRecords();
                });
                break;

              default:
                // Unknown action - refresh all records
                _refreshRecords();
                break;
            }
          }
        } else {
          // If we can't determine the action or get the record data, refresh all records
          _refreshRecords();
        }
      }
    });
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // Load animal types using DatabaseHelper
      final animalTypes =
          await _databaseHelper.farmSetupHandler.getAllAnimalTypes();

      // Create maps for efficient lookups
      _animalTypeIdToName = {
        for (var type in animalTypes) type.businessId ?? '': type.name ?? ''
      };
      _animalTypes = animalTypes;

      // Load all cattle using DatabaseHelper
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();
      final cattleMap = {
        for (var cattle in allCattle) cattle.tagId ?? '': cattle
      };

      // Load breeding records for all cattle
      final records =
          await _databaseHelper.breedingHandler.getAllBreedingRecords();

      if (mounted) {
        setState(() {
          _cattleMap = cattleMap;
          _breedingRecords = records;
          _filteredBreedingRecords = List.from(records);
          _filteredCattle = allCattle;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        BreedingMessageUtils.showBreedingError(context, 'loading', e.toString());
      }
    }
  }

  // Refresh records when data changes
  Future<void> _refreshRecords() async {
    try {
      // Get updated breeding records
      final records =
          await _databaseHelper.breedingHandler.getAllBreedingRecords();

      if (mounted) {
        setState(() {
          _breedingRecords = records;
          _filterRecords(); // Apply existing filters to the new data
        });
      }
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showBreedingError(context, 'refreshing', e.toString());
      }
    }
  }

  // Update filtered cattle list when animal type is selected
  void _updateFilteredCattle() {
    final allCattle = _cattleMap.values.toList();

    if (_selectedAnimalType == 'All') {
      // No animal type filter, show all female cattle
      _filteredCattle = allCattle
          .where((cattle) => cattle.gender?.toLowerCase() == 'female')
          .toList();
    } else {
      // Filter by selected animal type name
      _filteredCattle = allCattle
          .where((cattle) =>
              cattle.gender?.toLowerCase() == 'female' &&
              _animalTypeIdToName[cattle.animalTypeId] == _selectedAnimalType)
          .toList();
    }

    // If the currently selected cattle is not in the filtered list, clear the selection
    if (_selectedCattleId != 'All' &&
        !_filteredCattle.any((c) => c.tagId == _selectedCattleId)) {
      _selectedCattleId = 'All';
    }

    // Sort by name
    _filteredCattle.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _searchController.clear();
      _selectedAnimalType = 'All';
      _selectedCattleId = 'All';
      _selectedDateRange = 'All Time';
      _updateFilteredCattle();
      _filterRecords();
    });
  }

  void _filterRecords() {
    // Start with all records
    List<BreedingRecordIsar> filtered = List.from(_breedingRecords);

    // Apply animal type filter if selected
    if (_selectedAnimalType != 'All') {
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record.cattleId];
        return cattle != null &&
            _animalTypeIdToName[cattle.animalTypeId] == _selectedAnimalType;
      }).toList();
    }

    // Apply cattle filter if selected
    if (_selectedCattleId != 'All') {
      filtered = filtered
          .where((record) => record.cattleId == _selectedCattleId)
          .toList();
    }

    // Apply date range filter if selected
    if (_selectedDateRange != 'All Time') {
      final dateRange = _selectedDateRange;
      final now = DateTime.now();
      DateTime startDate;
      DateTime endDate;

      if (dateRange == 'Today') {
        startDate = DateTime(now.year, now.month, now.day);
        endDate = startDate.add(const Duration(days: 1));
      } else if (dateRange == '7 Days') {
        startDate = now.subtract(const Duration(days: 7));
        endDate = now.add(const Duration(days: 1));
      } else if (dateRange == '30 Days') {
        startDate = now.subtract(const Duration(days: 30));
        endDate = now.add(const Duration(days: 1));
      } else if (dateRange == '90 Days') {
        startDate = now.subtract(const Duration(days: 90));
        endDate = now.add(const Duration(days: 1));
      } else {
        // Default to all time if somehow we get here
        startDate = DateTime(1900);
        endDate = DateTime(2100);
      }

      filtered = filtered.where((record) {
        final breedingDate = record.date;
        return breedingDate != null &&
            breedingDate.isAfter(startDate) &&
            breedingDate.isBefore(endDate);
      }).toList();
    }

    // Apply search filter if there's a search query
    if (_searchQuery.isNotEmpty) {
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record.cattleId];
        if (cattle == null) return false;

        final searchLower = _searchQuery.toLowerCase();
        return (cattle.name?.toLowerCase() ?? '').contains(searchLower) ||
            (cattle.tagId?.toLowerCase() ?? '').contains(searchLower);
      }).toList();
    }

    setState(() {
      _filteredBreedingRecords = filtered;
    });
  }

  Future<void> _editRecord(BreedingRecordIsar record) async {
    // Verify the record has a business ID
    if (record.businessId == null || record.cattleId == null) {
      if (mounted) {
        BreedingMessageUtils.showError(context, BreedingMessageUtils.invalidRecordId);
      }
      return;
    }

    // Use the BreedingFormDialog with the record directly
    final updatedRecord = await showDialog<BreedingRecordIsar>(
      context: context,
      builder: (context) => BreedingFormDialog(
        record: record,
        initialCattleId: record.cattleId,
      ),
    );

    if (updatedRecord != null && mounted) {
      try {
        // Get the original record to check for status changes
        final originalStatus = record.status?.toLowerCase() ?? '';
        final newStatus = updatedRecord.status?.toLowerCase() ?? '';

        // Save to database - the record already has the correct ID
        await _databaseHelper.breedingHandler
            .updateBreedingRecord(updatedRecord);

        // Handle pregnancy record creation/update when changing to Confirmed or Completed
        if ((newStatus == 'confirmed' || newStatus == 'completed') &&
            (originalStatus != 'confirmed' && originalStatus != 'completed')) {
          try {
            // Extract the cattle ID
            final cattleId = updatedRecord.cattleId.toString();

            // Get any existing pregnancy record linked to this breeding record
            final pregnancyRecords = await _databaseHelper.breedingHandler
                .getPregnancyRecordsForCattle(cattleId);

            try {
              final existingPregnancy = pregnancyRecords.firstWhere(
                (pr) => pr.breedingRecordId == updatedRecord.businessId,
              );

              // If pregnancy record exists, update it
              final updatedPregnancy = existingPregnancy.copyWith(
                status: newStatus == 'completed' ? 'Completed' : 'Confirmed',
                expectedCalvingDate: updatedRecord.expectedDate,
                actualCalvingDate: newStatus == 'completed' ? DateTime.now() : null,
                endDate: newStatus == 'completed' ? DateTime.now() : null,
              );

              // Update pregnancy record
              await _databaseHelper.breedingHandler
                  .updatePregnancyRecord(updatedPregnancy, updateLinkedBreedingRecord: false);
            } catch (e) {
              // Create new pregnancy record if none exists
              final pregnancyRecord = PregnancyRecordIsar.create(
                cattleId: cattleId,
                startDate: updatedRecord.date ?? DateTime.now(),
                status: newStatus == 'completed' ? 'Completed' : 'Confirmed',
                expectedCalvingDate: updatedRecord.expectedDate,
                actualCalvingDate: newStatus == 'completed' ? DateTime.now() : null,
                endDate: newStatus == 'completed' ? DateTime.now() : null,
                notes: 'Created from breeding record: ${updatedRecord.method} on ${updatedRecord.date != null ? DateFormat('MMMM dd, yyyy').format(updatedRecord.date!) : 'Unknown date'}',
                breedingRecordId: updatedRecord.businessId,
              );

              // Save pregnancy record
              await _databaseHelper.breedingHandler
                  .managePregnancyRecord(pregnancyRecord);
            }
          } catch (e) {
            debugPrint('Error handling pregnancy record in edit: $e');
          }
        }

        // Update local state instead of refreshing all records
        setState(() {
          // Find and replace the updated record in the list
          final index = _breedingRecords
              .indexWhere((r) => r.businessId == updatedRecord.businessId);
          if (index >= 0) {
            _breedingRecords[index] = updatedRecord;
          }

          // Re-apply filters
          _filterRecords();
        });

        if (mounted) {
          final statusChanged = (newStatus == 'confirmed' || newStatus == 'completed') &&
              (originalStatus != 'confirmed' && originalStatus != 'completed');

          final message = BreedingMessageUtils.breedingRecordUpdated(
            pregnancyCreated: statusChanged,
          );
          BreedingMessageUtils.showSuccess(context, message);
        }
      } catch (e) {
        if (mounted) {
          BreedingMessageUtils.showBreedingError(context, 'updating', e.toString());
        }
      }
    }
  }

  Future<void> _deleteRecord(BreedingRecordIsar record) async {
    // Verify the record ID format
    if (record.businessId == null) {
      if (mounted) {
        BreedingMessageUtils.showError(context, BreedingMessageUtils.invalidRecordId);
      }
      return;
    }

    // Check for related pregnancy records
    final pregnancyRecords = await _databaseHelper.breedingHandler
        .getPregnancyRecordsForCattle(record.cattleId ?? '');
    final relatedPregnancyRecords = pregnancyRecords
        .where((pr) => pr.breedingRecordId == record.businessId)
        .toList();

    // Check for related delivery records through pregnancy records
    List<String> specificRecords = [];
    int deliveryCount = 0;

    for (final pregnancyRecord in relatedPregnancyRecords) {
      // Add pregnancy record ID
      if (pregnancyRecord.businessId != null) {
        specificRecords.add(pregnancyRecord.businessId!);
      }

      // Check for delivery records linked to this pregnancy
      final deliveryRecords = await _databaseHelper.breedingHandler
          .getDeliveryRecordsForCattle(record.cattleId ?? '');
      final linkedDelivery = deliveryRecords.firstWhereOrNull(
        (dr) => dr['pregnancyId'] == pregnancyRecord.businessId,
      );

      if (linkedDelivery != null) {
        deliveryCount++;
        final deliveryId = linkedDelivery['id']?.toString();
        if (deliveryId != null) {
          specificRecords.add(deliveryId);
        }
      }
    }

    // Show standardized confirmation dialog
    if (!mounted) return;
    final cattle = _cattleMap[record.cattleId];
    final cattleDisplayName = cattle?.name != null && cattle?.tagId != null
        ? '${cattle!.name} (${cattle.tagId})'
        : cattle?.name ?? cattle?.tagId ?? 'Unknown';
    final confirmed = await BreedingMessageUtils.showBreedingDeleteConfirmation(
      context,
      cattleName: cattleDisplayName,
      recordId: record.businessId,
      pregnancyRecords: relatedPregnancyRecords.length,
      deliveryRecords: deliveryCount,
      specificRecords: specificRecords,
    );

    if (confirmed == true) {
      try {
        // Delete the breeding record - this will cascade delete related records
        await _databaseHelper.breedingHandler
            .deleteBreedingRecord(record.businessId!);

        // Update local state instead of refreshing all records
        setState(() {
          // Remove the record from the list
          _breedingRecords
              .removeWhere((r) => r.businessId == record.businessId);
          // Re-apply filters
          _filterRecords();
        });

        if (mounted) {
          final message = BreedingMessageUtils.breedingRecordDeleted(
            pregnancyRecords: relatedPregnancyRecords.length,
          );
          BreedingMessageUtils.showSuccess(context, message);
        }
      } catch (e) {
        if (mounted) {
          BreedingMessageUtils.showBreedingError(context, 'deleting', e.toString());
        }
      }
    }
  }

  String _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return '#9C27B0'; // Purple
      case 'confirmed':
        return '#4CAF50'; // Green
      case 'pending':
        return '#2196F3'; // Blue
      case 'failed':
        return '#F44336'; // Red
      default:
        return '#2E7D32'; // Primary Green
    }
  }

  Future<void> _showStatusChangeDialog(BreedingRecordIsar record) async {
    final currentStatus = record.status ?? 'Unknown';
    String selectedStatus = currentStatus;
    final cattle = _cattleMap[record.cattleId];
    final cattleName = cattle?.name ?? 'Unknown Cattle';

    final result = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        titlePadding: const EdgeInsets.all(16),
        title: Row(
          children: [
            Icon(
              Icons.edit_note,
              color: Color(
                int.parse(
                      _getStatusColor(selectedStatus).substring(1),
                      radix: 16,
                    ) |
                    0xFF000000,
              ),
            ),
            const SizedBox(width: 8),
            const Flexible(
              child: Text(
                'Update Breeding Status',
                style: TextStyle(fontWeight: FontWeight.bold),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
        content: StatefulBuilder(
          builder: (context, setState) {
            final newStatusColor = _getStatusColor(selectedStatus);
            final newColor = Color(
              int.parse(
                    newStatusColor.substring(1),
                    radix: 16,
                  ) |
                  0xFF000000,
            );

            return Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Show cattle info
                Center(
                  child: Column(
                    children: [
                      Text(
                        'Cattle: $cattleName',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Tag ID: ${record.cattleId}',
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                const Divider(),
                const SizedBox(height: 8),

                // Current status display
                Row(
                  children: [
                    const Text(
                      'Current Status:',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Color(
                            int.parse(
                                  _getStatusColor(currentStatus).substring(1),
                                  radix: 16,
                                ) |
                                0xFF000000,
                          ).withValues(alpha: 0x99),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: Color(
                              int.parse(
                                    _getStatusColor(currentStatus).substring(1),
                                    radix: 16,
                                  ) |
                                  0xFF000000,
                            ).withValues(alpha: 0x99),
                          ),
                        ),
                        child: Text(
                          currentStatus,
                          style: TextStyle(
                            color: Color(
                              int.parse(
                                    _getStatusColor(currentStatus).substring(1),
                                    radix: 16,
                                  ) |
                                  0xFF000000,
                            ),
                            fontWeight: FontWeight.bold,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // New status selection
                Text(
                  'Select new status:',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: newColor,
                  ),
                ),
                const SizedBox(height: 8),
                ...['Pending', 'Confirmed', 'Completed', 'Failed'].map((status) {
                  final statusColor = _getStatusColor(status);
                  final color = Color(
                    int.parse(
                          statusColor.substring(1),
                          radix: 16,
                        ) |
                        0xFF000000,
                  );

                  return RadioListTile<String>(
                    title: Text(
                      status,
                      style: TextStyle(
                        color:
                            selectedStatus == status ? color : AppColors.primary,
                        fontWeight: selectedStatus == status
                            ? FontWeight.bold
                            : FontWeight.normal,
                      ),
                    ),
                    value: status,
                    groupValue: selectedStatus,
                    activeColor: color,
                    onChanged: (value) {
                      setState(() {
                        selectedStatus = value!;
                      });
                    },
                    secondary: Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: color.withValues(alpha: 0x99),
                        shape: BoxShape.circle,
                        border: Border.all(color: color, width: 2),
                      ),
                    ),
                    contentPadding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                    dense: true,
                  );
                }).toList(),

                if (selectedStatus != currentStatus) ...[
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text(
                        'Preview:',
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          color: Colors.black87, // Use solid readable color
                        ),
                      ),
                      const SizedBox(width: 8),
                      const Icon(
                        Icons.arrow_forward,
                        size: 16,
                        color: Colors.black87, // Use solid readable color
                      ),
                      const SizedBox(width: 8),
                      Flexible(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 10, vertical: 2),
                          decoration: BoxDecoration(
                            color: newColor.withValues(alpha: 0x99),
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(
                                color: newColor.withValues(alpha: 0x99),
                                width: 1),
                          ),
                          child: Text(
                            selectedStatus,
                            style: TextStyle(
                              color: newColor,
                              fontWeight: FontWeight.bold,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            );
          },
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(selectedStatus),
            style: TextButton.styleFrom(
              foregroundColor: Color(
                int.parse(
                      _getStatusColor(selectedStatus).substring(1),
                      radix: 16,
                    ) |
                    0xFF000000,
              ),
            ),
            child: const Text('Update'),
          ),
        ],
      ),
    );

    if (result != null && result != currentStatus) {
      try {
        // Create updated record with new status using copyWith
        final updatedRecord = record.copyWith(status: result);

        // Save to database
        await _databaseHelper.breedingHandler
            .updateBreedingRecord(updatedRecord);

        // Handle pregnancy record creation/update when changing to Confirmed or Completed
        if ((result.toLowerCase() == 'confirmed' || result.toLowerCase() == 'completed') &&
            (currentStatus.toLowerCase() != 'confirmed' && currentStatus.toLowerCase() != 'completed')) {
          try {
            // Extract the cattle ID
            final cattleId = record.cattleId.toString();

            // Get any existing pregnancy record linked to this breeding record
            final pregnancyRecords = await _databaseHelper.breedingHandler
                .getPregnancyRecordsForCattle(cattleId);

            try {
              final existingPregnancy = pregnancyRecords.firstWhere(
                (pr) => pr.breedingRecordId == record.businessId,
              );

              // If pregnancy record exists, update it
              final updatedPregnancy = existingPregnancy.copyWith(
                status: result,
                expectedCalvingDate: record.expectedDate,
                actualCalvingDate: result.toLowerCase() == 'completed' ? DateTime.now() : null,
                endDate: result.toLowerCase() == 'completed' ? DateTime.now() : null,
              );

              // Update pregnancy record
              await _databaseHelper.breedingHandler
                  .updatePregnancyRecord(updatedPregnancy, updateLinkedBreedingRecord: false);
            } catch (e) {
              // Create new pregnancy record if none exists
              final pregnancyRecord = PregnancyRecordIsar.create(
                cattleId: cattleId,
                startDate: record.date ?? DateTime.now(),
                status: result,
                expectedCalvingDate: record.expectedDate,
                actualCalvingDate: result.toLowerCase() == 'completed' ? DateTime.now() : null,
                endDate: result.toLowerCase() == 'completed' ? DateTime.now() : null,
                notes: 'Created from breeding record: ${record.method} on ${record.date != null ? DateFormat('MMMM dd, yyyy').format(record.date!) : 'Unknown date'}',
                breedingRecordId: record.businessId,
              );

              // Save pregnancy record
              await _databaseHelper.breedingHandler
                  .managePregnancyRecord(pregnancyRecord);
            }
          } catch (e) {
            debugPrint('Error handling pregnancy record: $e');
          }
        }
        // Handle pregnancy status update when changing from Confirmed/Completed to Pending/Failed
        else if ((currentStatus.toLowerCase() == 'confirmed' || currentStatus.toLowerCase() == 'completed') &&
            (result.toLowerCase() == 'pending' || result.toLowerCase() == 'failed')) {
          try {
            // Extract the cattle ID
            final cattleId = record.cattleId.toString();

            // Get any existing pregnancy record linked to this breeding record
            final pregnancyRecords = await _databaseHelper.breedingHandler
                .getPregnancyRecordsForCattle(cattleId);

            try {
              final existingPregnancy = pregnancyRecords.firstWhere(
                (pr) => pr.breedingRecordId == record.businessId,
              );

              // Update pregnancy status to match breeding status instead of deleting
              final updatedPregnancy = existingPregnancy.copyWith(
                status: result.toLowerCase() == 'pending' ? 'Pending' : 'Failed',
              );

              await _databaseHelper.breedingHandler
                  .updatePregnancyRecord(updatedPregnancy, updateLinkedBreedingRecord: false);
            } catch (e) {
              debugPrint('No pregnancy record found to update: $e');
              // No pregnancy record found, which is fine in this case
            }
          } catch (e) {
            debugPrint('Error handling pregnancy record downgrade: $e');
          }
        }

        // Find and update the record in the local state
        final index = _breedingRecords
            .indexWhere((r) => r.businessId == record.businessId);
        if (index >= 0) {
          setState(() {
            _breedingRecords[index] = updatedRecord;
            _filterRecords(); // Re-apply filters
          });
        }

        if (mounted) {
          final isConfirmedOrCompleted = result.toLowerCase() == 'confirmed' || result.toLowerCase() == 'completed';
          final wasNotConfirmedOrCompleted = currentStatus.toLowerCase() != 'confirmed' && currentStatus.toLowerCase() != 'completed';
          final wasConfirmedOrCompleted = currentStatus.toLowerCase() == 'confirmed' || currentStatus.toLowerCase() == 'completed';
          final isPendingOrFailed = result.toLowerCase() == 'pending' || result.toLowerCase() == 'failed';

          final message = BreedingMessageUtils.breedingStatusUpdated(
            result,
            pregnancyCreated: isConfirmedOrCompleted && wasNotConfirmedOrCompleted,
            pregnancyUpdated: wasConfirmedOrCompleted && isPendingOrFailed,
          );
          BreedingMessageUtils.showSuccess(context, message);
        }
      } catch (e) {
        if (mounted) {
          BreedingMessageUtils.showBreedingError(context, 'status', e.toString());
        }
      }
    }
  }

  // Comprehensive method to get eligible cattle with detailed eligibility checking
  Future<List<CattleIsar>> _getEligibleCattleWithDetails() async {
    final allCattle = _cattleMap.values.toList();
    final eligibleCattle = <CattleIsar>[];

    for (final cattle in allCattle) {
      // Only check female cattle
      if (cattle.gender?.toLowerCase() == 'female') {
        // Use comprehensive eligibility checking
        final eligibilityResult = await _checkBreedingEligibility(cattle);

        if (eligibilityResult.isEligible) {
          eligibleCattle.add(cattle);
        }
      }
    }

    return eligibleCattle;
  }

  // Comprehensive breeding eligibility check using EligibilityCard logic
  Future<EligibilityCheckResult> _checkBreedingEligibility(CattleIsar cattle) async {
    // Get breeding records for this cattle
    final breedingRecords = await _databaseHelper.breedingHandler
        .getBreedingRecordsForCattle(cattle.tagId ?? '')
        .then((records) => records.map((record) => record.toMap()).toList());

    // Get animal type for age validation
    final animalTypes = await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
    final animalType = animalTypes.isNotEmpty
        ? animalTypes.firstWhere(
            (type) => type.businessId == cattle.animalTypeId,
            orElse: () => animalTypes.first,
          )
        : null;

    // Use EligibilityCard's comprehensive checking
    return EligibilityCard.checkBreedingEligibility(
      gender: cattle.gender ?? '',
      cattleId: cattle.tagId ?? '',
      animalTypeId: cattle.animalTypeId ?? '',
      isPregnant: cattle.breedingStatus?.isPregnant ?? false,
      dateOfBirth: cattle.dateOfBirth,
      purchaseDate: cattle.purchaseDate,
      lastBreedingDate: cattle.breedingStatus?.breedingDate,
      lastCalvingDate: cattle.breedingStatus?.lastCalvingDate,
      breedingRecords: breedingRecords,
      animalTypeBreedingAge: animalType?.defaultBreedingAge != null
          ? (animalType!.defaultBreedingAge! / 30).round() // Convert days to months
          : null,
      animalTypeEmptyPeriodDays: animalType?.defaultEmptyPeriodDays,
    );
  }

  // Show detailed dialog when no eligible cattle found
  Future<void> _showNoEligibleCattleDialog() async {
    final allFemales = _cattleMap.values
        .where((c) => c.gender?.toLowerCase() == 'female')
        .toList();

    if (allFemales.isEmpty) {
      if (mounted) {
        BreedingMessageUtils.showWarning(context, BreedingMessageUtils.noFemaleCattleFound);
      }
      return;
    }

    // Check each female cattle and categorize ineligibility reasons
    final reasons = <String, List<String>>{};
    for (final cattle in allFemales) {
      final eligibility = await _checkBreedingEligibility(cattle);
      if (!eligibility.isEligible) {
        final reason = eligibility.reasonMessage;
        if (!reasons.containsKey(reason)) {
          reasons[reason] = [];
        }
        reasons[reason]!.add('${cattle.name ?? 'Unknown'} (${cattle.tagId ?? 'Unknown'})');
      }
    }

    if (!mounted) return;

    // Show detailed dialog with breakdown of why cattle aren't eligible
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        titlePadding: const EdgeInsets.all(16),
        contentPadding: const EdgeInsets.all(16),
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Color(0xFF2196F3),
              size: 24,
            ),
            SizedBox(width: 12),
            Flexible(
              child: Text(
                'No Eligible Cattle',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'None of your ${allFemales.length} female cattle are currently eligible for breeding:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...reasons.entries.map((entry) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '• ${entry.key}',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 16, top: 4),
                    child: Text(
                      'Cattle: ${entry.value.join(", ")}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF2E7D32),
            ),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Show cattle selection dialog for multiple eligible cattle
  Future<CattleIsar?> _showEligibleCattleSelectionDialog(List<CattleIsar> eligibleCattle) async {
    return await showDialog<CattleIsar>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Cattle for Breeding'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: eligibleCattle.length,
            itemBuilder: (context, index) {
              final cattle = eligibleCattle[index];
              return ListTile(
                title: Text(cattle.name ?? 'Unknown'),
                subtitle: Text('Tag ID: ${cattle.tagId ?? 'Unknown'}'),
                trailing: const Icon(Icons.check_circle, color: Color(0xFF4CAF50)),
                onTap: () => Navigator.of(context).pop(cattle),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  // Hybrid approach: Pre-filter + Dialog selection + Detailed error messages
  Future<void> _showBreedingForm() async {
    setState(() => _isLoading = true);

    try {
      // 1. First, get eligible cattle (pre-filter with comprehensive checking)
      final eligibleCattle = await _getEligibleCattleWithDetails();

      setState(() => _isLoading = false);

      if (eligibleCattle.isEmpty) {
        // 2. If no eligible cattle, show detailed explanation
        await _showNoEligibleCattleDialog();
        return;
      }

      if (!mounted) return;

      CattleIsar? selectedCattle;

      // 3. If only one eligible cattle, go directly to form
      if (eligibleCattle.length == 1) {
        selectedCattle = eligibleCattle.first;
      } else {
        // 4. If multiple eligible cattle, show selection dialog
        selectedCattle = await _showEligibleCattleSelectionDialog(eligibleCattle);
        if (selectedCattle == null) return; // User cancelled
      }

      // 5. Double-check eligibility before opening form (safety check)
      final finalEligibilityCheck = await _checkBreedingEligibility(selectedCattle);
      if (!finalEligibilityCheck.isEligible) {
        if (mounted) {
          await _showEligibilityErrorDialog(selectedCattle, finalEligibilityCheck);
        }
        return;
      }

      // 6. Open breeding form with selected cattle
      if (!mounted) return;
      final newRecord = await showDialog<BreedingRecordIsar>(
        context: context,
        builder: (context) => BreedingFormDialog(
          initialCattleId: selectedCattle?.tagId,
        ),
      );

      // 7. Handle form result
      if (newRecord != null && mounted) {
        await _handleNewBreedingRecord(newRecord);
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        BreedingMessageUtils.showError(context, BreedingMessageUtils.generalError(e.toString()));
      }
    }
  }

  // Show detailed eligibility error dialog
  Future<void> _showEligibilityErrorDialog(CattleIsar cattle, EligibilityCheckResult eligibilityResult) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        titlePadding: const EdgeInsets.all(16),
        contentPadding: const EdgeInsets.all(16),
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Row(
          children: [
            Icon(
              eligibilityResult.statusIcon,
              color: eligibilityResult.statusColor,
              size: 24,
            ),
            const SizedBox(width: 12),
            Flexible(
              child: Text(
                eligibilityResult.statusMessage,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Cattle: ${cattle.name ?? 'Unknown'} (${cattle.tagId ?? 'Unknown'})',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            const Text(
              'Cannot add breeding record because:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(eligibilityResult.reasonMessage),
            if (eligibilityResult.nextEligibleDateMessage != null) ...[
              const SizedBox(height: 12),
              Text(
                eligibilityResult.nextEligibleDateMessage!,
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF2E7D32),
            ),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Handle new breeding record creation
  Future<void> _handleNewBreedingRecord(BreedingRecordIsar newRecord) async {
    try {
      setState(() => _isLoading = true);

      // Add the breeding record and create pregnancy record if status is Confirmed or Completed
      final shouldCreatePregnancy = newRecord.status?.toLowerCase() == 'confirmed' ||
                                   newRecord.status?.toLowerCase() == 'completed';
      await _databaseHelper.breedingHandler.addBreedingRecord(
        newRecord,
        createPregnancyRecord: shouldCreatePregnancy,
      );

      // Update local state instead of refreshing all records
      setState(() {
        _breedingRecords.add(newRecord);
        _filterRecords();
        _isLoading = false;
      });

      if (mounted) {
        final message = BreedingMessageUtils.breedingRecordCreated(
          withPregnancy: shouldCreatePregnancy,
        );
        BreedingMessageUtils.showSuccess(context, message);
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        BreedingMessageUtils.showBreedingError(context, 'adding', e.toString());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Breeding Records',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showBreedingForm,
        backgroundColor: AppColors.primary,
        tooltip: 'Add Breeding Record',
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          _buildSearchAndFilterSection(),

          // Records list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredBreedingRecords.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.favorite_border,
                              size: 80,
                              color: AppColors.primary.withValues(alpha: 0.4),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _searchQuery.isEmpty
                                  ? 'No breeding records found'
                                  : 'No matching records found',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: AppColors.primary.withValues(alpha: 0.7),
                                ),
                              ),
                            ],
                          ),
                        )
                    : RefreshIndicator(
                        onRefresh: _refreshRecords,
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              _buildRecordsList(),
                              // Add padding at the bottom for the FAB
                              const SizedBox(height: 80),
                            ],
                          ),
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecordsList() {
    // Create a list of records with cattle name and ID information
    final recordsWithCattleInfo = _filteredBreedingRecords.map((record) {
      final cattle = _cattleMap[record.cattleId];
      final recordMap = record.toMap();
      recordMap['cattleName'] = cattle?.name ?? 'Unknown Cattle';
      recordMap['cattleId'] = cattle?.tagId ?? 'Unknown';
      return recordMap;
    }).toList();

    return BreedingHistoryCard(
      records: recordsWithCattleInfo,
      title: 'Breeding Records',
      emptyMessage: 'No breeding records found',
      onEdit: (recordMap) {
        // Convert map back to BreedingRecordIsar for editing
        final record = BreedingRecordIsar.fromMap(recordMap);
        _editRecord(record);
      },
      onDelete: (recordMap) {
        // Convert map back to BreedingRecordIsar for deleting
        final record = BreedingRecordIsar.fromMap(recordMap);
        _deleteRecord(record);
      },
      onStatusTap: (recordMap) {
        // Convert map back to BreedingRecordIsar for status update
        final record = BreedingRecordIsar.fromMap(recordMap);
        _showStatusChangeDialog(record);
      },
    );
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Search Row
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by cattle name or tag ID',
                prefixIcon: const Icon(Icons.search),
                border: const OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12)),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                  _filterRecords();
                });
              },
            ),
          ),

          // Filter Row
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            child: Row(
              children: [
                // Animal Type Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String?>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Types'),
                        ),
                        ..._animalTypes.map((type) => PopupMenuItem(
                              value: type.name,
                              child: Text(type.name),
                            )),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedAnimalType = value ?? 'All';
                          _updateFilteredCattle();
                          _filterRecords();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedAnimalType,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Cattle Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String?>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: null,
                          child: Text('All Cattle'),
                        ),
                        ..._filteredCattle.map((cattle) => PopupMenuItem(
                              value: cattle.tagId,
                              child: Text('${cattle.name} (${cattle.tagId})'),
                            )),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedCattleId = value!;
                          _filterRecords();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedCattleId,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Date Range Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                            value: 'All Time', child: Text('All Time')),
                        const PopupMenuItem(
                            value: 'Today', child: Text('Today')),
                        const PopupMenuItem(
                            value: '7 Days', child: Text('7 Days')),
                        const PopupMenuItem(
                            value: '30 Days', child: Text('30 Days')),
                        const PopupMenuItem(
                            value: '90 Days', child: Text('90 Days')),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedDateRange = value;
                          _filterRecords();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedDateRange,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Clear Filters Button (only show when filters are applied)
          if (_selectedAnimalType != 'All' ||
              _selectedCattleId != 'All' ||
              _selectedDateRange != 'All Time' ||
              _searchQuery.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _clearFilters,
                      icon: const Icon(Icons.clear),
                      label: const Text('Clear Filters'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    '${_filteredBreedingRecords.length} of ${_breedingRecords.length} records',
                    style: TextStyle(color: AppColors.primary.withValues(alpha: 0.7)),
                  ),
                ],
              ),
            ),
          const Divider(height: 1),
        ],
      ),
    );
  }
}
