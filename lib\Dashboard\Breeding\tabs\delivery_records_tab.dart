import 'package:flutter/material.dart';
import '../../../services/database/database_helper.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../dialogs/delivery_form_dialog.dart';
import '../../Cattle/widgets/delivery_history_card.dart';
import '../controllers/breeding_controller.dart';
import '../../../utils/message_utils.dart';
import 'dart:async';
import '../../../constants/app_colors.dart';
import '../../widgets/index.dart';
import 'package:intl/intl.dart';

class DeliveryRecordsTab extends StatelessWidget {
  final BreedingController controller;

  const DeliveryRecordsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // For now, we'll show completed pregnancies as delivery records
    final deliveryRecords = controller.pregnancyRecords
        .where((record) => record.status?.toLowerCase() == 'completed')
        .toList();

    if (deliveryRecords.isEmpty) {
      return UniversalEmptyState.breeding(
        title: 'No Delivery Records',
        message: 'Delivery records will appear here when pregnancies are completed',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: deliveryRecords.length,
      itemBuilder: (context, index) {
        final record = deliveryRecords[index];
        final cattle = controller.getCattle(record.cattleId);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.blue,
              child: const Icon(
                Icons.child_care,
                color: Colors.white,
              ),
            ),
            title: Text(cattle?.name ?? 'Unknown Cattle'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Breeding Date: ${_formatDate(record.startDate)}'),
                if (record.expectedCalvingDate != null)
                  Text('Expected Calving: ${_formatDate(record.expectedCalvingDate)}'),
                if (record.actualCalvingDate != null)
                  Text('Actual Calving: ${_formatDate(record.actualCalvingDate)}'),
                const Text('Status: Delivered'),
              ],
            ),
            trailing: const Icon(
              Icons.check_circle,
              color: Colors.green,
            ),
          ),
        );
      },
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}

class DeliveryRecordsScreen extends StatefulWidget {
  const DeliveryRecordsScreen({Key? key}) : super(key: key);

  @override
  State<DeliveryRecordsScreen> createState() => _DeliveryRecordsScreenState();
}

class _DeliveryRecordsScreenState extends State<DeliveryRecordsScreen> {
  final TextEditingController _searchController = TextEditingController();
  late final DatabaseHelper _databaseHelper;

  List<Map<String, dynamic>> _deliveryRecords = [];
  List<Map<String, dynamic>> _filteredRecords = [];
  Map<String, CattleIsar> _cattleMap = {};
  List<dynamic> _animalTypes = [];
  bool _isLoading = true;
  String _searchQuery = '';

  // Filter variables
  String _selectedAnimalType = 'All';
  String _selectedCattleId = 'All';
  String _selectedDateRange = 'All Time';
  List<CattleIsar> _filteredCattle = [];
  Map<String, String> _animalTypeIdToName = {};

  // Stream subscription
  StreamSubscription<Map<String, dynamic>>? _deliveryStreamSubscription;

  @override
  void initState() {
    super.initState();
    _databaseHelper = DatabaseHelper.instance;
    _loadData();
    _setupStreamListener();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _deliveryStreamSubscription?.cancel();
    super.dispose();
  }

  void _setupStreamListener() {
    _deliveryStreamSubscription =
        _databaseHelper.deliveryRecordStream.listen(_handleRecordUpdate);
  }

  // Handle updates from the stream
  void _handleRecordUpdate(Map<String, dynamic> event) {
    final action = event['action'] as String?;

    if (action == 'add' || action == 'update') {
      final recordMap = event['record'] as Map<String, dynamic>?;
      if (recordMap != null) {
        _updateLocalRecord(recordMap, action);
      }
    } else if (action == 'delete') {
      final recordId = event['recordId'] as String?;
      if (recordId != null) {
        _removeLocalRecord(recordId);
      }
    }
  }

  // Update a record in the local state
  void _updateLocalRecord(Map<String, dynamic> record, String? action) {
    _safeSetState(() {
      if (action == 'add') {
        // Add if not exists
        if (!_deliveryRecords.any((r) => r['id'] == record['id'])) {
          _deliveryRecords.add(record);
        }
      } else {
        // Update existing
        final index = _deliveryRecords.indexWhere((r) => r['id'] == record['id']);
        if (index >= 0) {
          _deliveryRecords[index] = record;
        } else {
          _deliveryRecords.add(record);
        }
      }

      // Sort by date (newest first)
      _deliveryRecords.sort((a, b) {
        final aDate = DateTime.tryParse(a['date'] ?? '') ?? DateTime.now();
        final bDate = DateTime.tryParse(b['date'] ?? '') ?? DateTime.now();
        return bDate.compareTo(aDate);
      });

      // Update filtered records
      _filterRecords();
    });
  }

  // Remove a record from the local state
  void _removeLocalRecord(String recordId) {
    _safeSetState(() {
      _deliveryRecords.removeWhere((r) => r['id'] == recordId);
      _filterRecords();
    });
  }

  // Safe setState that checks if mounted
  void _safeSetState(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    }
  }

  Future<void> _loadData() async {
    _safeSetState(() => _isLoading = true);

    try {
      // Load all cattle for reference
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();
      _cattleMap = {for (var cattle in allCattle) cattle.tagId ?? '': cattle};

      // Load animal types for reference
      final animalTypesData =
          await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
      _animalTypes = animalTypesData;
      _animalTypeIdToName = {
        for (var type in animalTypesData) type.businessId ?? '': type.name ?? ''
      };

      // Load delivery records from database
      final records = await _databaseHelper.breedingHandler.getAllDeliveryRecords();

      // Sort by date (newest first)
      records.sort((a, b) {
        final aDate = DateTime.tryParse(a['date'] ?? '') ?? DateTime.now();
        final bDate = DateTime.tryParse(b['date'] ?? '') ?? DateTime.now();
        return bDate.compareTo(aDate);
      });

      // Extract unique female cattle for filtering
      _filteredCattle = allCattle
          .where((cattle) => cattle.gender?.toLowerCase() == 'female')
          .toList();

      _safeSetState(() {
        _deliveryRecords = records;
        _filteredRecords = records;
        _isLoading = false;
      });
    } catch (e) {
      _safeSetState(() => _isLoading = false);
      if (mounted) {
        BreedingMessageUtils.showError(context, _getReadableErrorMessage(e));
      }
    }
  }

  // Get user-friendly error message
  String _getReadableErrorMessage(dynamic error) {
    if (error is Exception) {
      final message = error.toString().replaceAll('Exception: ', '');
      return message.isNotEmpty
          ? message
          : 'An error occurred while loading records';
    }
    return 'Error loading delivery records: $error';
  }

  // Update filtered cattle list when animal type is selected
  void _updateFilteredCattle() {
    final allCattle = _cattleMap.values.toList();
    List<CattleIsar> newFilteredCattle;

    if (_selectedAnimalType == 'All') {
      newFilteredCattle = allCattle
          .where((cattle) => cattle.gender?.toLowerCase() == 'female')
          .toList();
    } else {
      // Filter by selected animal type name using the mapping
      newFilteredCattle = allCattle
          .where((cattle) =>
              cattle.gender?.toLowerCase() == 'female' &&
              _animalTypeIdToName[cattle.animalTypeId] == _selectedAnimalType)
          .toList();
    }

    // Update both filtered cattle list and reset cattle selection
    _safeSetState(() {
      _filteredCattle = newFilteredCattle;
      _selectedCattleId = 'All';
    });
  }

  void _clearFilters() {
    _safeSetState(() {
      _searchQuery = '';
      _searchController.clear();
      _selectedAnimalType = 'All';
      _selectedCattleId = 'All';
      _selectedDateRange = 'All Time';
      _updateFilteredCattle();
      _filterRecords();
    });
  }

  void _filterRecords() {
    // Start with all records
    List<Map<String, dynamic>> filtered = List.from(_deliveryRecords);

    // Apply animal type filter if selected
    if (_selectedAnimalType != 'All') {
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record['cattleId']];
        return cattle != null &&
            _animalTypeIdToName[cattle.animalTypeId] == _selectedAnimalType;
      }).toList();
    }

    // Apply cattle filter if selected
    if (_selectedCattleId != 'All') {
      filtered = filtered
          .where((record) => record['cattleId'] == _selectedCattleId)
          .toList();
    }

    // Apply date range filter if selected
    if (_selectedDateRange != 'All Time') {
      filtered = filtered.where((record) {
        return _isWithinDateRange(record['date'], _selectedDateRange);
      }).toList();
    }

    // Apply search filter if there's a search query
    if (_searchQuery.isNotEmpty) {
      final query = _searchQuery.toLowerCase();
      filtered = filtered.where((record) {
        final cattle = _cattleMap[record['cattleId']];
        if (cattle == null) return false;

        final cattleName = cattle.name?.toLowerCase() ?? '';
        final cattleId = record['cattleId']?.toLowerCase() ?? '';

        return cattleName.contains(query) ||
            cattleId.contains(query) ||
            (record['notes']?.toLowerCase() ?? '').contains(query);
      }).toList();
    }

    _safeSetState(() {
      _filteredRecords = filtered;
    });
  }

  bool _isWithinDateRange(String? dateStr, String range) {
    if (dateStr == null) return false;
    
    final recordDate = DateTime.tryParse(dateStr);
    if (recordDate == null) return false;

    final now = DateTime.now();
    switch (range) {
      case 'Today':
        return recordDate.isAfter(DateTime(now.year, now.month, now.day));
      case '7 Days':
        return recordDate.isAfter(now.subtract(const Duration(days: 7)));
      case '30 Days':
        return recordDate.isAfter(now.subtract(const Duration(days: 30)));
      case '90 Days':
        return recordDate.isAfter(now.subtract(const Duration(days: 90)));
      default:
        return true;
    }
  }

  Future<void> _refreshRecords() async {
    _safeSetState(() => _isLoading = true);
    await _loadData();
  }

  // Handle edit record
  Future<void> _editDeliveryRecord(Map<String, dynamic> record) async {
    final allCattle = await _databaseHelper.cattleHandler.getAllCattle();

    if (!mounted) return;
    final result = await showDialog<Map<String, dynamic>>(
      context: context,
      builder: (context) => DeliveryFormDialog(
        record: record,
        existingCattle: allCattle,
      ),
    );

    if (result != null) {
      try {
        // Handle the result from the dialog
        await _databaseHelper.breedingHandler.updateDeliveryRecord(
          result['updatedRecord']['id'],
          result['updatedRecord'],
        );

        if (mounted) {
          final message = BreedingMessageUtils.deliveryRecordUpdated();
          BreedingMessageUtils.showSuccess(context, message);
        }
      } catch (e) {
        if (mounted) {
          BreedingMessageUtils.showError(context,
              'Error updating delivery record: ${_getReadableErrorMessage(e)}');
        }
      }
    }
  }

  // Handle delete record
  Future<void> _deleteDeliveryRecord(Map<String, dynamic> record) async {
    // Find linked records that will be cascade deleted
    List<String> cascadeWarnings = [];

    try {
      // Check if this delivery record has a linked pregnancy
      if (record['pregnancyId'] != null) {
        final pregnancyId = record['pregnancyId'];
        cascadeWarnings.add('1 pregnancy record');
        cascadeWarnings.add(pregnancyId);

        // Get the pregnancy record to find linked breeding record
        final pregnancyRecords = await _databaseHelper.breedingHandler
            .getPregnancyRecordsForCattle(record['cattleId']);

        final linkedPregnancy = pregnancyRecords.firstWhere(
          (pr) => pr.businessId == pregnancyId,
          orElse: () => PregnancyRecordIsar(),
        );

        if (linkedPregnancy.breedingRecordId != null) {
          cascadeWarnings.add('1 breeding record');
          cascadeWarnings.add(linkedPregnancy.breedingRecordId!);
        }
      }
    } catch (e) {
      debugPrint('Error finding linked records: $e');
      // Continue with deletion even if we can't find linked records
    }

    // Show standardized confirmation dialog with cascade warnings
    final cattle = _cattleMap[record['cattleId']];
    final cattleDisplayName = cattle?.name != null && cattle?.tagId != null
        ? '${cattle!.name} (${cattle.tagId})'
        : cattle?.name ?? cattle?.tagId ?? 'Unknown';
    if (!mounted) return;
    final confirmed = await BreedingMessageUtils.showDeliveryDeleteConfirmation(
      context,
      cattleName: cattleDisplayName,
      recordId: record['id']?.toString(),
      specificRecords: cascadeWarnings,
    );

    if (confirmed == true) {
      try {
        // Use the centralized method to delete with cascade deletion
        await _databaseHelper.breedingHandler.deleteDeliveryRecord(record['id']);

        // Update local state immediately to ensure UI reflects the change
        _safeSetState(() {
          // Remove the record from the list
          _deliveryRecords.removeWhere((r) => r['id'] == record['id']);
          // Re-apply filters
          _filterRecords();
        });

        if (mounted) {
          final message = BreedingMessageUtils.deliveryRecordDeleted();
          BreedingMessageUtils.showSuccess(context, message);
        }
      } catch (e) {
        if (mounted) {
          BreedingMessageUtils.showError(context,
              'Error deleting delivery record: ${_getReadableErrorMessage(e)}');
        }
      }
    }
  }

  // Add new delivery record
  Future<void> _addDeliveryRecord() async {
    setState(() => _isLoading = true);

    try {
      // Get eligible cattle (pregnant female cattle)
      final eligibleCattle = await _getEligibleCattleForDelivery();

      setState(() => _isLoading = false);

      if (eligibleCattle.isEmpty) {
        // Show message if no eligible cattle
        if (mounted && context.mounted) {
          BreedingMessageUtils.showWarning(context,
              'No pregnant cattle found. Only pregnant cattle can have delivery records.');
        }
        return;
      }

      if (!mounted) return;

      // Show delivery form dialog
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();
      if (!mounted) return;
      final result = await showDialog<Map<String, dynamic>>(
        context: context,
        builder: (context) => DeliveryFormDialog(
          existingCattle: allCattle,
        ),
      );

      // Handle form result
      if (result != null && mounted) {
        await _handleNewDeliveryRecord(result);
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        BreedingMessageUtils.showError(context, BreedingMessageUtils.generalError(e.toString()));
      }
    }
  }

  // Get eligible cattle for delivery (pregnant cattle)
  Future<List<CattleIsar>> _getEligibleCattleForDelivery() async {
    final allCattle = _cattleMap.values.toList();
    final eligibleCattle = <CattleIsar>[];

    for (final cattle in allCattle) {
      // Only check female cattle that are pregnant
      if (cattle.gender?.toLowerCase() == 'female' &&
          cattle.breedingStatus?.isPregnant == true) {
        eligibleCattle.add(cattle);
      }
    }

    return eligibleCattle;
  }

  // Handle new delivery record creation
  Future<void> _handleNewDeliveryRecord(Map<String, dynamic> result) async {
    try {
      // Use the breeding handler to add the delivery record
      await _databaseHelper.breedingHandler.addDeliveryRecordFromMap(result);

      if (mounted) {
        final message = BreedingMessageUtils.deliveryRecordCreated();
        BreedingMessageUtils.showSuccess(context, message);
      }
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showError(context,
            'Error creating delivery record: ${_getReadableErrorMessage(e)}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Delivery Records',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addDeliveryRecord,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          // Search and filter section
          _buildSearchAndFilterSection(),

          // Records list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredRecords.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.child_care_outlined,
                              size: 80,
                              color: AppColors.primary.withValues(alpha: 0.4),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _searchQuery.isEmpty
                                  ? 'No delivery records found'
                                  : 'No matching records found',
                              style: TextStyle(
                                fontSize: 18,
                                color: AppColors.primary.withValues(alpha: 0.7),
                              ),
                            ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _refreshRecords,
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              _buildRecordsList(),
                              // Add padding at the bottom for the FAB
                              const SizedBox(height: 80),
                            ],
                          ),
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Search Row
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by cattle name or tag ID',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                _safeSetState(() {
                  _searchQuery = value;
                  _filterRecords();
                });
              },
            ),
          ),

          // Filter Row
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            child: Row(
              children: [
                // Animal Type Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String?>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Types'),
                        ),
                        ..._animalTypes.map((type) => PopupMenuItem(
                              value: type.name,
                              child: Text(type.name),
                            )),
                      ],
                      onSelected: (value) {
                        _safeSetState(() {
                          _selectedAnimalType = value ?? 'All';
                          _updateFilteredCattle();
                          _filterRecords();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedAnimalType,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Cattle Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String?>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Cattle'),
                        ),
                        ..._filteredCattle.map((cattle) => PopupMenuItem(
                              value: cattle.tagId,
                              child: Text('${cattle.name} (${cattle.tagId})'),
                            )),
                      ],
                      onSelected: (value) {
                        _safeSetState(() {
                          _selectedCattleId = value ?? 'All';
                          _filterRecords();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedCattleId,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Date Range Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => const [
                        PopupMenuItem(value: 'All Time', child: Text('All Time')),
                        PopupMenuItem(value: 'Today', child: Text('Today')),
                        PopupMenuItem(value: '7 Days', child: Text('7 Days')),
                        PopupMenuItem(value: '30 Days', child: Text('30 Days')),
                        PopupMenuItem(value: '90 Days', child: Text('90 Days')),
                      ],
                      onSelected: (value) {
                        _safeSetState(() {
                          _selectedDateRange = value;
                          _filterRecords();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedDateRange,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Clear Filters Button (only show when filters are applied)
          if (_selectedAnimalType != 'All' ||
              _selectedCattleId != 'All' ||
              _selectedDateRange != 'All Time' ||
              _searchQuery.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _clearFilters,
                      icon: const Icon(Icons.clear),
                      label: const Text('Clear Filters'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    '${_filteredRecords.length} of ${_deliveryRecords.length} records',
                    style: TextStyle(color: AppColors.primary.withValues(alpha: 0.7)),
                  ),
                ],
              ),
            ),
          const Divider(height: 1),
        ],
      ),
    );
  }

  Widget _buildRecordsList() {
    // Prepare records with cattle name and ID info for display
    final recordsForDisplay = _filteredRecords.map((record) {
      final cattle = _cattleMap[record['cattleId']];
      return {
        ...record,
        'cattleName': cattle?.name ?? 'Unknown Cattle',
        'cattleId': cattle?.tagId ?? 'Unknown',  // Add cattle tag ID for display
      };
    }).toList();

    return DeliveryHistoryCard(
      records: recordsForDisplay,
      title: 'Delivery Records',
      emptyMessage: 'No delivery records found',
      onEdit: _editDeliveryRecord,
      onDelete: _deleteDeliveryRecord,
    );
  }
}
