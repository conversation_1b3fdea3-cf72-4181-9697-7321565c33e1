import 'package:flutter/material.dart';
import '../../../services/database/database_helper.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/pregnancy_record_isar.dart';
import '../dialogs/pregnancy_form_dialog.dart';
import '../../Cattle/widgets/pregnancy_history_card.dart';
import '../../Cattle/widgets/eligibility_card.dart';
import '../controllers/breeding_controller.dart';
import '../../../utils/message_utils.dart';
import 'dart:async';
import '../../../constants/app_colors.dart';
import 'package:intl/intl.dart';
import 'package:collection/collection.dart'; // For firstWhereOrNull
import '../../widgets/index.dart';

class PregnancyRecordsTab extends StatelessWidget {
  final BreedingController controller;

  const PregnancyRecordsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final records = controller.pregnancyRecords;

    if (records.isEmpty) {
      return UniversalEmptyState.breeding(
        title: 'No Pregnancy Records',
        message: 'Pregnancy records will appear here when added',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: records.length,
      itemBuilder: (context, index) {
        final record = records[index];
        final cattle = controller.getCattle(record.cattleId);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: Colors.purple,
              child: const Icon(
                Icons.pregnant_woman,
                color: Colors.white,
              ),
            ),
            title: Text(cattle?.name ?? 'Unknown Cattle'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Breeding Date: ${_formatDate(record.startDate)}'),
                if (record.expectedCalvingDate != null)
                  Text('Expected Calving: ${_formatDate(record.expectedCalvingDate)}'),
                if (record.status != null)
                  Text('Status: ${record.status}'),
              ],
            ),
            trailing: Icon(
              _getStatusIcon(record.status),
              color: _getStatusColor(record.status),
            ),
          ),
        );
      },
    );
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Icons.pending;
      case 'confirmed':
        return Icons.check_circle;
      case 'completed':
        return Icons.child_care;
      case 'failed':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'completed':
        return Colors.green;
      case 'failed':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}

class PregnancyRecordsScreen extends StatefulWidget {
  const PregnancyRecordsScreen({Key? key}) : super(key: key);

  @override
  State<PregnancyRecordsScreen> createState() => _PregnancyRecordsScreenState();
}

class _PregnancyRecordsScreenState extends State<PregnancyRecordsScreen> {
  final TextEditingController _searchController = TextEditingController();
  late final DatabaseHelper _databaseHelper;

  List<PregnancyRecordIsar> _pregnancyRecords = [];
  List<PregnancyRecordIsar> _filteredRecords = [];
  Map<String, CattleIsar> _cattleMap = {};
  List<dynamic> _animalTypes = [];
  final List<String> _successfulBreedingRecordsNotTracked = [];
  bool _isLoading = true;
  String _searchQuery = '';

  // Filter variables
  String _selectedAnimalType = 'All';
  String _selectedCattleId = 'All';
  String _selectedDateRange = 'All Time';
  List<CattleIsar> _filteredCattle = [];
  Map<String, String> _animalTypeIdToName = {};

  // Stream subscription
  StreamSubscription<Map<String, dynamic>>? _pregnancyStreamSubscription;

  @override
  void initState() {
    super.initState();
    _databaseHelper = DatabaseHelper.instance;
    _loadData();
    _setupStreamListener();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _pregnancyStreamSubscription?.cancel();
    super.dispose();
  }

  void _setupStreamListener() {
    _pregnancyStreamSubscription =
        _databaseHelper.pregnancyRecordStream.listen(_handleRecordUpdate);
  }

  // Handle updates from the stream
  void _handleRecordUpdate(Map<String, dynamic> event) {
    final action = event['action'] as String?;

    if (action == 'add' || action == 'update') {
      final recordMap = event['record'] as Map<String, dynamic>?;
      if (recordMap != null) {
        final record = PregnancyRecordIsar.fromMap(recordMap);
        _updateLocalRecord(record, action);
      }
    } else if (action == 'delete') {
      final recordId = event['recordId'] as String?;
      if (recordId != null) {
        _removeLocalRecord(recordId);
      }
    }
  }

  // Update a record in the local state
  void _updateLocalRecord(PregnancyRecordIsar record, String? action) {
    _safeSetState(() {
      if (action == 'add') {
        // Add if not exists
        if (!_pregnancyRecords.any((r) => r.businessId == record.businessId)) {
          _pregnancyRecords.add(record);
        }
      } else {
        // Update existing
        final index = _pregnancyRecords
            .indexWhere((r) => r.businessId == record.businessId);
        if (index >= 0) {
          _pregnancyRecords[index] = record;
        } else {
          _pregnancyRecords.add(record);
        }
      }

      // Sort by date (newest first)
      _pregnancyRecords.sort((a, b) {
        final aDate = a.startDate ?? DateTime.now();
        final bDate = b.startDate ?? DateTime.now();
        return bDate.compareTo(aDate);
      });

      // Update filtered records
      _filterRecords();
    });
  }

  // Remove a record from the local state
  void _removeLocalRecord(String recordId) {
    _safeSetState(() {
      _pregnancyRecords.removeWhere((r) => r.businessId == recordId);
      _filterRecords();
    });
  }

  // Safe setState that checks if mounted
  void _safeSetState(VoidCallback fn) {
    if (mounted) {
      setState(fn);
    }
  }

  Future<void> _loadData() async {
    _safeSetState(() => _isLoading = true);

    try {
      // Load all cattle for reference
      final allCattle = await _databaseHelper.cattleHandler.getAllCattle();
      _cattleMap = {for (var cattle in allCattle) cattle.tagId ?? '': cattle};

      // Load animal types for reference
      final animalTypesData =
          await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
      _animalTypes = animalTypesData;
      _animalTypeIdToName = {
        for (var type in animalTypesData) type.businessId ?? '': type.name ?? ''
      };

      // Load pregnancy records from database
      final records =
          await _databaseHelper.breedingHandler.getAllPregnancyRecords();

      // Sort by date (newest first)
      records.sort((a, b) {
        final aDate = a.startDate ?? DateTime.now();
        final bDate = b.startDate ?? DateTime.now();
        return bDate.compareTo(aDate);
      });

      // Extract unique animal types
      _filteredCattle = allCattle
          .where((cattle) => cattle.gender?.toLowerCase() == 'female')
          .toList();

      // Check for successful breeding records that aren't tracked in pregnancy yet
      await _checkSuccessfulBreedingRecords();

      _safeSetState(() {
        _pregnancyRecords = records;
        _filteredRecords = records;
        _isLoading = false;
      });
    } catch (e) {
      _safeSetState(() => _isLoading = false);
      if (mounted) {
        BreedingMessageUtils.showError(context, _getReadableErrorMessage(e));
      }
    }
  }

  // Get user-friendly error message
  String _getReadableErrorMessage(dynamic error) {
    if (error is Exception) {
      final message = error.toString().replaceAll('Exception: ', '');
      return message.isNotEmpty
          ? message
          : 'An error occurred while loading records';
    }
    return 'Error loading pregnancy records: $error';
  }

  Future<void> _checkSuccessfulBreedingRecords() async {
    try {
      // Get all breeding records with "Completed" status that don't have pregnancy records
      final breedingRecords = await _databaseHelper.breedingHandler
          .getBreedingRecordsForCattle('all');
      final completeRecords = breedingRecords
          .where((record) => record.status == 'Completed')
          .toList();

      // Get all pregnancy records
      final allPregnancyRecords =
          await _databaseHelper.breedingHandler.getAllPregnancyRecords();

      // Find breeding records without associated pregnancy records
      final unlinkedBreedingIds = <String>[];

      for (final breedingRecord in completeRecords) {
        final breedingId = breedingRecord.businessId;

        if (breedingId != null &&
            !allPregnancyRecords
                .any((pr) => pr.breedingRecordId == breedingId)) {
          unlinkedBreedingIds.add(breedingId);
        }
      }

      _safeSetState(() {
        _successfulBreedingRecordsNotTracked.clear();
        _successfulBreedingRecordsNotTracked.addAll(unlinkedBreedingIds);
      });
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showError(context,
            'Error checking breeding records: ${_getReadableErrorMessage(e)}');
      }
    }
  }

  // Convert PregnancyRecordIsar to Map for history card
  Map<String, dynamic> _recordToMap(PregnancyRecordIsar record) {
    final cattle = _cattleMap[record.cattleId];
    return {
      'id': record.businessId,
      'cattleId': cattle?.tagId ?? 'Unknown',  // Use cattle tag ID for display
      'cattleName': cattle?.name ?? 'Unknown Cattle',
      'startDate': record.startDate?.toIso8601String(),
      'status': record.status,
      'expectedCalvingDate': record.expectedCalvingDate?.toIso8601String(),
      'actualCalvingDate': record.actualCalvingDate?.toIso8601String(),
      'breedingRecordId': record.breedingRecordId,
      'notes': record.notes,
    };
  }

  // Update filtered cattle list when animal type is selected
  void _updateFilteredCattle() {
    final allCattle = _cattleMap.values.toList();

    if (_selectedAnimalType == 'All') {
      _filteredCattle = allCattle
          .where((cattle) => cattle.gender?.toLowerCase() == 'female')
          .toList();
    } else {
      // Find animal type ID
      dynamic selectedType = {'businessId': '', 'name': 'Unknown'};

      try {
        selectedType = _animalTypes.firstWhere(
          (type) => type.name == _selectedAnimalType,
        );
      } catch (_) {
        // If not found, use the default type
      }

      final animalTypeId = selectedType['businessId'];

      // Filter cattle by animal type ID
      if (animalTypeId != null && animalTypeId.isNotEmpty) {
        _filteredCattle = allCattle
            .where((cattle) =>
                cattle.gender?.toLowerCase() == 'female' &&
                cattle.animalTypeId == animalTypeId)
            .toList();
      } else {
        _filteredCattle = [];
      }
    }

    // Update cattle dropdown
    _safeSetState(() {
      _selectedCattleId = 'All';
    });
  }

  void _clearFilters() {
    _safeSetState(() {
      _searchQuery = '';
      _searchController.clear();
      _selectedAnimalType = 'All';
      _selectedCattleId = 'All';
      _selectedDateRange = 'All Time';
      _updateFilteredCattle();
      _filterRecords();
    });
  }

  void _filterRecords() {
    final query = _searchQuery.toLowerCase();
    final List<PregnancyRecordIsar> filtered = [];

    for (final record in _pregnancyRecords) {
      // Get the cattle info
      final cattle = _cattleMap[record.cattleId];
      final cattleName = cattle?.name?.toLowerCase() ?? '';
      final cattleId = record.cattleId?.toLowerCase() ?? '';

      // Apply filters
      bool matchesAnimalType = _selectedAnimalType == 'All' ||
          _animalTypeIdToName[cattle?.animalTypeId ?? ''] ==
              _selectedAnimalType;

      bool matchesCattle =
          _selectedCattleId == 'All' || record.cattleId == _selectedCattleId;



      bool matchesDateRange = _selectedDateRange == 'All Time' ||
          _isWithinDateRange(record.startDate, _selectedDateRange);

      bool matchesSearch = query.isEmpty ||
          cattleName.contains(query) ||
          cattleId.contains(query) ||
          (record.notes?.toLowerCase() ?? '').contains(query);

      if (matchesAnimalType &&
          matchesCattle &&
          matchesDateRange &&
          matchesSearch) {
        filtered.add(record);
      }
    }

    _safeSetState(() {
      _filteredRecords = filtered;
    });
  }

  bool _isWithinDateRange(DateTime? recordDate, String range) {
    if (recordDate == null) return false;

    final now = DateTime.now();
    switch (range) {
      case 'Today':
        return recordDate.isAfter(DateTime(now.year, now.month, now.day));
      case '7 Days':
        return recordDate.isAfter(now.subtract(const Duration(days: 7)));
      case '30 Days':
        return recordDate.isAfter(now.subtract(const Duration(days: 30)));
      case '90 Days':
        return recordDate.isAfter(now.subtract(const Duration(days: 90)));
      default:
        return true;
    }
  }

  // Change the status of a pregnancy record
  Future<void> _changePregnancyStatus(
      PregnancyRecordIsar record, String newStatus) async {
    try {
      // Use the centralized method to update status
      await _databaseHelper.breedingHandler
          .updatePregnancyStatus(record, newStatus);

      if (mounted) {
        BreedingMessageUtils.showSuccess(context,
            'Pregnancy record status updated to $newStatus');
      }
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showError(context,
            'Error updating pregnancy record status: ${_getReadableErrorMessage(e)}');
      }
    }
  }

  Future<void> _refreshRecords() async {
    _safeSetState(() => _isLoading = true);
    await _loadData();
  }

  // Handle edit record
  Future<void> _editPregnancyRecord(PregnancyRecordIsar record) async {
    final result = await showDialog<PregnancyRecordIsar>(
      context: context,
      builder: (context) => PregnancyFormDialog(record: record),
    );

    if (result != null) {
      try {
        // Use the centralized method to manage the record
        await _databaseHelper.breedingHandler.managePregnancyRecord(result);

        if (mounted) {
          final message = BreedingMessageUtils.pregnancyRecordUpdated();
          BreedingMessageUtils.showSuccess(context, message);
        }
      } catch (e) {
        if (mounted) {
          BreedingMessageUtils.showError(context,
              'Error updating pregnancy record: ${_getReadableErrorMessage(e)}');
        }
      }
    }
  }

  // Handle delete record
  Future<void> _deletePregnancyRecord(PregnancyRecordIsar record) async {
    // Check for associated delivery records
    final deliveryRecords = await _databaseHelper.breedingHandler
        .getDeliveryRecordsForCattle(record.cattleId ?? '');
    final associatedDelivery = deliveryRecords.firstWhereOrNull(
      (dr) => dr['pregnancyId'] == record.businessId,
    );

    // Check for associated breeding record
    final breedingRecords = await _databaseHelper.breedingHandler
        .getBreedingRecordsForCattle(record.cattleId ?? '');
    final associatedBreeding = breedingRecords.firstWhereOrNull(
      (br) => br.businessId == record.breedingRecordId,
    );

    // Build specific records list for cascade warnings
    List<String> specificRecords = [];
    int deliveryCount = 0;
    int breedingCount = 0;

    if (associatedDelivery != null) {
      deliveryCount = 1;
      final deliveryId = associatedDelivery['id']?.toString();
      if (deliveryId != null) {
        specificRecords.add(deliveryId);
      }
    }

    if (associatedBreeding != null) {
      breedingCount = 1;
      final breedingId = associatedBreeding.businessId;
      if (breedingId != null) {
        specificRecords.add(breedingId);
      }
    }

    // Show standardized confirmation dialog
    final cattle = _cattleMap[record.cattleId];
    final cattleDisplayName = cattle?.name != null && cattle?.tagId != null
        ? '${cattle!.name} (${cattle.tagId})'
        : cattle?.name ?? cattle?.tagId ?? 'Unknown';
    if (!mounted) return;

    final confirmed = await BreedingMessageUtils.showPregnancyDeleteConfirmation(
      context,
      cattleName: cattleDisplayName,
      recordId: record.businessId,
      deliveryRecords: deliveryCount,
      breedingRecords: breedingCount,
      specificRecords: specificRecords,
    );

    if (confirmed == true) {
      try {
        // Use the centralized method to delete with cascading updates
        await _databaseHelper.breedingHandler
            .deletePregnancyRecord(record.businessId!);

        if (mounted) {
          final message = BreedingMessageUtils.pregnancyRecordDeleted();
          BreedingMessageUtils.showSuccess(context, message);
        }
      } catch (e) {
        if (mounted) {
          BreedingMessageUtils.showError(context,
              'Error deleting pregnancy record: ${_getReadableErrorMessage(e)}');
        }
      }
    }
  }

  // Hybrid approach: Pre-filter + Dialog selection + Detailed error messages
  Future<void> _addPregnancyRecord() async {
    setState(() => _isLoading = true);

    try {
      // 1. First, get eligible cattle (pre-filter with comprehensive checking)
      final eligibleCattle = await _getEligibleCattleWithDetails();

      setState(() => _isLoading = false);

      if (eligibleCattle.isEmpty) {
        // 2. If no eligible cattle, show detailed explanation
        await _showNoEligibleCattleDialog();
        return;
      }

      if (!mounted) return;

      CattleIsar? selectedCattle;

      // 3. If only one eligible cattle, go directly to form
      if (eligibleCattle.length == 1) {
        selectedCattle = eligibleCattle.first;
      } else {
        // 4. If multiple eligible cattle, show selection dialog
        selectedCattle = await _showEligibleCattleSelectionDialog(eligibleCattle);
        if (selectedCattle == null) return; // User cancelled
      }

      // 5. Double-check eligibility before opening form (safety check)
      final finalEligibilityCheck = await _checkPregnancyEligibility(selectedCattle);
      if (!finalEligibilityCheck.isEligible) {
        if (mounted) {
          await _showEligibilityErrorDialog(selectedCattle, finalEligibilityCheck);
        }
        return;
      }

      // 6. Open pregnancy form with selected cattle
      if (!mounted) return;
      final result = await showDialog<PregnancyRecordIsar>(
        context: context,
        builder: (context) => PregnancyFormDialog(
          initialCattleId: selectedCattle?.tagId,
        ),
      );

      // 7. Handle form result
      if (result != null && mounted) {
        await _handleNewPregnancyRecord(result);
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        BreedingMessageUtils.showError(context, BreedingMessageUtils.generalError(e.toString()));
      }
    }
  }

  // Comprehensive method to get eligible cattle with detailed eligibility checking
  Future<List<CattleIsar>> _getEligibleCattleWithDetails() async {
    final allCattle = _cattleMap.values.toList();
    final eligibleCattle = <CattleIsar>[];

    for (final cattle in allCattle) {
      // Only check female cattle
      if (cattle.gender?.toLowerCase() == 'female') {
        // Use comprehensive eligibility checking
        final eligibilityResult = await _checkPregnancyEligibility(cattle);

        if (eligibilityResult.isEligible) {
          eligibleCattle.add(cattle);
        }
      }
    }

    return eligibleCattle;
  }

  // Show detailed dialog when no eligible cattle found
  Future<void> _showNoEligibleCattleDialog() async {
    final allFemales = _cattleMap.values
        .where((c) => c.gender?.toLowerCase() == 'female')
        .toList();

    if (allFemales.isEmpty) {
      if (mounted) {
        BreedingMessageUtils.showWarning(context, 'No female cattle found');
      }
      return;
    }

    // Check each female cattle and categorize ineligibility reasons
    final reasons = <String, List<String>>{};
    for (final cattle in allFemales) {
      final eligibility = await _checkPregnancyEligibility(cattle);
      if (!eligibility.isEligible) {
        final reason = eligibility.reasonMessage;
        if (!reasons.containsKey(reason)) {
          reasons[reason] = [];
        }
        reasons[reason]!.add('${cattle.name ?? 'Unknown'} (${cattle.tagId ?? 'Unknown'})');
      }
    }

    if (!mounted) return;

    // Show detailed dialog with breakdown of why cattle aren't eligible
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        titlePadding: const EdgeInsets.all(16),
        contentPadding: const EdgeInsets.all(16),
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Row(
          children: [
            Icon(
              Icons.info_outline,
              color: Color(0xFF2196F3),
              size: 24,
            ),
            SizedBox(width: 12),
            Flexible(
              child: Text(
                'No Eligible Cattle',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'None of your ${allFemales.length} female cattle are currently eligible for pregnancy records:',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...reasons.entries.map((entry) => Padding(
              padding: const EdgeInsets.only(bottom: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '• ${entry.key}',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 16, top: 4),
                    child: Text(
                      'Cattle: ${entry.value.join(", ")}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF2E7D32),
            ),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Show cattle selection dialog for multiple eligible cattle
  Future<CattleIsar?> _showEligibleCattleSelectionDialog(List<CattleIsar> eligibleCattle) async {
    return await showDialog<CattleIsar>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Cattle for Pregnancy Record'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: eligibleCattle.length,
            itemBuilder: (context, index) {
              final cattle = eligibleCattle[index];
              return ListTile(
                title: Text(cattle.name ?? 'Unknown'),
                subtitle: Text('Tag ID: ${cattle.tagId ?? 'Unknown'}'),
                trailing: const Icon(Icons.check_circle, color: Color(0xFF4CAF50)),
                onTap: () => Navigator.of(context).pop(cattle),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  // Check pregnancy eligibility for selected cattle
  Future<EligibilityCheckResult> _checkPregnancyEligibility(CattleIsar cattle) async {
    // First check for pending breeding records
    final breedingRecords = await _databaseHelper.breedingHandler
        .getBreedingRecordsForCattle(cattle.tagId ?? '')
        .then((records) => records.map((record) => record.toMap()).toList());

    // Check if there's a pending breeding record
    bool hasPendingBreeding = false;
    if (breedingRecords.isNotEmpty) {
      hasPendingBreeding = breedingRecords.any(
          (record) => record['status']?.toString().toLowerCase() == 'pending');
    }

    // If there's a pending breeding record, return not eligible
    if (hasPendingBreeding) {
      // Find the pending breeding record to get its date
      final pendingRecord = breedingRecords.firstWhere(
          (record) => record['status']?.toString().toLowerCase() == 'pending');
      final breedingDate = DateTime.parse(
          pendingRecord['date'] ?? DateTime.now().toIso8601String());
      final formattedDate = DateFormat('MMMM dd, yyyy').format(breedingDate);

      return EligibilityCheckResult(
        isEligible: false,
        statusMessage: 'Pending Breeding Record',
        reasonMessage:
            'Breeding attempt on $formattedDate is still pending confirmation',
        statusColor: const Color(0xFF2196F3), // Blue
        statusIcon: Icons.pending,
        nextEligibleDateMessage:
            'Please update the pending breeding record before adding a pregnancy record',
      );
    }

    // Check if cattle is already pregnant
    final isPregnant = cattle.breedingStatus?.isPregnant ?? false;

    // Get animal type settings for empty period
    final animalTypes = await _databaseHelper.farmSetupHandler.getAllAnimalTypes();
    final animalType = animalTypes.isNotEmpty
        ? animalTypes.firstWhere(
            (type) => type.businessId == cattle.animalTypeId,
            orElse: () => animalTypes.first,
          )
        : null;

    // Otherwise, use the standard eligibility check
    return EligibilityCard.checkPregnancyEligibility(
      gender: cattle.gender ?? '',
      cattleId: cattle.tagId ?? '',
      isPregnant: isPregnant,
      dateOfBirth: cattle.dateOfBirth,
      animalTypeId: cattle.animalTypeId ?? '',
      purchaseDate: cattle.purchaseDate,
      lastCalvingDate: cattle.breedingStatus?.lastCalvingDate,
      animalTypeEmptyPeriodDays: animalType?.defaultEmptyPeriodDays,
    );
  }

  // Show detailed eligibility error dialog
  Future<void> _showEligibilityErrorDialog(CattleIsar cattle, EligibilityCheckResult eligibilityResult) async {
    if (!mounted) return;
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        titlePadding: const EdgeInsets.all(16),
        contentPadding: const EdgeInsets.all(16),
        insetPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 24),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Row(
          children: [
            Icon(
              eligibilityResult.statusIcon,
              color: eligibilityResult.statusColor,
              size: 24,
            ),
            const SizedBox(width: 12),
            Flexible(
              child: Text(
                eligibilityResult.statusMessage,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Cattle: ${cattle.name ?? 'Unknown'} (${cattle.tagId ?? 'Unknown'})',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            const Text(
              'Cannot add pregnancy record because:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(eligibilityResult.reasonMessage),
            if (eligibilityResult.nextEligibleDateMessage != null) ...[
              const SizedBox(height: 12),
              Text(
                eligibilityResult.nextEligibleDateMessage!,
                style: const TextStyle(fontStyle: FontStyle.italic),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            style: TextButton.styleFrom(
              foregroundColor: const Color(0xFF2E7D32),
            ),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Handle new pregnancy record creation
  Future<void> _handleNewPregnancyRecord(PregnancyRecordIsar newRecord) async {
    try {
      // Use the centralized method to manage the new record
      await _databaseHelper.breedingHandler.managePregnancyRecord(newRecord);

      if (mounted) {
        final message = BreedingMessageUtils.pregnancyRecordCreated();
        BreedingMessageUtils.showSuccess(context, message);
      }
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showError(context,
            'Error adding pregnancy record: ${_getReadableErrorMessage(e)}');
      }
    }
  }

  Future<void> _createAllMissingPregnancyRecords() async {
    if (_successfulBreedingRecordsNotTracked.isEmpty) return;

    try {
      for (final breedingId in _successfulBreedingRecordsNotTracked) {
        await _databaseHelper.breedingHandler
            .createPregnancyFromBreeding(breedingId);
      }

      _safeSetState(() {
        _successfulBreedingRecordsNotTracked.clear();
      });

      if (mounted) {
        BreedingMessageUtils.showSuccess(context, 'All pregnancy records created successfully');
      }
    } catch (e) {
      if (mounted) {
        BreedingMessageUtils.showError(context,
            'Error creating pregnancy records: ${_getReadableErrorMessage(e)}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Pregnancy Records',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addPregnancyRecord,
        backgroundColor: AppColors.primary,
        child: const Icon(Icons.add),
      ),
      body: Column(
        children: [
          // Search and filter section
          _buildSearchAndFilterSection(),

          // Banner for successful breeding records
          if (!_isLoading) _buildBreedingRecordsBanner(),

          // Records list
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _filteredRecords.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.pets,
                              size: 80,
                              color: AppColors.primary.withValues(alpha: 0.4),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              _searchQuery.isEmpty &&
                                      _selectedAnimalType == 'All' &&
                                      _selectedCattleId == 'All'
                                  ? 'No pregnancy records found'
                                  : 'No matching records found',
                              style: TextStyle(
                                fontSize: 18,
                                color: AppColors.primary.withValues(alpha: 0.7),
                              ),
                            ),
                            if (_searchQuery.isNotEmpty ||
                                _selectedAnimalType != 'All' ||
                                _selectedCattleId != 'All')
                              TextButton(
                                onPressed: _clearFilters,
                                child: const Text('Clear Filters'),
                              ),
                          ],
                        ),
                      )
                    : RefreshIndicator(
                        onRefresh: _refreshRecords,
                        child: SingleChildScrollView(
                          physics: const AlwaysScrollableScrollPhysics(),
                          padding: const EdgeInsets.all(16.0),
                          child: Column(
                            children: [
                              _buildRecordsList(),
                              // Add padding at the bottom for the FAB
                              const SizedBox(height: 80),
                            ],
                          ),
                        ),
                      ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Search Row
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by cattle name or tag ID',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                _safeSetState(() {
                  _searchQuery = value;
                  _filterRecords();
                });
              },
            ),
          ),

          // Filter Row
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            child: Row(
              children: [
                // Animal Type Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Types'),
                        ),
                        ..._animalTypes.map((type) => PopupMenuItem(
                              value: type.name ?? '',
                              child: Text(type.name ?? ''),
                            )),
                      ],
                      onSelected: (value) {
                        _safeSetState(() {
                          _selectedAnimalType = value;
                          _updateFilteredCattle();
                          _filterRecords();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedAnimalType,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Cattle Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Cattle'),
                        ),
                        ..._filteredCattle.map((cattle) => PopupMenuItem(
                              value: cattle.tagId ?? '',
                              child: Text(
                                  '${cattle.name ?? ''} (${cattle.tagId ?? ''})'),
                            )),
                      ],
                      onSelected: (value) {
                        _safeSetState(() {
                          _selectedCattleId = value;
                          _filterRecords();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedCattleId != 'All'
                                    ? '${_cattleMap[_selectedCattleId]?.name ?? ''} ($_selectedCattleId)'
                                    : 'All Cattle',
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),

                const SizedBox(width: 8),

                // Date Range Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => const [
                        PopupMenuItem(
                          value: 'All Time',
                          child: Text('All Time'),
                        ),
                        PopupMenuItem(
                          value: 'Today',
                          child: Text('Today'),
                        ),
                        PopupMenuItem(
                          value: '7 Days',
                          child: Text('Last 7 Days'),
                        ),
                        PopupMenuItem(
                          value: '30 Days',
                          child: Text('Last 30 Days'),
                        ),
                        PopupMenuItem(
                          value: '90 Days',
                          child: Text('Last 90 Days'),
                        ),
                      ],
                      onSelected: (value) {
                        _safeSetState(() {
                          _selectedDateRange = value;
                          _filterRecords();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedDateRange,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Clear Filters Button (only show when filters are applied)
          if (_selectedAnimalType != 'All' ||
              _selectedCattleId != 'All' ||
              _selectedDateRange != 'All Time' ||
              _searchQuery.isNotEmpty)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: _clearFilters,
                      icon: const Icon(Icons.clear),
                      label: const Text('Clear Filters'),
                    ),
                  ),
                  const SizedBox(width: 16),
                  Text(
                    '${_filteredRecords.length} of ${_pregnancyRecords.length} records',
                    style: TextStyle(color: AppColors.primary.withValues(alpha: 0.7)),
                  ),
                ],
              ),
            ),
          const Divider(height: 1),
        ],
      ),
    );
  }

  Widget _buildRecordsList() {
    // Prepare records with cattle name info for display
    final recordsForDisplay = _filteredRecords.map(_recordToMap).toList();

    return PregnancyHistoryCard(
      records: recordsForDisplay,
      title: 'Pregnancy Records',
      emptyMessage: 'No pregnancy records found',
      onEdit: (record) => _editPregnancyRecord(_getRecordFromMap(record)),
      onDelete: (record) => _deletePregnancyRecord(_getRecordFromMap(record)),
      onStatusTap: (record) =>
          _changePregnancyStatus(_getRecordFromMap(record), record['status']),
    );
  }

  // Helper method to convert a Map back to PregnancyRecordIsar
  PregnancyRecordIsar _getRecordFromMap(Map<String, dynamic> map) {
    // Find the original record in our list
    try {
      return _pregnancyRecords.firstWhere(
        (record) => record.businessId == map['id'],
      );
    } catch (_) {
      // If not found, create a new record from the map data
      return PregnancyRecordIsar.fromMap(map);
    }
  }

  // Build banner for breeding records if any are available to convert
  Widget _buildBreedingRecordsBanner() {
    if (_successfulBreedingRecordsNotTracked.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue.shade700),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  'Completed Breeding Records Found',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${_successfulBreedingRecordsNotTracked.length} completed breeding records not tracked as pregnancies',
            style: TextStyle(color: Colors.blue.shade900),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _createAllMissingPregnancyRecords,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue.shade700,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('Add All to Pregnancy Records'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
