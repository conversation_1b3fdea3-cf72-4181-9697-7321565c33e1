import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import '../models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Farm Setup/models/currency_settings_isar.dart';
import '../services/cattle_handler.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../../widgets/filters/filter_data_service.dart';


/// Controller state enum
enum ControllerState { initial, loading, loaded, error, empty }

class CattleController extends ChangeNotifier {
  final CattleHandler _cattleHandler = GetIt.instance<CattleHandler>();
  final FarmSetupHandler _farmSetupHandler = GetIt.instance<FarmSetupHandler>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;
  
  // Data
  List<CattleIsar> _cattle = [];
  List<AnimalTypeIsar> _animalTypes = [];
  List<BreedCategoryIsar> _breeds = [];
  CurrencySettingsIsar? _currencySettings;

  // Business ID - Generate unique ID for this controller instance
  final String businessId = const Uuid().v4();

  // Cached analytics - calculated once after data loads
  late int _totalCattle;
  late int _maleCattle;
  late int _femaleCattle;
  late Map<String, int> _cattleByType;
  late Map<String, int> _cattleByGender;
  late double _averageAge;
  late double _oldestCattleAge;
  late double _youngestCattleAge;
  late Map<String, int> _ageDistribution;
  late double _herdValue;
  late double _averageValue;
  late double _totalInvestment;
  late double _activeCattleValue;
  late double _soldCattleValue;
  late double _averageActiveCattleValue;
  late int _calfCount;
  late int _youngStockCount;
  late int _matureStockCount;
  late int _seniorStockCount;
  late int _activeCattleCount;
  late int _soldCattleCount;
  late int _deadCattleCount;
  late String _youngStockPercentage;
  late String _matureStockPercentage;
  late String _seniorStockPercentage;
  late String _calfPercentage;
  late String _genderRatio;
  late Map<String, int> _breedDistribution;

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  List<CattleIsar> get cattle => List.unmodifiable(_cattle);
  List<AnimalTypeIsar> get animalTypes => List.unmodifiable(_animalTypes);
  List<BreedCategoryIsar> get breeds => List.unmodifiable(_breeds);
  String get currencySymbol => _currencySettings?.currencySymbol ?? '\$';
  
  // Analytics data - now returns cached values for performance
  int get totalCattle => _totalCattle;
  int get maleCattle => _maleCattle;
  int get femaleCattle => _femaleCattle;
  Map<String, int> get cattleByType => _cattleByType;
  Map<String, int> get cattleByGender => _cattleByGender;

  // Age-related analytics - now returns cached values for performance
  double get averageAge => _averageAge;
  double get oldestCattleAge => _oldestCattleAge;
  double get youngestCattleAge => _youngestCattleAge;

  Map<String, int> get ageDistribution => _ageDistribution;

  // Financial analytics - now returns cached values for performance
  double get herdValue => _herdValue;
  double get averageValue => _averageValue;
  double get totalInvestment => _totalInvestment;

  // Additional financial metrics - now returns cached values for performance
  double get activeCattleValue => _activeCattleValue;
  double get soldCattleValue => _soldCattleValue;
  double get averageActiveCattleValue => _averageActiveCattleValue;

  // Age demographics - now returns cached values for performance
  int get calfCount => _calfCount;
  int get youngStockCount => _youngStockCount;
  int get matureStockCount => _matureStockCount;
  int get seniorStockCount => _seniorStockCount;
  int get activeCattleCount => _activeCattleCount;
  int get soldCattleCount => _soldCattleCount;
  int get deadCattleCount => _deadCattleCount;

  // Age percentage calculations - now returns cached values for performance
  String get youngStockPercentage => _youngStockPercentage;
  String get matureStockPercentage => _matureStockPercentage;
  String get seniorStockPercentage => _seniorStockPercentage;
  String get calfPercentage => _calfPercentage;

  // Gender ratio calculation - now returns cached values for performance
  String get genderRatio => _genderRatio;

  // Breed distribution calculation - now returns cached values for performance
  Map<String, int> get breedDistribution => _breedDistribution;

  // Helper method to calculate age for a cattle
  double? _calculateCattleAge(CattleIsar cattle) {
    final now = DateTime.now();

    if (cattle.dateOfBirth != null) {
      final age = now.difference(cattle.dateOfBirth!);
      return age.inDays / 365.25;
    } else if (cattle.purchaseDate != null) {
      final estimatedBirthDate = cattle.purchaseDate!.subtract(const Duration(days: 365));
      final age = now.difference(estimatedBirthDate);
      return age.inDays / 365.25;
    }

    return null;
  }

  // Initialize and load data
  Future<void> loadData() async {
    try {
      _setState(ControllerState.loading);

      await Future.wait([
        _loadCattle(),
        _loadAnimalTypes(),
        _loadBreeds(),
        _loadCurrencySettings(),
      ]);

      // Calculate all analytics once after data loads
      _calculateAllAnalytics();

      _setState(ControllerState.loaded);
    } catch (e, stackTrace) {
      debugPrint('Error loading cattle data: $e\n$stackTrace');
      _setError('Failed to load cattle data: ${e.toString()}');
    }
  }

  Future<void> refresh() async {
    await loadData();
  }

  Future<void> _loadCattle() async {
    try {
      final cattleData = await _cattleHandler.getAllCattle();
      _cattle = cattleData;
    } catch (e) {
      debugPrint('Error loading cattle: $e');
      throw Exception('Failed to load cattle records');
    }
  }

  Future<void> _loadAnimalTypes() async {
    try {
      final animalTypesData = await _farmSetupHandler.getAllAnimalTypes();
      _animalTypes = animalTypesData;
    } catch (e) {
      debugPrint('Error loading animal types: $e');
      throw Exception('Failed to load animal types');
    }
  }

  Future<void> _loadBreeds() async {
    try {
      final breedsData = await _farmSetupHandler.getAllBreedCategories();
      _breeds = breedsData;
    } catch (e) {
      debugPrint('Error loading breeds: $e');
      throw Exception('Failed to load breeds');
    }
  }

  Future<void> _loadCurrencySettings() async {
    try {
      final currencyData = await _farmSetupHandler.getCurrencySettings();
      _currencySettings = currencyData;
    } catch (e) {
      debugPrint('Error loading currency settings: $e');
      // Don't throw exception for currency settings, use default
      _currencySettings = null;
    }
  }

  /// Calculate all analytics once and cache them for performance
  void _calculateAllAnalytics() {
    // Basic counts
    _totalCattle = _cattle.length;
    _maleCattle = _cattle.where((c) => c.gender?.toLowerCase() == 'male').length;
    _femaleCattle = _cattle.where((c) => c.gender?.toLowerCase() == 'female').length;

    // Type and gender distributions
    _cattleByType = {};
    for (final cattle in _cattle) {
      final type = _getAnimalTypeName(cattle.animalTypeId) ?? 'Unknown';
      _cattleByType[type] = (_cattleByType[type] ?? 0) + 1;
    }

    _cattleByGender = {};
    for (final cattle in _cattle) {
      final gender = cattle.gender ?? 'Unknown';
      _cattleByGender[gender] = (_cattleByGender[gender] ?? 0) + 1;
    }

    // Age calculations
    final ages = _cattle
        .map((cattle) => _calculateCattleAge(cattle))
        .where((age) => age != null)
        .cast<double>()
        .toList();

    if (ages.isEmpty) {
      _averageAge = 0.0;
      _oldestCattleAge = 0.0;
      _youngestCattleAge = 0.0;
    } else {
      _averageAge = ages.reduce((a, b) => a + b) / ages.length;
      _oldestCattleAge = ages.reduce((a, b) => a > b ? a : b);
      _youngestCattleAge = ages.reduce((a, b) => a < b ? a : b);
    }

    // Age distribution
    _ageDistribution = {
      '0-6 months': 0,
      '6 months-2 years': 0,
      '2-8 years': 0,
      '8+ years': 0,
    };

    for (final cattle in _cattle) {
      final age = _calculateCattleAge(cattle);
      if (age != null) {
        if (age < 0.5) {
          _ageDistribution['0-6 months'] = _ageDistribution['0-6 months']! + 1;
        } else if (age >= 0.5 && age < 2.0) {
          _ageDistribution['6 months-2 years'] = _ageDistribution['6 months-2 years']! + 1;
        } else if (age >= 2.0 && age < 8.0) {
          _ageDistribution['2-8 years'] = _ageDistribution['2-8 years']! + 1;
        } else {
          _ageDistribution['8+ years'] = _ageDistribution['8+ years']! + 1;
        }
      }
    }

    // Financial calculations
    _herdValue = _cattle.fold(0.0, (sum, cattle) => sum + (cattle.purchasePrice ?? 0.0));
    _averageValue = _totalCattle == 0 ? 0.0 : _herdValue / _totalCattle;
    _totalInvestment = _herdValue; // Same as herd value

    _activeCattleValue = _cattle
        .where((cattle) => cattle.status?.toLowerCase() == 'active')
        .fold(0.0, (sum, cattle) => sum + (cattle.purchasePrice ?? 0.0));

    _soldCattleValue = _cattle
        .where((cattle) => cattle.status?.toLowerCase() == 'sold')
        .fold(0.0, (sum, cattle) => sum + (cattle.purchasePrice ?? 0.0));

    // Status counts
    _activeCattleCount = _cattle.where((cattle) => cattle.status?.toLowerCase() == 'active').length;
    _soldCattleCount = _cattle.where((cattle) => cattle.status?.toLowerCase() == 'sold').length;
    _deadCattleCount = _cattle.where((cattle) => cattle.status?.toLowerCase() == 'dead').length;

    _averageActiveCattleValue = _activeCattleCount == 0 ? 0.0 : _activeCattleValue / _activeCattleCount;

    // Age demographics
    _calfCount = _cattle.where((cattle) {
      final age = _calculateCattleAge(cattle);
      return age != null && age < 0.5; // 0-6 months
    }).length;

    _youngStockCount = _cattle.where((cattle) {
      final age = _calculateCattleAge(cattle);
      return age != null && age >= 0.5 && age < 2.0; // 6 months to 2 years
    }).length;

    _matureStockCount = _cattle.where((cattle) {
      final age = _calculateCattleAge(cattle);
      return age != null && age >= 2.0 && age < 8.0; // 2-8 years
    }).length;

    _seniorStockCount = _cattle.where((cattle) {
      final age = _calculateCattleAge(cattle);
      return age != null && age >= 8.0; // 8+ years
    }).length;

    // Percentage calculations
    _youngStockPercentage = _totalCattle == 0 ? '0' : ((_youngStockCount / _totalCattle) * 100).toStringAsFixed(1);
    _matureStockPercentage = _totalCattle == 0 ? '0' : ((_matureStockCount / _totalCattle) * 100).toStringAsFixed(1);
    _seniorStockPercentage = _totalCattle == 0 ? '0' : ((_seniorStockCount / _totalCattle) * 100).toStringAsFixed(1);
    _calfPercentage = _totalCattle == 0 ? '0' : ((_calfCount / _totalCattle) * 100).toStringAsFixed(1);

    // Gender ratio
    if (_totalCattle == 0 || _femaleCattle == 0) {
      _genderRatio = 'N/A';
    } else {
      final ratio = _maleCattle / _femaleCattle;
      _genderRatio = '${ratio.toStringAsFixed(1)}:1';
    }

    // Breed distribution
    _breedDistribution = {};
    for (final cattle in _cattle) {
      final breedId = cattle.breedId;
      String breedName = 'Unknown';

      if (breedId != null) {
        final breed = _breeds.firstWhere(
          (b) => b.businessId == breedId,
          orElse: () => BreedCategoryIsar(),
        );
        breedName = breed.name ?? 'Unknown';
      }

      _breedDistribution[breedName] = (_breedDistribution[breedName] ?? 0) + 1;
    }
  }

  // Add new cattle
  Future<void> addCattle(CattleIsar cattle) async {
    try {
      await _cattleHandler.addCattle(cattle);
      _cattle.add(cattle);
      _cattle.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));

      // Recalculate analytics after adding cattle
      _calculateAllAnalytics();

      // Clear filter cache after cattle addition
      FilterDataService.instance.clearCache();

      notifyListeners();
    } catch (e) {
      debugPrint('Error adding cattle: $e');
      throw Exception('Failed to add cattle');
    }
  }

  // Update cattle
  Future<void> updateCattle(CattleIsar updatedCattle) async {
    try {
      // First persist to database
      await _cattleHandler.updateCattle(updatedCattle);

      // Then update in-memory list
      final index = _cattle.indexWhere((c) => c.businessId == updatedCattle.businessId);
      if (index >= 0) {
        _cattle[index] = updatedCattle;
        _cattle.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));

        // Recalculate analytics after updating cattle
        _calculateAllAnalytics();

        // Clear filter cache after cattle update
        FilterDataService.instance.clearCache();

        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error updating cattle: $e');
      throw Exception('Failed to update cattle');
    }
  }

  // Delete cattle
  Future<void> deleteCattle(String businessId) async {
    try {
      // First delete from database
      await _cattleHandler.deleteCattle(businessId);

      // Then remove from in-memory list
      _cattle.removeWhere((c) => c.businessId == businessId);

      // Recalculate analytics after deleting cattle
      _calculateAllAnalytics();

      // Clear filter cache after cattle deletion
      FilterDataService.instance.clearCache();

      notifyListeners();
    } catch (e) {
      debugPrint('Error deleting cattle: $e');
      throw Exception('Failed to delete cattle');
    }
  }

  // Helper methods
  String? _getAnimalTypeName(String? animalTypeBusinessId) {
    if (animalTypeBusinessId == null) return null;
    final animalType = _animalTypes.firstWhere(
      (type) => type.businessId == animalTypeBusinessId,
      orElse: () => AnimalTypeIsar(),
    );
    return animalType.name;
  }

  AnimalTypeIsar? getAnimalType(String? animalTypeBusinessId) {
    if (animalTypeBusinessId == null) return null;
    try {
      return _animalTypes.firstWhere(
        (type) => type.businessId == animalTypeBusinessId,
      );
    } catch (e) {
      return null;
    }
  }

  BreedCategoryIsar? getBreed(String? breedBusinessId) {
    if (breedBusinessId == null) return null;
    try {
      return _breeds.firstWhere(
        (breed) => breed.businessId == breedBusinessId,
      );
    } catch (e) {
      return null;
    }
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }
}
