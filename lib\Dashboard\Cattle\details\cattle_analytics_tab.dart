import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/cattle_isar.dart';
import '../../../constants/app_colors.dart';


class CattleDetailAnalyticsTab extends StatefulWidget {
  final CattleIsar cattle;

  const CattleDetailAnalyticsTab({
    Key? key,
    required this.cattle,
  }) : super(key: key);

  @override
  State<CattleDetailAnalyticsTab> createState() => _CattleDetailAnalyticsTabState();
}

class _CattleDetailAnalyticsTabState extends State<CattleDetailAnalyticsTab>
    with AutomaticKeepAliveClientMixin {

  // Layout constants matching main analytics tab
  static const double _spacing = 16.0;
  static const double _aspectRatio = 1.3; // Increased height to prevent overflow
  static const int _crossAxisCount = 2;

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return SingleChildScrollView(
      padding: const EdgeInsets.all(_spacing),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cattle Information Dashboard
          _buildCattleInformationDashboard(context),
          SizedBox(height: _spacing),

          // Cattle Characteristics
          _buildCattleCharacteristics(context),
          SizedBox(height: _spacing),

          // Financial Information
          _buildFinancialInformation(context),
          SizedBox(height: _spacing),

          // Cattle Timeline
          _buildCattleTimeline(context),

          // Bottom padding
          SizedBox(height: _spacing),
        ],
      ),
    );
  }

  // Cattle Information Dashboard - only cattle-specific data
  Widget _buildCattleInformationDashboard(BuildContext context) {
    final kpiColors = AppColors.cattleKpiColors;

    return _buildGridSection(
      context: context,
      title: 'Cattle Information',
      subtitle: 'Basic information for ${widget.cattle.name ?? 'this cattle'}',
      icon: Icons.info_outlined,
      headerColor: AppColors.cattleKpiSection,
      cardData: [
        {
          'title': 'Tag ID',
          'value': widget.cattle.tagId ?? 'N/A',
          'subtitle': 'identification',
          'icon': Icons.tag,
          'color': kpiColors[0],
          'insight': 'Unique identifier',
        },
        {
          'title': 'Gender',
          'value': widget.cattle.gender ?? 'Unknown',
          'subtitle': 'biological sex',
          'icon': widget.cattle.gender?.toLowerCase() == 'male' ? Icons.male : Icons.female,
          'color': kpiColors[1],
          'insight': _getGenderInsight(),
        },
        {
          'title': 'Age',
          'value': _calculateAge(),
          'subtitle': 'current age',
          'icon': Icons.cake,
          'color': kpiColors[2],
          'insight': _getAgeInsight(),
        },
        {
          'title': 'Status',
          'value': widget.cattle.status ?? 'Unknown',
          'subtitle': 'current status',
          'icon': Icons.health_and_safety,
          'color': kpiColors[3],
          'insight': _getStatusInsight(),
        },
      ],
    );
  }

  // Universal grid section builder matching main analytics tab
  Widget _buildGridSection({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<Map<String, dynamic>> cardData,
    int? crossAxisCount,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 1. Build section header
        _buildSectionHeader(title, icon, headerColor, subtitle: subtitle),
        SizedBox(height: _spacing),

        // 2. Build the responsive grid
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: crossAxisCount ?? _crossAxisCount,
          mainAxisSpacing: _spacing * 0.5,
          crossAxisSpacing: _spacing * 0.5,
          childAspectRatio: _aspectRatio,
          children: cardData.map((data) {
            return _buildUniversalCard(
              context: context,
              title: data['title'] as String,
              value: data['value'] as String,
              subtitle: data['subtitle'] as String?,
              icon: data['icon'] as IconData,
              color: data['color'] as Color,
              insight: data['insight'] as String?,
            );
          }).toList(),
        ),
      ],
    );
  }

  // Section header builder
  Widget _buildSectionHeader(String title, IconData icon, Color color, {String? subtitle}) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: Colors.white, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 14,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Universal card builder matching main analytics tab
  Widget _buildUniversalCard({
    required BuildContext context,
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    String? subtitle,
    String? insight,
  }) {
    final isLargeScreen = MediaQuery.of(context).size.width > 600;
    final cardPadding = isLargeScreen ? 12.0 : 8.0;
    final iconSize = isLargeScreen ? 20.0 : 16.0;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: EdgeInsets.all(cardPadding),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(icon, color: color, size: iconSize),
                ),
                const Spacer(),
              ],
            ),
            const SizedBox(height: 8),
            Flexible(
              child: Text(
                value,
                style: TextStyle(
                  fontSize: isLargeScreen ? 20 : 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            const SizedBox(height: 2),
            Flexible(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: isLargeScreen ? 12 : 10,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey[800],
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            if (subtitle != null) ...[
              const SizedBox(height: 1),
              Flexible(
                child: Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: isLargeScreen ? 10 : 8,
                    color: Colors.grey[600],
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
            if (insight != null) ...[
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  insight,
                  style: TextStyle(
                    fontSize: 8,
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }



  // Cattle characteristics section
  Widget _buildCattleCharacteristics(BuildContext context) {
    final allColors = AppColors.cattleKpiColors;

    return _buildGridSection(
      context: context,
      title: 'Cattle Characteristics',
      subtitle: 'Physical and biological characteristics',
      icon: Icons.pets,
      headerColor: AppColors.cattleAgeDemographics,
      cardData: [
        {
          'title': 'Weight',
          'value': widget.cattle.weight?.toString() ?? 'N/A',
          'subtitle': 'kg',
          'icon': Icons.monitor_weight,
          'color': allColors[4 % allColors.length],
          'insight': _getCattleWeightInsight(),
        },
        {
          'title': 'Color',
          'value': widget.cattle.color ?? 'Not specified',
          'subtitle': 'coat color',
          'icon': Icons.palette,
          'color': allColors[5 % allColors.length],
          'insight': 'Physical trait',
        },
        {
          'title': 'Source',
          'value': widget.cattle.source ?? 'Unknown',
          'subtitle': 'origin',
          'icon': Icons.location_on,
          'color': allColors[6 % allColors.length],
          'insight': _getSourceInsight(),
        },
        {
          'title': 'Birth Date',
          'value': _formatBirthDate(),
          'subtitle': 'date of birth',
          'icon': Icons.calendar_today,
          'color': allColors[7 % allColors.length],
          'insight': _getBirthDateInsight(),
        },
      ],
    );
  }

  // Cattle timeline section
  Widget _buildCattleTimeline(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(
          'Cattle Timeline',
          Icons.timeline,
          AppColors.cattleFinancialOverview,
          subtitle: 'Important dates and milestones',
        ),
        SizedBox(height: _spacing),
        Card(
          elevation: 2,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: _buildTimelineItems(),
            ),
          ),
        ),
      ],
    );
  }

  List<Widget> _buildTimelineItems() {
    final timelineItems = <Map<String, dynamic>>[];

    // Add birth date
    if (widget.cattle.dateOfBirth != null) {
      timelineItems.add({
        'date': widget.cattle.dateOfBirth!,
        'title': 'Birth Date',
        'subtitle': 'Born on farm',
        'icon': Icons.cake,
        'color': AppColors.cattleKpiColors[0],
      });
    }

    // Add purchase date
    if (widget.cattle.purchaseDate != null) {
      timelineItems.add({
        'date': widget.cattle.purchaseDate!,
        'title': 'Purchase Date',
        'subtitle': 'Acquired for ${_formatCurrency(widget.cattle.purchasePrice)}',
        'icon': Icons.shopping_cart,
        'color': AppColors.cattleKpiColors[1],
      });
    }

    // Add current date as reference
    timelineItems.add({
      'date': DateTime.now(),
      'title': 'Current Status',
      'subtitle': widget.cattle.status ?? 'Active',
      'icon': Icons.today,
      'color': AppColors.cattleKpiColors[2],
    });

    // Sort by date (oldest first)
    timelineItems.sort((a, b) => (a['date'] as DateTime).compareTo(b['date'] as DateTime));

    if (timelineItems.isEmpty) {
      return [
        const Center(
          child: Padding(
            padding: EdgeInsets.all(32.0),
            child: Text(
              'No timeline data available',
              style: TextStyle(
                color: Colors.grey,
                fontSize: 16,
              ),
            ),
          ),
        ),
      ];
    }

    return timelineItems.map((item) {
      return _buildTimelineItem(
        item['title'] as String,
        item['subtitle'] as String,
        item['date'] as DateTime,
        item['icon'] as IconData,
        item['color'] as Color,
      );
    }).toList();
  }

  Widget _buildTimelineItem(String title, String subtitle, DateTime date, IconData icon, Color color) {
    final isToday = DateTime.now().difference(date).inDays == 0;
    final daysSince = DateTime.now().difference(date).inDays;
    final timeText = isToday ? 'Today' :
                    daysSince == 1 ? 'Yesterday' :
                    daysSince > 0 ? '$daysSince days ago' :
                    'In ${daysSince.abs()} days';

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                DateFormat('MMM dd, yyyy').format(date),
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 12,
                ),
              ),
              Text(
                timeText,
                style: TextStyle(
                  color: Colors.grey[500],
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // Financial information section
  Widget _buildFinancialInformation(BuildContext context) {
    final allColors = AppColors.cattleKpiColors;

    return _buildGridSection(
      context: context,
      title: 'Financial Information',
      subtitle: 'Purchase and value information',
      icon: Icons.attach_money,
      headerColor: AppColors.cattleFinancialOverview,
      cardData: [
        {
          'title': 'Purchase Price',
          'value': _formatCurrency(widget.cattle.purchasePrice),
          'subtitle': 'initial cost',
          'icon': Icons.shopping_cart,
          'color': allColors[2 % allColors.length],
          'insight': _getPurchasePriceInsight(),
        },
        {
          'title': 'Purchase Date',
          'value': _formatPurchaseDate(),
          'subtitle': 'acquisition date',
          'icon': Icons.calendar_today,
          'color': allColors[3 % allColors.length],
          'insight': _getPurchaseDateInsight(),
        },
        {
          'title': 'Estimated Value',
          'value': _formatCurrency(widget.cattle.purchasePrice),
          'subtitle': 'current worth',
          'icon': Icons.trending_up,
          'color': allColors[4 % allColors.length],
          'insight': 'Based on purchase price',
        },
        {
          'title': 'Days Owned',
          'value': _calculateDaysOwned(),
          'subtitle': 'ownership period',
          'icon': Icons.access_time,
          'color': allColors[5 % allColors.length],
          'insight': _getOwnershipInsight(),
        },
      ],
    );
  }

  // Helper methods for cattle-specific data
  String _calculateAge() {
    if (widget.cattle.dateOfBirth == null) return 'Unknown';
    final now = DateTime.now();
    final age = now.difference(widget.cattle.dateOfBirth!);
    final years = age.inDays ~/ 365;
    final months = (age.inDays % 365) ~/ 30;

    if (years > 0) {
      return months > 0 ? '${years}y ${months}m' : '${years}y';
    } else if (months > 0) {
      return '${months}m';
    } else {
      return '${age.inDays}d';
    }
  }

  String _getGenderInsight() {
    final gender = widget.cattle.gender?.toLowerCase() ?? 'unknown';
    switch (gender) {
      case 'male': return 'Bull/Steer';
      case 'female': return 'Cow/Heifer';
      default: return 'Not specified';
    }
  }

  String _getAgeInsight() {
    if (widget.cattle.dateOfBirth == null) return 'Unknown birth date';
    final age = DateTime.now().difference(widget.cattle.dateOfBirth!);
    final years = age.inDays ~/ 365;

    if (years < 1) return 'Young calf';
    if (years < 2) return 'Growing stage';
    if (years < 5) return 'Prime age';
    return 'Mature cattle';
  }

  String _getStatusInsight() {
    final status = widget.cattle.status?.toLowerCase() ?? 'unknown';
    switch (status) {
      case 'healthy': return 'Good condition';
      case 'sick': return 'Needs attention';
      case 'recovering': return 'Improving';
      case 'pregnant': return 'Expecting';
      default: return 'Status unknown';
    }
  }

  String _getCattleWeightInsight() {
    if (widget.cattle.weight == null) return 'No weight recorded';
    final weight = widget.cattle.weight!;
    if (weight < 100) return 'Light weight';
    if (weight < 300) return 'Average weight';
    if (weight < 500) return 'Heavy weight';
    return 'Very heavy';
  }

  String _getSourceInsight() {
    final source = widget.cattle.source?.toLowerCase() ?? 'unknown';
    switch (source) {
      case 'purchased': return 'Bought externally';
      case 'born': return 'Born on farm';
      case 'gift': return 'Received as gift';
      case 'inherited': return 'Family inheritance';
      default: return 'Origin unknown';
    }
  }

  String _formatBirthDate() {
    if (widget.cattle.dateOfBirth == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(widget.cattle.dateOfBirth!);
  }

  String _getBirthDateInsight() {
    if (widget.cattle.dateOfBirth == null) return 'No birth date';
    final age = DateTime.now().difference(widget.cattle.dateOfBirth!);
    return '${age.inDays} days old';
  }

  String _formatCurrency(double? amount) {
    if (amount == null) return 'N/A';
    return NumberFormat.currency(locale: 'en_US', symbol: '\$').format(amount);
  }

  String _getPurchasePriceInsight() {
    if (widget.cattle.purchasePrice == null) return 'No price data';
    final price = widget.cattle.purchasePrice!;
    if (price < 500) return 'Low cost';
    if (price < 1500) return 'Average cost';
    if (price < 3000) return 'High cost';
    return 'Premium cost';
  }

  String _formatPurchaseDate() {
    if (widget.cattle.purchaseDate == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(widget.cattle.purchaseDate!);
  }

  String _getPurchaseDateInsight() {
    if (widget.cattle.purchaseDate == null) return 'No purchase date';
    final daysSince = DateTime.now().difference(widget.cattle.purchaseDate!).inDays;
    return '$daysSince days ago';
  }



  String _calculateDaysOwned() {
    DateTime? startDate = widget.cattle.purchaseDate ?? widget.cattle.dateOfBirth;
    if (startDate == null) return 'Unknown';
    return DateTime.now().difference(startDate).inDays.toString();
  }

  String _getOwnershipInsight() {
    DateTime? startDate = widget.cattle.purchaseDate ?? widget.cattle.dateOfBirth;
    if (startDate == null) return 'Unknown period';
    final days = DateTime.now().difference(startDate).inDays;
    final years = days ~/ 365;
    if (years > 0) return '$years year${years > 1 ? 's' : ''}';
    final months = days ~/ 30;
    if (months > 0) return '$months month${months > 1 ? 's' : ''}';
    return '$days day${days > 1 ? 's' : ''}';
  }
}
