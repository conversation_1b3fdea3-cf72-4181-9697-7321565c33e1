import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../../Dashboard/Cattle/models/cattle_isar.dart';
import '../../../Dashboard/Farm Setup/models/breed_category_isar.dart';
import '../../../Dashboard/Farm Setup/models/animal_type_isar.dart';
import '../../../utils/navigation_utils.dart';
import 'dart:io';
import 'dart:async';
import 'package:image_picker/image_picker.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:logging/logging.dart';
import '../../../Dashboard/Cattle/services/cattle_handler.dart';
import '../../../Dashboard/Breeding/services/breeding_handler.dart';


// Create a class for better type safety with animal stages
class AnimalStage {
  final String name;
  final IconData icon;
  final Color color;
  final String description;
  final int minAgeDays;
  final int maxAgeDays; // -1 means no upper limit
  final int displayOrder;

  AnimalStage({
    required this.name,
    required this.icon,
    required this.color,
    required this.description,
    required this.minAgeDays,
    required this.maxAgeDays,
    required this.displayOrder,
  });

  // Factory constructor to create from a map (e.g., for database storage)
  factory AnimalStage.fromMap(Map<String, dynamic> map) {
    return AnimalStage(
      name: map['name'] as String,
      icon: IconData(
        map['iconCodePoint'] as int,
        fontFamily: map['iconFontFamily'] as String?,
      ),
      color: Color(map['colorValue'] as int),
      description: map['description'] as String,
      minAgeDays: map['minAgeDays'] as int,
      maxAgeDays: map['maxAgeDays'] as int,
      displayOrder: map['displayOrder'] as int,
    );
  }
}

class OverviewTab extends StatefulWidget {
  final CattleIsar cattle;
  final AnimalTypeIsar animalType;
  final BreedCategoryIsar? breed;
  final Function(CattleIsar)? onCattleUpdated;

  // Add handlers for dependency injection
  final CattleHandler cattleHandler;
  final BreedingHandler breedingHandler;

  const OverviewTab({
    Key? key,
    required this.cattle,
    required this.animalType,
    this.breed,
    this.onCattleUpdated,
    required this.cattleHandler,
    required this.breedingHandler,
  }) : super(key: key);

  @override
  State<OverviewTab> createState() => _OverviewTabState();
}

class _OverviewTabState extends State<OverviewTab> {
  late CattleIsar _cattle;
  late AnimalTypeIsar _animalType;
  final _logger = Logger('_OverviewTabState');

  // Replace List<Map> with Map<String, AnimalStage> for better type safety
  late Map<String, AnimalStage> _stageDataMap;
  // Add a cached sorted list of stages to avoid redundant sorting
  late List<AnimalStage> _sortedStages;
  bool _isLoadingStages = false;

  @override
  void initState() {
    super.initState();
    _cattle = widget.cattle;

    // Initialize _animalType with widget.animalType by default
    _animalType = widget.animalType;

    // Special handling for null animal type - create a default one
    if (widget.animalType.name == null &&
        widget.animalType.businessId == null) {
      _logger.warning('Animal type is null, creating a default one');
      _animalType = AnimalTypeIsar()..name = "Cattle";

      // Try to determine a better type based on gender
      if (_cattle.gender != null) {
        if (_cattle.gender == 'Female') {
          _animalType.name = "Cow";
        } else if (_cattle.gender == 'Male') {
          _animalType.name = "Bull";
        }
      }
    }

    // Initialize stage data map
    _initializeStageDataMap();
  }

  // Initialize stage data directly into a map structure
  void _initializeStageDataMap() {
    setState(() {
      _isLoadingStages = true;
    });

    final animalTypeName = _animalType.name?.toLowerCase() ?? 'unknown';
    final isFemale = (_cattle.gender?.toLowerCase() ?? '') == 'female';

    _logger.info(
        'Generating stages for $animalTypeName, gender: ${isFemale ? 'female' : 'male'}');

    _stageDataMap = {};

    // Add stages based on animal type and gender
    switch (animalTypeName) {
      case 'cow':
        if (isFemale) {
          _stageDataMap = {
            'Heifer Calf': AnimalStage(
              name: 'Heifer Calf',
              icon: Icons.child_care,
              color: const Color(0xFFE91E63),
              description: '0-6 months old female calf, milk-fed.',
              minAgeDays: 0,
              maxAgeDays: 180,
              displayOrder: 1,
            ),
            'Weaner': AnimalStage(
              name: 'Weaner',
              icon: Icons.trending_up,
              color: const Color(0xFF9C27B0),
              description: '6-12 months old female, weaned from milk.',
              minAgeDays: 180,
              maxAgeDays: 365,
              displayOrder: 2,
            ),
            'Yearling Heifer': AnimalStage(
              name: 'Yearling Heifer',
              icon: Icons.favorite,
              color: const Color(0xFF673AB7),
              description: '12-24 months old female, growing but not bred.',
              minAgeDays: 365,
              maxAgeDays: 730,
              displayOrder: 3,
            ),
            'Breeding Heifer': AnimalStage(
              name: 'Breeding Heifer',
              icon: Icons.pregnant_woman,
              color: const Color(0xFF3F51B5),
              description: '24+ months old female, bred for first time.',
              minAgeDays: 730,
              maxAgeDays: 1095,
              displayOrder: 4,
            ),
            'Cow': AnimalStage(
              name: 'Cow',
              icon: Icons.stars,
              color: const Color(0xFF1976D2),
              description: 'Adult female after first calving.',
              minAgeDays: 1095,
              maxAgeDays: -1,
              displayOrder: 5,
            ),
            'Dry Cow': AnimalStage(
              name: 'Dry Cow',
              icon: Icons.water_drop_outlined,
              color: const Color(0xFF0D47A1),
              description: 'Non-lactating adult female.',
              minAgeDays: 1095,
              maxAgeDays: -1,
              displayOrder: 6,
            ),
          };
        } else {
          _stageDataMap = {
            'Bull Calf': AnimalStage(
              name: 'Bull Calf',
              icon: Icons.child_care,
              color: const Color(0xFF2196F3),
              description: '0-6 months old male calf, milk-fed.',
              minAgeDays: 0,
              maxAgeDays: 180,
              displayOrder: 1,
            ),
            'Weaner': AnimalStage(
              name: 'Weaner',
              icon: Icons.trending_up,
              color: const Color(0xFF03A9F4),
              description: '6-12 months old male, weaned from milk.',
              minAgeDays: 180,
              maxAgeDays: 365,
              displayOrder: 2,
            ),
            'Yearling Bull': AnimalStage(
              name: 'Yearling Bull',
              icon: Icons.fitness_center,
              color: const Color(0xFF00BCD4),
              description: '12-24 months old male, reaching puberty.',
              minAgeDays: 365,
              maxAgeDays: 730,
              displayOrder: 3,
            ),
            'Mature Bull': AnimalStage(
              name: 'Mature Bull',
              icon: Icons.stars,
              color: const Color(0xFF009688),
              description: '2+ years old male, used for breeding.',
              minAgeDays: 730,
              maxAgeDays: 2190,
              displayOrder: 4,
            ),
            'Senior Bull': AnimalStage(
              name: 'Senior Bull',
              icon: Icons.elderly,
              color: const Color(0xFF00796B),
              description: '6+ years old male, declining fertility.',
              minAgeDays: 2190,
              maxAgeDays: -1,
              displayOrder: 5,
            ),
          };
        }
        break;
      default:
        // Default stages for unknown animal types
        _stageDataMap = isFemale
            ? {
                'Young': AnimalStage(
                  name: 'Young',
                  icon: Icons.child_care,
                  color: const Color(0xFFE91E63),
                  description: 'Young female.',
                  minAgeDays: 0,
                  maxAgeDays: 365,
                  displayOrder: 1,
                ),
                'Adult': AnimalStage(
                  name: 'Adult',
                  icon: Icons.stars,
                  color: const Color(0xFF3F51B5),
                  description: 'Adult female.',
                  minAgeDays: 365,
                  maxAgeDays: -1,
                  displayOrder: 2,
                ),
              }
            : {
                'Young': AnimalStage(
                  name: 'Young',
                  icon: Icons.child_care,
                  color: const Color(0xFF2196F3),
                  description: 'Young male.',
                  minAgeDays: 0,
                  maxAgeDays: 365,
                  displayOrder: 1,
                ),
                'Adult': AnimalStage(
                  name: 'Adult',
                  icon: Icons.stars,
                  color: const Color(0xFF009688),
                  description: 'Adult male.',
                  minAgeDays: 365,
                  maxAgeDays: -1,
                  displayOrder: 2,
                ),
              };
    }

    // Cache sorted stages once to avoid repeated sorting
    _sortedStages = _stageDataMap.values.toList()
      ..sort((a, b) => a.displayOrder.compareTo(b.displayOrder));

    setState(() {
      _isLoadingStages = false;
    });
  }

  // Simplified to just return the stage data map
  Map<String, AnimalStage> getStageOptions() {
    return _stageDataMap;
  }

  // Calculate current stage based on age - simplified to use only the stage data map
  String calculateStage() {
    try {
      // Calculate age based on date of birth if available, otherwise use purchase date
      final referenceDate = _cattle.dateOfBirth ?? _cattle.purchaseDate;

      if (referenceDate != null) {
        final ageInDays = DateTime.now().difference(referenceDate).inDays;

        // Use the pre-sorted stages list instead of sorting again
        for (var stage in _sortedStages) {
          if (ageInDays >= stage.minAgeDays &&
              (stage.maxAgeDays == -1 || ageInDays < stage.maxAgeDays)) {
            return stage.name;
          }
        }

        // If no matching stage found, return the first stage as fallback
        if (_sortedStages.isNotEmpty) {
          return _sortedStages.first.name;
        }
      }
    } catch (e) {
      _logger.warning('Error calculating stage: $e');
      // Fall through to default handling
    }

    // Default to the first stage if we couldn't calculate
    return _stageDataMap.isNotEmpty ? _stageDataMap.keys.first : 'Unknown';
  }

  // Helper method to get breeding history
  Future<List<dynamic>> _getBreedingRecords() async {
    try {
      if (_cattle.tagId == null) return [];
      return await widget.breedingHandler
          .getBreedingRecordsForCattle(_cattle.tagId ?? '');
    } catch (e) {
      _logger.severe('Error getting breeding records: $e');
      return [];
    }
  }

  // Helper method to check pregnancy status
  bool _isPregnant() {
    return _cattle.breedingStatus?.status?.toLowerCase() == 'pregnant';
  }

  // Helper method to format nullable strings
  String _formatNullableString(String? value) {
    return value?.trim().isNotEmpty == true ? value!.trim() : 'N/A';
  }

  // Enhanced error message handler with more specific exception types
  String _getReadableErrorMessage(dynamic e) {
    // Check for specific exception types first
    if (e is FileSystemException) {
      if (e.message.contains('permission')) {
        return 'Permission denied. Please check app permissions in settings.';
      } else if (e.message.contains('not found')) {
        return 'File not found. The file may have been moved or deleted.';
      } else if (e.message.contains('busy')) {
        return 'File is in use by another process. Please try again later.';
      }
      return 'File system error: ${e.message}. Please try again.';
    }

    if (e is FormatException) {
      return 'Invalid format: ${e.message}. Please check your input.';
    }

    if (e is TimeoutException) {
      return 'Operation timed out. Please check your connection and try again.';
    }

    // Check for common error strings if not a specific exception type
    final errorMsg = e.toString().toLowerCase();

    if (errorMsg.contains('permission')) {
      return 'Permission denied. Please check app permissions in settings.';
    } else if (errorMsg.contains('file')) {
      return 'Error accessing file. The file may be corrupted or unavailable.';
    } else if (errorMsg.contains('database')) {
      return 'Database error. Please restart the app and try again.';
    } else if (errorMsg.contains('network') ||
        errorMsg.contains('socket') ||
        errorMsg.contains('connection')) {
      return 'Network error. Please check your connection and try again.';
    } else if (errorMsg.contains('image')) {
      return 'Error processing image. Please try a different image.';
    } else if (errorMsg.contains('null')) {
      return 'Missing data error. Please ensure all required information is provided.';
    } else if (errorMsg.contains('timeout')) {
      return 'Operation timed out. Please try again later.';
    } else if (errorMsg.contains('not found') || errorMsg.contains('404')) {
      return 'Resource not found. Please check your input and try again.';
    } else if (errorMsg.contains('invalid')) {
      return 'Invalid input. Please check your information and try again.';
    }

    // Default fallback message
    return 'An unexpected error occurred. Please try again.';
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 80),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 16),
            _buildGeneralStatusCard(),
            const SizedBox(height: 16),
            _buildBasicInfoCard(),
            const SizedBox(height: 16),
            _buildSourceDetailsCard(context),
            const SizedBox(height: 16),
            if (_hasOptionalInfo()) _buildOptionalInfoCard(),
            const SizedBox(height: 16),
            _buildStageCard(),
          ],
        ),
      ),
    );
  }

  bool _hasOptionalInfo() {
    return _cattle.weight != null ||
        (_cattle.color?.isNotEmpty ?? false) ||
        (_cattle.notes?.isNotEmpty ?? false);
  }

  Widget _buildOptionalInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withAlpha(26),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor:
                      Theme.of(context).colorScheme.primary.withAlpha(50),
                  child: Icon(
                    Icons.info_outline,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Additional Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (_cattle.weight != null) ...[
                  _buildInfoRow(
                    icon: Icons.monitor_weight_outlined,
                    label: 'Weight',
                    value: '${_cattle.weight} kg',
                    iconColor: const Color(0xFFF57C00),
                  ),
                ],
                if (_cattle.color != null && _cattle.color!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  _buildInfoRow(
                    icon: Icons.palette_outlined,
                    label: 'Color',
                    value: _cattle.color!,
                    iconColor: const Color(0xFFEC407A),
                  ),
                ],
                if (_cattle.notes != null && _cattle.notes!.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  _buildInfoRow(
                    icon: Icons.note_outlined,
                    label: 'Notes',
                    value: _cattle.notes!,
                    iconColor: const Color(0xFF5C6BC0),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Center(
          child: Column(
            children: [
              _buildPhotoSection(context),
              const SizedBox(height: 16),
              Text(
                _cattle.name ?? 'Unnamed',
                style: const TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 4),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: _cattle.gender == 'Male'
                      ? const Color(0xFF1976D2).withAlpha(26)
                      : const Color(0xFFE91E63).withAlpha(26),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  _cattle.gender ?? 'Unknown',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: _cattle.gender == 'Male'
                        ? const Color(0xFF1976D2)
                        : const Color(0xFFE91E63),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhotoSection(BuildContext context) {
    return GestureDetector(
      onTap: () => _showFullImage(context),
      child: Stack(
        alignment: Alignment.bottomRight,
        children: [
          Container(
            width: double.infinity,
            height: 180,
            decoration: BoxDecoration(
              color: const Color(0xFFE3F2FD), // Light blue background
              borderRadius: BorderRadius.circular(15),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(15),
              child: _cattle.photoPath != null &&
                      File(_cattle.photoPath!).existsSync()
                  ? Image.file(
                      File(_cattle.photoPath!),
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        debugPrint('Error loading image: $error');
                        return SvgPicture.asset(
                          _getAnimalTypeIconPath(),
                          fit: BoxFit.contain,
                        );
                      },
                    )
                  : SvgPicture.asset(
                      _getAnimalTypeIconPath(),
                      fit: BoxFit.contain,
                    ),
            ),
          ),
          Positioned(
            bottom: 10,
            right: 10,
            child: Row(
              children: [
                CircleAvatar(
                  backgroundColor: Colors.white.withAlpha(179), // 0.7 opacity
                  child: IconButton(
                    icon: const Icon(Icons.edit, color: Colors.black),
                    onPressed: () => _pickImage(context),
                  ),
                ),
                const SizedBox(width: 10),
                if (_cattle.photoPath != null)
                  CircleAvatar(
                    backgroundColor: Colors.white.withAlpha(179), // 0.7 opacity
                    child: IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      onPressed: () => _confirmRemovePhoto(context),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _pickImage(BuildContext context) async {
    if (!mounted) return;
    final ImagePicker picker = ImagePicker();
    final navigatorContext = context;

    final source = await showModalBottomSheet<ImageSource>(
      context: navigatorContext,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('Take a Photo'),
                onTap: () => Navigator.pop(context, ImageSource.camera),
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () => Navigator.pop(context, ImageSource.gallery),
              ),
            ],
          ),
        );
      },
    );

    if (source == null) return;

    try {
      final XFile? pickedFile = await picker.pickImage(
        source: source,
        maxWidth: 1200,
        maxHeight: 1200,
        imageQuality: 85,
      );

      if (pickedFile != null) {
        final String photoPath = pickedFile.path;

        // Update cattle with new photo path
        final updatedCattle = _cattle.copyWith(
          photoPath: photoPath,
        );

        // Save to database using injected handler
        await widget.cattleHandler.updateCattle(updatedCattle);

        // Update state
        if (mounted) {
          setState(() {
            _cattle = updatedCattle;
          });

          // Notify parent if callback exists
          if (widget.onCattleUpdated != null) {
            widget.onCattleUpdated!(updatedCattle);
          }

          // Show success message
          if (navigatorContext.mounted) {
            ScaffoldMessenger.of(navigatorContext).showSnackBar(
              SnackBar(
                content: const Text('Photo updated successfully'),
                backgroundColor: Theme.of(context).colorScheme.primary,
              ),
            );
          }
        }
      }
    } catch (e) {
      _logger.severe('Error updating photo', e);
      if (mounted && navigatorContext.mounted) {
        ScaffoldMessenger.of(navigatorContext).showSnackBar(
          SnackBar(
            content: Text(_getReadableErrorMessage(e)),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _confirmRemovePhoto(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Remove Photo'),
          content: const Text('Are you sure you want to remove this photo?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(dialogContext).pop(),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
              child: const Text('Remove'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
                _removePhoto(context);
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> _removePhoto(BuildContext context) async {
    final navigatorContext = context;

    try {
      _logger.info('Attempting to remove photo for cattle: $_cattle');
      _logger.info('Current photo path: ${_cattle.photoPath}');

      if (_cattle.photoPath == null || _cattle.photoPath!.isEmpty) {
        _logger.warning('No photo path to remove');
        if (navigatorContext.mounted) {
          ScaffoldMessenger.of(navigatorContext).showSnackBar(
            const SnackBar(
              content: Text('No photo to remove'),
              backgroundColor: Color(0xFF795548), // Brown color
            ),
          );
        }
        return;
      }

      final photoFile = File(_cattle.photoPath!);
      if (photoFile.existsSync()) {
        try {
          await photoFile.delete();
          _logger.info('Photo file deleted successfully');
        } catch (fileDeleteError) {
          _logger.warning('Error deleting photo file', fileDeleteError);
          // Continue with the database update even if file deletion fails
        }
      } else {
        _logger.warning('Photo file does not exist: ${_cattle.photoPath}');
      }

      final updatedCattle = _cattle.copyWith(
        photoPath: null,
      );

      _logger.info('Updated cattle before database update: $updatedCattle');
      _logger.info('Updated cattle photoPath: ${updatedCattle.photoPath}');

      await widget.cattleHandler.updateCattle(updatedCattle);

      if (mounted) {
        setState(() {
          _cattle = updatedCattle;
        });

        _logger.info('Photo path after state update: ${_cattle.photoPath}');

        if (widget.onCattleUpdated != null) {
          widget.onCattleUpdated!(updatedCattle);
        }

        if (navigatorContext.mounted) {
          ScaffoldMessenger.of(navigatorContext).showSnackBar(
            SnackBar(
              content: const Text('Photo removed successfully'),
              backgroundColor: Theme.of(context).colorScheme.primary,
            ),
          );
        }
      }
    } catch (e) {
      _logger.severe('Error removing photo', e);

      if (mounted && navigatorContext.mounted) {
        ScaffoldMessenger.of(navigatorContext).showSnackBar(
          SnackBar(
            content: Text(_getReadableErrorMessage(e)),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showFullImage(BuildContext context) {
    // If no photo, don't show full screen
    if (_cattle.photoPath == null || !File(_cattle.photoPath!).existsSync()) {
      return;
    }

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          insetPadding: const EdgeInsets.all(10),
          child: Stack(
            alignment: Alignment.bottomRight,
            children: [
              Container(
                width: double.infinity,
                height: MediaQuery.of(context).size.height * 0.7,
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(15),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(15),
                  child: Image.file(
                    File(_cattle.photoPath!),
                    fit: BoxFit.contain,
                    errorBuilder: (context, error, stackTrace) {
                      // Use the existing helper method instead of duplicating logic
                      return SvgPicture.asset(
                        _getAnimalTypeIconPath(),
                        fit: BoxFit.contain,
                      );
                    },
                  ),
                ),
              ),
              Positioned(
                bottom: 20,
                right: 20,
                child: Row(
                  children: [
                    CircleAvatar(
                      backgroundColor:
                          Colors.white.withAlpha(179), // 0.7 opacity
                      child: IconButton(
                        icon: const Icon(Icons.edit, color: Colors.black),
                        onPressed: () {
                          Navigator.pop(context);
                          _pickImage(context);
                        },
                      ),
                    ),
                    const SizedBox(width: 10),
                    CircleAvatar(
                      backgroundColor:
                          Colors.white.withAlpha(179), // 0.7 opacity
                      child: IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        onPressed: () {
                          Navigator.pop(context);
                          _confirmRemovePhoto(context);
                        },
                      ),
                    ),
                    const SizedBox(width: 10),
                    CircleAvatar(
                      backgroundColor:
                          Colors.white.withAlpha(179), // 0.7 opacity
                      child: IconButton(
                        icon: const Icon(Icons.close, color: Colors.black),
                        onPressed: () => Navigator.pop(context),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildGeneralStatusCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.health_and_safety,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                const Text(
                  'General Status',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            _buildInfoRow(
              icon: Icons.local_hospital,
              label: 'Health Status:',
              value: _formatNullableString(_cattle.status),
              valueColor: _cattle.status?.toLowerCase() == 'healthy'
                  ? Colors.green
                  : const Color(0xFFF44336), // Red for unhealthy
              iconColor: const Color(0xFF00BCD4), // Cyan for health icon
            ),
            if (_cattle.gender?.toLowerCase() == 'female') ...[
              const SizedBox(height: 8),
              _buildInfoRow(
                icon: Icons.pregnant_woman,
                label: 'Pregnancy Status:',
                value: _isPregnant() ? 'Pregnant' : 'Not Pregnant',
                valueColor: _isPregnant() ? Colors.blue : const Color(0xFF3F51B5), // Indigo for not pregnant
                iconColor: const Color(0xFF9C27B0), // Purple for pregnancy icon
              ),
              const SizedBox(height: 16),
              const Divider(),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.child_care,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  const Text(
                    'Calving History',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              FutureBuilder<List<dynamic>>(
                future: _getBreedingRecords(),
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return const Center(
                      child: Padding(
                        padding: EdgeInsets.all(8.0),
                        child: CircularProgressIndicator(),
                      ),
                    );
                  }
                  if (snapshot.hasError) {
                    return Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Text(
                        _getReadableErrorMessage(snapshot.error),
                        style: TextStyle(
                            color: Theme.of(context).colorScheme.error),
                      ),
                    );
                  }
                  if (!snapshot.hasData || snapshot.data!.isEmpty) {
                    return const Padding(
                      padding: EdgeInsets.all(8.0),
                      child: Text('No calving history available'),
                    );
                  }
                  return _buildCalvingHistory(snapshot.data!);
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCalvingHistory(List<dynamic> breedingRecords) {
    int totalCalves = 0;
    int healthyCalves = 0;
    int weakCalves = 0;
    int stillbornCalves = 0;

    for (var record in breedingRecords) {
      if (record['status'] == 'Completed') {
        totalCalves++;
        String healthStatus = record['calfHealthStatus'] ?? '';
        if (healthStatus.toLowerCase() == 'healthy') {
          healthyCalves++;
        } else if (healthStatus.toLowerCase() == 'weak') {
          weakCalves++;
        } else if (healthStatus.toLowerCase() == 'stillborn') {
          stillbornCalves++;
        }
      }
    }

    return Column(
      children: [
        _buildInfoRow(
          icon: Icons.pets,
          label: 'Total Calves:',
          value: totalCalves.toString(),
          valueColor: Theme.of(context).colorScheme.primary,
          iconColor: const Color(0xFF4CAF50), // Green for total calves icon
        ),
        if (healthyCalves > 0) ...[
          const SizedBox(height: 8),
          _buildInfoRow(
            icon: Icons.check_circle,
            label: 'Healthy Calves:',
            value: healthyCalves.toString(),
            valueColor: Colors.green,
            iconColor: const Color(0xFF00C853), // Bright green for healthy calves icon
          ),
        ],
        if (weakCalves > 0) ...[
          const SizedBox(height: 8),
          _buildInfoRow(
            icon: Icons.warning,
            label: 'Weak Calves:',
            value: weakCalves.toString(),
            valueColor: const Color(0xFFFF5722), // Deep orange-red for weak calves
            iconColor: const Color(0xFFFF9800), // Amber for warning icon
          ),
        ],
        if (stillbornCalves > 0) ...[
          const SizedBox(height: 8),
          _buildInfoRow(
            icon: Icons.error,
            label: 'Stillborn Calves:',
            value: stillbornCalves.toString(),
            valueColor: Colors.red,
            iconColor: const Color(0xFFD32F2F), // Dark red for error icon
          ),
        ],
      ],
    );
  }

  Widget _buildBasicInfoCard() {
    // Get safe animal type name with better fallback
    String getAnimalTypeDisplay() {
      if (_animalType.name != null && _animalType.name!.isNotEmpty) {
        return _animalType.name!;
      }

      // Try to get from businessId if name is empty
      if (_animalType.businessId != null) {
        debugPrint(
            'Animal type name empty, using ID: ${_animalType.businessId}');
        return 'Cattle'; // Default to Cattle as fallback
      }

      return 'Unknown';
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withAlpha(26),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor:
                      Theme.of(context).colorScheme.primary.withAlpha(50),
                  child: Icon(
                    Icons.assignment_outlined,
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Basic Information',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfoRow(
                  icon: Icons.badge_outlined,
                  label: 'Name',
                  value: _formatNullableString(_cattle.name),
                  iconColor: const Color(0xFFF57C00),
                ),
                const SizedBox(height: 12),
                _buildInfoRow(
                  icon: Icons.tag,
                  label: 'Tag ID',
                  value: _formatNullableString(_cattle.tagId),
                  iconColor: const Color(0xFF1976D2),
                ),
                const SizedBox(height: 12),
                _buildInfoRow(
                  icon: Icons.pets_outlined,
                  label: 'Animal Type',
                  value: getAnimalTypeDisplay(),
                  iconColor: const Color(0xFF673AB7),
                ),
                if (widget.breed != null) ...[
                  const SizedBox(height: 12),
                  _buildInfoRow(
                    icon: Icons.category_outlined,
                    label: 'Breed',
                    value: _formatNullableString(widget.breed?.name),
                    iconColor: const Color(0xFF00796B),
                  ),
                ],
                const SizedBox(height: 12),
                _buildInfoRow(
                  icon: Icons.person_outline,
                  label: 'Gender',
                  value: _formatNullableString(_cattle.gender),
                  valueColor: (_cattle.gender?.toLowerCase() ?? '') == 'male'
                      ? const Color(0xFF1976D2)
                      : const Color(0xFFE91E63),
                  iconColor: (_cattle.gender?.toLowerCase() ?? '') == 'male'
                      ? const Color(0xFF1976D2)
                      : const Color(0xFFE91E63),
                ),
                const SizedBox(height: 12),
                _buildInfoRow(
                  icon: Icons.shopping_cart_outlined,
                  label: 'Source',
                  value: _formatNullableString(_cattle.source),
                  iconColor: const Color(0xFFFF9800),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSourceDetailsCard(BuildContext context) {
    final source = _cattle.source?.toLowerCase() ?? '';
    if (source == 'born at farm') {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withAlpha(26),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor:
                        Theme.of(context).colorScheme.primary.withAlpha(50),
                    child: Icon(
                      Icons.child_care,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Birth Details',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (_cattle.dateOfBirth != null)
                    _buildInfoRow(
                      icon: Icons.calendar_today,
                      label: 'Date of Birth',
                      value: DateFormat('MMM dd, yyyy')
                          .format(_cattle.dateOfBirth!),
                      iconColor: const Color(0xFFD32F2F),
                    ),
                  if (_cattle.motherTagId != null) ...[
                    const SizedBox(height: 12),
                    InkWell(
                      onTap: () {
                        if (_cattle.motherTagId != null) {
                          navigateToCattleDetails(
                              context, _cattle.motherTagId!);
                        }
                      },
                      child: Row(
                        children: [
                          const Icon(
                            Icons.family_restroom,
                            size: 20,
                            color: Color(0xFF9C27B0),
                          ),
                          const SizedBox(width: 12),
                          const Expanded(
                            flex: 2,
                            child: Text(
                              'Mother Tag ID',
                              style: TextStyle(fontSize: 16),
                            ),
                          ),
                          Expanded(
                            flex: 3,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                Text(
                                  _formatNullableString(_cattle.motherTagId),
                                  style: TextStyle(
                                    fontSize: 16,
                                    color:
                                        Theme.of(context).colorScheme.primary,
                                    decoration: TextDecoration.underline,
                                  ),
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(width: 4),
                                Icon(
                                  Icons.arrow_forward_ios,
                                  size: 14,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      );
    } else if (source == 'purchased') {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withAlpha(26),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor:
                        Theme.of(context).colorScheme.primary.withAlpha(50),
                    child: Icon(
                      Icons.shopping_bag,
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'Purchase Details',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (_cattle.purchaseDate != null)
                    _buildInfoRow(
                      icon: Icons.calendar_today,
                      label: 'Purchase Date',
                      value: DateFormat('MMM dd, yyyy')
                          .format(_cattle.purchaseDate!),
                      iconColor: const Color(0xFFD32F2F),
                    ),
                  if (_cattle.purchasePrice != null) ...[
                    const SizedBox(height: 12),
                    _buildInfoRow(
                      icon: Icons.attach_money,
                      label: 'Purchase Price',
                      value:
                          NumberFormat.currency(locale: 'en_US', symbol: '\$')
                              .format(_cattle.purchasePrice),
                      iconColor: const Color(0xFF388E3C),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildStageCard() {
    if (_isLoadingStages) {
      return Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Padding(
          padding: EdgeInsets.all(16.0),
          child: Center(
            child: Column(
              children: [
                Text("Loading growth stage..."),
                SizedBox(height: 8),
                CircularProgressIndicator(),
              ],
            ),
          ),
        ),
      );
    }

    final stageOptions = getStageOptions();

    // Cache the calculated stage
    String calculatedStage = calculateStage();

    // Use the stored stage if available, otherwise use calculated
    String currentStage = _cattle.stage ?? calculatedStage;

    // Validate the current stage exists in options
    if (!stageOptions.containsKey(currentStage)) {
      currentStage = calculatedStage;
    }

    final stageData = stageOptions[currentStage]!;
    final stageColor = stageData.color;
    final stageIcon = stageData.icon;
    final stageDescription = stageData.description;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: stageColor.withAlpha(26),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: stageColor.withAlpha(50),
                  child: Icon(
                    stageIcon,
                    color: stageColor,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                const Text(
                  'Growth Stage',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: InkWell(
              onTap: () => _showStageChangeDialog(),
              child: Row(
                children: [
                  Icon(
                    stageIcon,
                    color: stageColor,
                    size: 24,
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              currentStage,
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                                color: stageColor,
                              ),
                            ),
                            const Spacer(),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: stageColor.withAlpha(26),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(
                                    Icons.edit,
                                    color: stageColor,
                                    size: 18,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          stageDescription,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showStageChangeDialog() async {
    getStageOptions();

    // Use the cached sorted stages list for the dialog
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Change Growth Stage'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: _sortedStages.map((stage) {
                final status = stage.name;
                return ListTile(
                  leading: Icon(
                    stage.icon,
                    color: stage.color,
                  ),
                  title: Text(status),
                  subtitle: Text(
                    stage.description,
                    style: const TextStyle(fontSize: 12),
                  ),
                  onTap: () async {
                    try {
                      final updatedCattle = _cattle.copyWith(
                        stage: status,
                      );
                      await widget.cattleHandler.updateCattle(updatedCattle);
                      setState(() {
                        _cattle = updatedCattle;
                      });
                      if (context.mounted) {
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content:
                                Text('Successfully updated stage to $status'),
                            backgroundColor:
                                Theme.of(context).colorScheme.primary,
                          ),
                        );
                        if (widget.onCattleUpdated != null) {
                          widget.onCattleUpdated!(updatedCattle);
                        }
                      }
                    } catch (e, stackTrace) {
                      _logger.severe('Error updating stage', e, stackTrace);
                      if (context.mounted) {
                        Navigator.of(context).pop();
                        ScaffoldMessenger.of(context).showSnackBar(
                          SnackBar(
                            content: Text(_getReadableErrorMessage(e)),
                            backgroundColor: Colors.red,
                          ),
                        );
                      }
                    }
                  },
                );
              }).toList(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Cancel'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
    Color? iconColor,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: iconColor ?? Colors.grey,
        ),
        const SizedBox(width: 12),
        Expanded(
          flex: 2,
          child: Text(
            label,
            style: const TextStyle(fontSize: 16),
          ),
        ),
        Expanded(
          flex: 3,
          child: Text(
            value,
            style: TextStyle(
              fontSize: 16,
              color: valueColor,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.end,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  // Helper method to get the appropriate animal icon path based on type and gender
  String _getAnimalTypeIconPath() {
    final animalTypeName = _animalType.name?.toLowerCase() ?? '';

    // Map known animal types to their icons
    switch (animalTypeName) {
      case 'cow':
        return 'assets/icons/dairy_cow.svg';
      case 'dairy':
        return 'assets/icons/dairy_cow.svg';
      case 'beef':
        return 'assets/icons/beef_cow.svg';
      case 'heifer':
        return 'assets/icons/heifer.svg';
      case 'bull':
        return 'assets/icons/bull.svg';
      case 'buffalo':
        return 'assets/icons/buffalo.svg';
      case 'goat':
        return 'assets/icons/goat.svg';
      case 'sheep':
        return 'assets/icons/sheep.svg';
      case 'horse':
        return 'assets/icons/horse.svg';
      default:
        // Map based on gender if type is unknown
        if (_cattle.gender?.toLowerCase() == 'female') {
          return 'assets/icons/dairy_cow.svg'; // Default female icon
        } else if (_cattle.gender?.toLowerCase() == 'male') {
          return 'assets/icons/bull.svg'; // Default male icon
        }
        return 'assets/icons/cattle_generic.svg';
    }
  }
}
