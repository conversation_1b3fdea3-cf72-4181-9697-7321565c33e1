import 'package:isar/isar.dart';
import 'package:uuid/uuid.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:flutter/material.dart';

part 'cattle_isar.g.dart';

/// Custom JsonConverter for IconData
class IconDataConverter implements JsonConverter<IconData, Map<String, dynamic>> {
  const IconDataConverter();

  @override
  IconData fromJson(Map<String, dynamic> json) {
    return IconData(
      json['codePoint'] as int,
      fontFamily: json['fontFamily'] as String?,
    );
  }

  @override
  Map<String, dynamic> toJson(IconData object) {
    return {
      'codePoint': object.codePoint,
      'fontFamily': object.fontFamily,
    };
  }
}

/// Breeding status for cattle
@embedded
@JsonSerializable()
class BreedingStatus {
  /// Default constructor
  BreedingStatus();
  
  /// The current breeding status
  String? status; // 'Open', 'Bred', 'Pregnant', 'Fresh', etc.

  /// Date of the last heat/estrus
  DateTime? lastHeatDate;

  /// Is the animal currently pregnant
  bool isPregnant = false;

  /// Date of the most recent breeding
  DateTime? breedingDate;

  /// Expected date of calving based on breeding date
  DateTime? expectedCalvingDate;

  /// Next expected heat date if not pregnant
  DateTime? nextHeatDate;

  /// Last method used for breeding (Natural, AI, etc.)
  String? lastBreedingMethod;

  /// Date of the last calving
  DateTime? lastCalvingDate;

  factory BreedingStatus.fromJson(Map<String, dynamic> json) =>
      _$BreedingStatusFromJson(json);
  Map<String, dynamic> toJson() => _$BreedingStatusToJson(this);
}

/// Details about the birth of a calf
@embedded
@JsonSerializable()
class BirthDetails {
  /// Default constructor
  BirthDetails();
  
  /// The birth weight in kg
  double? birthWeight;

  /// Health status at birth (Healthy, Weak, Stillborn)
  String? healthStatus;

  /// Birth type (Single, Twin, etc.)
  String? birthType;

  /// Any complications during birth
  String? complications;

  /// Assistance required during birth
  bool assistanceRequired = false;

  factory BirthDetails.fromJson(Map<String, dynamic> json) =>
      _$BirthDetailsFromJson(json);
  Map<String, dynamic> toJson() => _$BirthDetailsToJson(this);
}

/// Health status information
@embedded
@JsonSerializable()
class HealthInfo {
  /// Default constructor
  HealthInfo();
  
  /// Current health status
  String? status; // 'Healthy', 'Sick', 'Recovering', etc.

  /// Current medication
  String? currentMedication;

  /// Date of last health check
  DateTime? lastCheckupDate;

  /// Date of last vaccination
  DateTime? lastVaccinationDate;

  /// Any chronic conditions
  String? chronicConditions;

  factory HealthInfo.fromJson(Map<String, dynamic> json) =>
      _$HealthInfoFromJson(json);
  Map<String, dynamic> toJson() => _$HealthInfoToJson(this);
}

/// Production information for dairy animals
@embedded
@JsonSerializable()
class ProductionInfo {
  /// Default constructor
  ProductionInfo();
  
  /// Current lactation number
  int? lactationNumber;

  /// Average daily milk production in liters
  double? avgDailyProduction;

  /// Peak milk production in liters
  double? peakProduction;

  /// Date of peak production
  DateTime? peakProductionDate;

  /// Days in milk for current lactation
  int? daysInMilk;

  /// Expected dry off date
  DateTime? expectedDryOffDate;

  factory ProductionInfo.fromJson(Map<String, dynamic> json) =>
      _$ProductionInfoFromJson(json);
  Map<String, dynamic> toJson() => _$ProductionInfoToJson(this);
}

/// Main Cattle model for Isar
@collection
@JsonSerializable()
class CattleIsar {
  factory CattleIsar.fromJson(Map<String, dynamic> json) =>
      _$CattleIsarFromJson(json);
  Map<String, dynamic> toJson() => _$CattleIsarToJson(this);

  /// Isar database ID
  Id id = Isar.autoIncrement;

  /// Business ID (UUID) - indexed for quick lookup
  @Index(unique: true)
  String? businessId;

  /// Tag ID / Ear tag number - indexed for quick lookup
  @Index(unique: true, caseSensitive: false)
  String? tagId;

  /// Name of the animal - indexed for search
  @Index(caseSensitive: false)
  String? name;

  /// Reference to the animal type business ID - indexed for filtering
  @Index()
  String? animalTypeId;

  /// Reference to the breed business ID - indexed for filtering
  @Index()
  String? breedId;

  /// Gender of the animal - indexed for filtering
  @Index()
  String? gender;

  /// Source of the animal (Born on farm, Purchased, etc.)
  String? source;

  /// Tag ID of the mother if known
  String? motherTagId;

  /// Business ID of the mother if known
  @Index()
  String? motherBusinessId;

  /// Date of birth
  DateTime? dateOfBirth;

  /// Date of purchase if purchased
  DateTime? purchaseDate;

  /// Purchase price
  double? purchasePrice;

  /// Current weight in kg
  double? weight;

  /// Color of the animal
  String? color;

  /// General notes
  String? notes;

  /// Path to photo of the animal
  String? photoPath;

  /// Date when record was created
  DateTime? createdAt;

  /// Date when record was last updated
  DateTime? updatedAt;

  /// Category of the animal (Heifer, Cow, Bull, Steer, etc.)
  @Index()
  String? category;

  /// Status of the animal (Active, Sold, Deceased, etc.)
  @Index()
  String? status;

  /// Production/growth stage of the animal
  @Index()
  String? stage;

  /// Breeding status information
  @JsonKey(includeFromJson: false, includeToJson: false)
  BreedingStatus? breedingStatus;

  /// Birth details if recorded
  @JsonKey(includeFromJson: false, includeToJson: false)
  BirthDetails? birthDetails;

  /// Health information
  @JsonKey(includeFromJson: false, includeToJson: false)
  HealthInfo? healthInfo;

  /// Production information for dairy animals
  @JsonKey(includeFromJson: false, includeToJson: false)
  ProductionInfo? productionInfo;

  /// Default constructor
  CattleIsar();

  /// Generate a deterministic ID based on the tag ID to ensure consistency across reinstalls
  static String generateBusinessId(String tagId) {
    // Clean the tag ID (remove spaces and special characters)
    final cleanTagId = tagId.trim().replaceAll(RegExp(r'[^a-zA-Z0-9]'), '');
    
    // If the tag ID is empty after cleaning, fall back to a random UUID
    if (cleanTagId.isEmpty) {
      return const Uuid().v4();
    }
    
    // Create a static prefix to distinguish cattle IDs
    const prefix = "cattle_";
    
    // Return a deterministic ID based on the tag ID
    return '$prefix$cleanTagId';
  }

  /// Factory constructor for creating a new cattle record
  factory CattleIsar.create({
    required String tagId,
    required String name,
    required String animalTypeId,
    required String breedId,
    required String gender,
    required String source,
    String? motherTagId,
    String? motherBusinessId,
    DateTime? dateOfBirth,
    DateTime? purchaseDate,
    double? purchasePrice,
    double? weight,
    String? color,
    String? notes,
    String? photoPath,
    String? category,
    String? status,
    String? stage,
    BreedingStatus? breedingStatus,
    BirthDetails? birthDetails,
    HealthInfo? healthInfo,
    ProductionInfo? productionInfo,
  }) {
    final cattle = CattleIsar()
      ..businessId = generateBusinessId(tagId)
      ..tagId = tagId
      ..name = name
      ..animalTypeId = animalTypeId
      ..breedId = breedId
      ..gender = gender
      ..source = source
      ..motherTagId = motherTagId
      ..motherBusinessId = motherBusinessId
      ..dateOfBirth = dateOfBirth
      ..purchaseDate = purchaseDate
      ..purchasePrice = purchasePrice
      ..weight = weight
      ..color = color
      ..notes = notes
      ..photoPath = photoPath
      ..category = category
      ..status = status ?? 'Active'
      ..stage = stage
      ..breedingStatus = breedingStatus ?? BreedingStatus()
      ..birthDetails = birthDetails ?? BirthDetails()
      ..healthInfo = healthInfo ?? HealthInfo()
      ..productionInfo = productionInfo ?? ProductionInfo()
      ..createdAt = DateTime.now()
      ..updatedAt = DateTime.now();

    return cattle;
  }

  /// Convert to a Map for serialization
  Map<String, dynamic> toMap() {
    return {
      'id': businessId,
      'tagId': tagId,
      'name': name,
      'animalTypeId': animalTypeId,
      'breedId': breedId,
      'gender': gender,
      'source': source,
      'motherTagId': motherTagId,
      'motherBusinessId': motherBusinessId,
      'dateOfBirth': dateOfBirth?.toIso8601String(),
      'purchaseDate': purchaseDate?.toIso8601String(),
      'purchasePrice': purchasePrice,
      'weight': weight,
      'color': color,
      'notes': notes,
      'photoPath': photoPath,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'category': category,
      'status': status,
      'stage': stage,
      'breedingStatus': breedingStatus != null
          ? {
              'status': breedingStatus!.status,
              'lastHeatDate': breedingStatus!.lastHeatDate?.toIso8601String(),
              'isPregnant': breedingStatus!.isPregnant,
              'breedingDate': breedingStatus!.breedingDate?.toIso8601String(),
              'expectedCalvingDate':
                  breedingStatus!.expectedCalvingDate?.toIso8601String(),
              'nextHeatDate': breedingStatus!.nextHeatDate?.toIso8601String(),
              'lastBreedingMethod': breedingStatus!.lastBreedingMethod,
              'lastCalvingDate':
                  breedingStatus!.lastCalvingDate?.toIso8601String(),
            }
          : null,
      'birthDetails': birthDetails != null
          ? {
              'birthWeight': birthDetails!.birthWeight,
              'healthStatus': birthDetails!.healthStatus,
              'birthType': birthDetails!.birthType,
              'complications': birthDetails!.complications,
              'assistanceRequired': birthDetails!.assistanceRequired,
            }
          : null,
      'healthInfo': healthInfo != null
          ? {
              'status': healthInfo!.status,
              'currentMedication': healthInfo!.currentMedication,
              'lastCheckupDate': healthInfo!.lastCheckupDate?.toIso8601String(),
              'lastVaccinationDate':
                  healthInfo!.lastVaccinationDate?.toIso8601String(),
              'chronicConditions': healthInfo!.chronicConditions,
            }
          : null,
      'productionInfo': productionInfo != null
          ? {
              'lactationNumber': productionInfo!.lactationNumber,
              'avgDailyProduction': productionInfo!.avgDailyProduction,
              'peakProduction': productionInfo!.peakProduction,
              'peakProductionDate':
                  productionInfo!.peakProductionDate?.toIso8601String(),
              'daysInMilk': productionInfo!.daysInMilk,
              'expectedDryOffDate':
                  productionInfo!.expectedDryOffDate?.toIso8601String(),
            }
          : null,
    };
  }

  /// Create from a Map (deserialization)
  factory CattleIsar.fromMap(Map<String, dynamic> map) {
    // Helper function to parse date values
    DateTime? parseDate(dynamic value) {
      if (value == null) return null;
      if (value is DateTime) return value;
      if (value is String) {
        try {
          return DateTime.parse(value);
        } catch (e) {
          return null;
        }
      }
      return null;
    }

    // Parse breeding status if available
    BreedingStatus? parseBreedingStatus(Map<String, dynamic>? data) {
      if (data == null) return null;
      final status = BreedingStatus()
        ..status = data['status'] as String?
        ..lastHeatDate = parseDate(data['lastHeatDate'])
        ..isPregnant = data['isPregnant'] as bool? ?? false
        ..breedingDate = parseDate(data['breedingDate'])
        ..expectedCalvingDate = parseDate(data['expectedCalvingDate'])
        ..nextHeatDate = parseDate(data['nextHeatDate'])
        ..lastBreedingMethod = data['lastBreedingMethod'] as String?
        ..lastCalvingDate = parseDate(data['lastCalvingDate']);
      return status;
    }

    // Parse birth details if available
    BirthDetails? parseBirthDetails(Map<String, dynamic>? data) {
      if (data == null) return null;
      final details = BirthDetails()
        ..birthWeight = data['birthWeight'] != null
            ? double.parse(data['birthWeight'].toString())
            : null
        ..healthStatus = data['healthStatus'] as String?
        ..birthType = data['birthType'] as String?
        ..complications = data['complications'] as String?
        ..assistanceRequired = data['assistanceRequired'] as bool? ?? false;
      return details;
    }

    // Parse health info if available
    HealthInfo? parseHealthInfo(Map<String, dynamic>? data) {
      if (data == null) return null;
      final info = HealthInfo()
        ..status = data['status'] as String?
        ..currentMedication = data['currentMedication'] as String?
        ..lastCheckupDate = parseDate(data['lastCheckupDate'])
        ..lastVaccinationDate = parseDate(data['lastVaccinationDate'])
        ..chronicConditions = data['chronicConditions'] as String?;
      return info;
    }

    // Parse production info if available
    ProductionInfo? parseProductionInfo(Map<String, dynamic>? data) {
      if (data == null) return null;
      final info = ProductionInfo()
        ..lactationNumber = data['lactationNumber'] as int?
        ..avgDailyProduction = data['avgDailyProduction'] != null
            ? double.parse(data['avgDailyProduction'].toString())
            : null
        ..peakProduction = data['peakProduction'] != null
            ? double.parse(data['peakProduction'].toString())
            : null
        ..peakProductionDate = parseDate(data['peakProductionDate'])
        ..daysInMilk = data['daysInMilk'] as int?
        ..expectedDryOffDate = parseDate(data['expectedDryOffDate']);
      return info;
    }

    final cattle = CattleIsar()
      ..businessId = map['id'] as String?
      ..tagId = map['tagId'] as String?
      ..name = map['name'] as String?
      ..animalTypeId = map['animalTypeId'] as String?
      ..breedId = map['breedId'] as String?
      ..gender = map['gender'] as String?
      ..source = map['source'] as String?
      ..motherTagId = map['motherTagId'] as String?
      ..motherBusinessId = map['motherBusinessId'] as String?
      ..dateOfBirth = parseDate(map['dateOfBirth'])
      ..purchaseDate = parseDate(map['purchaseDate'])
      ..purchasePrice = map['purchasePrice'] != null
          ? double.parse(map['purchasePrice'].toString())
          : null
      ..weight =
          map['weight'] != null ? double.parse(map['weight'].toString()) : null
      ..color = map['color'] as String?
      ..notes = map['notes'] as String?
      ..photoPath = map['photoPath'] as String?
      ..createdAt = parseDate(map['createdAt']) ?? DateTime.now()
      ..updatedAt = parseDate(map['updatedAt']) ?? DateTime.now()
      ..category = map['category'] as String?
      ..status = map['status'] as String?
      ..stage = map['stage'] as String?
      ..breedingStatus =
          parseBreedingStatus(map['breedingStatus'] as Map<String, dynamic>?)
      ..birthDetails =
          parseBirthDetails(map['birthDetails'] as Map<String, dynamic>?)
      ..healthInfo = parseHealthInfo(map['healthInfo'] as Map<String, dynamic>?)
      ..productionInfo =
          parseProductionInfo(map['productionInfo'] as Map<String, dynamic>?);

    return cattle;
  }

  /// Create a copy with updated values
  CattleIsar copyWith({
    String? businessId,
    String? tagId,
    String? name,
    String? animalTypeId,
    String? breedId,
    String? gender,
    String? source,
    String? motherTagId,
    String? motherBusinessId,
    DateTime? dateOfBirth,
    DateTime? purchaseDate,
    double? purchasePrice,
    double? weight,
    String? color,
    String? notes,
    String? photoPath,
    String? category,
    String? status,
    String? stage,
    BreedingStatus? breedingStatus,
    BirthDetails? birthDetails,
    HealthInfo? healthInfo,
    ProductionInfo? productionInfo,
  }) {
    final cattle = CattleIsar()
      ..id = id
      ..businessId = businessId ?? this.businessId
      ..tagId = tagId ?? this.tagId
      ..name = name ?? this.name
      ..animalTypeId = animalTypeId ?? this.animalTypeId
      ..breedId = breedId ?? this.breedId
      ..gender = gender ?? this.gender
      ..source = source ?? this.source
      ..motherTagId = motherTagId ?? this.motherTagId
      ..motherBusinessId = motherBusinessId ?? this.motherBusinessId
      ..dateOfBirth = dateOfBirth ?? this.dateOfBirth
      ..purchaseDate = purchaseDate ?? this.purchaseDate
      ..purchasePrice = purchasePrice ?? this.purchasePrice
      ..weight = weight ?? this.weight
      ..color = color ?? this.color
      ..notes = notes ?? this.notes
      ..photoPath = photoPath ?? this.photoPath
      ..createdAt = createdAt
      ..updatedAt = DateTime.now()
      ..category = category ?? this.category
      ..status = status ?? this.status
      ..stage = stage ?? this.stage;

    // Handle embedded objects
    if (breedingStatus != null) {
      cattle.breedingStatus = breedingStatus;
    } else if (this.breedingStatus != null) {
      cattle.breedingStatus = BreedingStatus()
        ..status = this.breedingStatus!.status
        ..lastHeatDate = this.breedingStatus!.lastHeatDate
        ..isPregnant = this.breedingStatus!.isPregnant
        ..breedingDate = this.breedingStatus!.breedingDate
        ..expectedCalvingDate = this.breedingStatus!.expectedCalvingDate
        ..nextHeatDate = this.breedingStatus!.nextHeatDate
        ..lastBreedingMethod = this.breedingStatus!.lastBreedingMethod
        ..lastCalvingDate = this.breedingStatus!.lastCalvingDate;
    }

    if (birthDetails != null) {
      cattle.birthDetails = birthDetails;
    } else if (this.birthDetails != null) {
      cattle.birthDetails = BirthDetails()
        ..birthWeight = this.birthDetails!.birthWeight
        ..healthStatus = this.birthDetails!.healthStatus
        ..birthType = this.birthDetails!.birthType
        ..complications = this.birthDetails!.complications
        ..assistanceRequired = this.birthDetails!.assistanceRequired;
    }

    if (healthInfo != null) {
      cattle.healthInfo = healthInfo;
    } else if (this.healthInfo != null) {
      cattle.healthInfo = HealthInfo()
        ..status = this.healthInfo!.status
        ..currentMedication = this.healthInfo!.currentMedication
        ..lastCheckupDate = this.healthInfo!.lastCheckupDate
        ..lastVaccinationDate = this.healthInfo!.lastVaccinationDate
        ..chronicConditions = this.healthInfo!.chronicConditions;
    }

    if (productionInfo != null) {
      cattle.productionInfo = productionInfo;
    } else if (this.productionInfo != null) {
      cattle.productionInfo = ProductionInfo()
        ..lactationNumber = this.productionInfo!.lactationNumber
        ..avgDailyProduction = this.productionInfo!.avgDailyProduction
        ..peakProduction = this.productionInfo!.peakProduction
        ..peakProductionDate = this.productionInfo!.peakProductionDate
        ..daysInMilk = this.productionInfo!.daysInMilk
        ..expectedDryOffDate = this.productionInfo!.expectedDryOffDate;
    }

    return cattle;
  }

  // Getters for icon conversion
  @ignore
  @JsonKey(includeFromJson: false, includeToJson: false)
  IconData get icon => IconData(iconCodePoint ?? 0, fontFamily: iconFontFamily);

  set icon(IconData value) {
    iconCodePoint = value.codePoint;
    iconFontFamily = value.fontFamily ?? 'MaterialIcons';
  }
  
  /// Icon code point for serialization 
  int? iconCodePoint;
  
  /// Icon font family for serialization
  String? iconFontFamily;
}

/// Extension methods for accessing breeding status fields more easily
extension BreedingStatusExtension on CattleIsar {
  /// Get the breeding status
  String? get breedingStatusValue => breedingStatus?.status;
  
  /// Set the breeding status
  set breedingStatusValue(String? value) {
    breedingStatus ??= BreedingStatus();
    breedingStatus!.status = value;
  }

  /// Get the last heat date
  DateTime? get lastHeatDate => breedingStatus?.lastHeatDate;
  
  /// Set the last heat date
  set lastHeatDate(DateTime? value) {
    breedingStatus ??= BreedingStatus();
    breedingStatus!.lastHeatDate = value;
  }

  /// Get whether the animal is pregnant
  bool get isPregnant => breedingStatus?.isPregnant ?? false;
  
  /// Set whether the animal is pregnant
  set isPregnant(bool value) {
    breedingStatus ??= BreedingStatus();
    breedingStatus!.isPregnant = value;
  }

  /// Get the last breeding date
  DateTime? get lastBreedingDate => breedingStatus?.breedingDate;
  
  /// Set the last breeding date
  set lastBreedingDate(DateTime? value) {
    breedingStatus ??= BreedingStatus();
    breedingStatus!.breedingDate = value;
  }

  /// Get the expected calving date
  DateTime? get expectedCalvingDate => breedingStatus?.expectedCalvingDate;
  
  /// Set the expected calving date
  set expectedCalvingDate(DateTime? value) {
    breedingStatus ??= BreedingStatus();
    breedingStatus!.expectedCalvingDate = value;
  }

  /// Get the next heat date
  DateTime? get nextHeatDate => breedingStatus?.nextHeatDate;
  
  /// Set the next heat date
  set nextHeatDate(DateTime? value) {
    breedingStatus ??= BreedingStatus();
    breedingStatus!.nextHeatDate = value;
  }

  /// Get the last breeding method
  String? get lastBreedingMethod => breedingStatus?.lastBreedingMethod;
  
  /// Set the last breeding method
  set lastBreedingMethod(String? value) {
    breedingStatus ??= BreedingStatus();
    breedingStatus!.lastBreedingMethod = value;
  }

  /// Get the last calving date
  DateTime? get lastCalvingDate => breedingStatus?.lastCalvingDate;
  
  /// Set the last calving date
  set lastCalvingDate(DateTime? value) {
    breedingStatus ??= BreedingStatus();
    breedingStatus!.lastCalvingDate = value;
  }

  /// Update the reproductive status
  set reproductiveStatus(String? value) {
    breedingStatus ??= BreedingStatus();
    breedingStatus!.status = value;
  }

  /// Get the reproductive status
  String? get reproductiveStatus => breedingStatus?.status;
}
