import 'package:flutter/material.dart';

import '../controllers/cattle_controller.dart';
import '../dialogs/cattle_form_dialog.dart';
import '../tabs/cattle_records_tab.dart';
import '../tabs/cattle_analytics_tab.dart';
import '../tabs/cattle_insights_tab.dart';
import '../../../utils/message_utils.dart';
import '../../../routes/app_routes.dart';
import '../../widgets/index.dart';
import '../../../constants/app_layout.dart'; // Import Universal Layout
import '../../../constants/app_tabs.dart'; // Import Universal Tabs
import '../../../constants/app_colors.dart'; // Import Universal Colors

class CattleScreen extends StatefulWidget {
  const CattleScreen({Key? key}) : super(key: key);

  @override
  State<CattleScreen> createState() => _CattleScreenState();
}

class _CattleScreenState extends State<CattleScreen>
    with TickerProviderStateMixin, UniversalScreenState {
  late TabController _tabController;
  late CattleController _cattleController;
  late UniversalTabManager _tabManager;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild to show/hide FAB based on tab
    });
    _cattleController = CattleController();
    _cattleController.loadData();

    // Initialize the universal tab manager with 3 tabs
    _tabManager = UniversalTabManager.threeTabs(
      controller: _tabController,
      tabViews: [
        CattleAnalyticsTab(controller: _cattleController),
        CattleRecordsTab(controller: _cattleController),
        CattleInsightsTab(controller: _cattleController),
      ],
      labels: ['Analytics', 'Records', 'Insights'],
      icons: [Icons.analytics, Icons.list, Icons.lightbulb],
      colors: [
        AppColors.cattleKpiColors[0], // Blue
        AppColors.cattleKpiColors[1], // Green
        AppColors.cattleKpiColors[2], // Purple
      ],
      showFABs: [false, true, false], // FAB only on Records tab
      indicatorColor: AppColors.cattleHeader,
    );
  }

  @override
  void dispose() {
    _tabController.dispose();
    _cattleController.dispose();
    super.dispose();
  }


  void _showAddCattleDialog() {
    showDialog(
      context: context,
      builder: (context) => CattleFormDialog(
        cattle: null,
        existingCattle: _cattleController.cattle,
        businessId: _cattleController.businessId,
        animalTypes: _cattleController.animalTypes,
        onSave: (newCattle) async {
          try {
            await _cattleController.addCattle(newCattle);

            if (!mounted) return;

            // Use standardized success message
            if (context.mounted) {
              CattleMessageUtils.showSuccess(context,
                  CattleMessageUtils.cattleRecordCreated());
            }

          } catch (e) {
            debugPrint('Error adding cattle: $e');

            if (!mounted) return;

            // Use standardized error message
            if (context.mounted) {
              CattleMessageUtils.showError(context, 'Error adding cattle');
            }
          }
        },
      ),
    );
  }



  @override
  Widget build(BuildContext context) {
    return UniversalLayout.tabScreen(
      title: 'Cattle Management',
      body: ListenableBuilder(
        listenable: _cattleController,
        builder: (context, child) {
          return UniversalStateBuilder(
            state: _getScreenStateFromController(),
            errorMessage: _cattleController.errorMessage,
            onRetry: () => executeWithLoading(() => _cattleController.refresh()),
            moduleColor: UniversalEmptyStateTheme.cattle,
            loadingWidget: UniversalLoadingIndicator.cattle(),
            errorWidget: UniversalErrorIndicator.cattle(
              message: _cattleController.errorMessage ?? 'Failed to load cattle data',
              onRetry: () => executeWithLoading(() => _cattleController.refresh()),
            ),
            child: _tabManager, // Use the universal tab manager
          );
        },
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.bar_chart),
          onPressed: () => Navigator.pushNamed(
            context,
            AppRoutes.cattleReport,
          ),
          tooltip: 'View Cattle Reports',
        ),
        IconButton(
          icon: const Icon(Icons.refresh),
          onPressed: () => executeWithLoading(() => _cattleController.refresh()),
          tooltip: 'Refresh',
        ),
      ],
      floatingActionButton: _tabManager.getCurrentFAB(
        onPressed: _showAddCattleDialog,
        tooltip: 'Add Cattle',
        backgroundColor: AppColors.cattleHeader,
      ), // Automatic FAB management
      onRefresh: () => executeWithLoading(() => _cattleController.refresh()),
    );
  }

  // Helper method to sync controller state with Universal Screen State
  ScreenState _getScreenStateFromController() {
    switch (_cattleController.state) {
      case ControllerState.initial:
        return ScreenState.initial;
      case ControllerState.loading:
        return ScreenState.loading;
      case ControllerState.loaded:
        return ScreenState.loaded;
      case ControllerState.error:
        return ScreenState.error;
      case ControllerState.empty:
        return ScreenState.empty;
    }
  }
}
