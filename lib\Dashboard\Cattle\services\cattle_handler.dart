import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';
import 'package:flutter/foundation.dart';

import '../models/cattle_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Milk Records/models/milk_record_isar.dart';
import '../../Milk Records/services/milk_handler.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';
import '../../../services/streams/stream_service.dart';
import '../../widgets/filters/filter_data_service.dart';

/// Consolidated handler for all Cattle module database operations
class CattleHandler {
  static final Logger _logger = Logger('CattleHandler');
  final IsarService _isarService;

  // Singleton instance
  static final CattleHandler _instance = CattleHandler._internal();
  static CattleHandler get instance => _instance;

  // Private constructor
  CattleHandler._internal() : _isarService = GetIt.instance<IsarService>();

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== CATTLE RECORDS ===//

  /// Get all cattle
  Future<List<CattleIsar>> getAllCattle() async {
    try {
      return await _isar.cattleIsars.where().findAll();
    } catch (e) {
      _logger.severe('Error getting all cattle: $e');
      throw DatabaseException('Failed to retrieve cattle', e.toString());
    }
  }

  /// Get cattle by ID
  Future<CattleIsar?> getCattleById(String businessId) async {
    // Add debugging to see if this method is called correctly
    debugPrint('DEBUG: getCattleById called with businessId: $businessId');

    if (businessId.isEmpty) {
      debugPrint('ERROR: Empty businessId provided to getCattleById');
      return null;
    }

    try {
      return await _isar.cattleIsars
          .filter()
          .businessIdEqualTo(businessId)
          .findFirst();
    } catch (e) {
      _logger.severe('Error getting cattle by ID $businessId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve cattle', e.toString());
    }
  }

  /// Get cattle by tag number
  Future<CattleIsar?> getCattleByTagNumber(String tagNumber) async {
    try {
      if (tagNumber.isEmpty) {
        throw ValidationException('Tag number is required');
      }

      return await _isar.cattleIsars
          .filter()
          .tagIdEqualTo(tagNumber)
          .findFirst();
    } catch (e) {
      _logger.severe('Error getting cattle by tag number $tagNumber: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve cattle', e.toString());
    }
  }

  /// Get cattle by tag ID
  Future<CattleIsar?> getCattleByTagId(String tagId) async {
    try {
      if (tagId.isEmpty) {
        throw ValidationException('Tag ID is required');
      }

      return await _isar.cattleIsars.filter().tagIdEqualTo(tagId).findFirst();
    } catch (e) {
      _logger.severe('Error getting cattle by tag ID $tagId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve cattle', e.toString());
    }
  }

  /// Get cattle by mother tag ID
  Future<List<CattleIsar>> getCattleByMotherTagId(String motherTagId) async {
    try {
      if (motherTagId.isEmpty) {
        throw ValidationException('Mother tag ID is required');
      }

      return await _isar.cattleIsars
          .filter()
          .motherTagIdEqualTo(motherTagId)
          .findAll();
    } catch (e) {
      _logger.severe('Error getting cattle by mother tag ID $motherTagId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve cattle', e.toString());
    }
  }

  /// Get cattle by breed category
  Future<List<CattleIsar>> getCattleByBreedCategory(
      String breedCategoryId) async {
    try {
      if (breedCategoryId.isEmpty) {
        throw ValidationException('Breed category ID is required');
      }

      return await _isar.cattleIsars
          .filter()
          .breedIdEqualTo(breedCategoryId)
          .findAll();
    } catch (e) {
      _logger.severe(
          'Error getting cattle by breed category $breedCategoryId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve cattle', e.toString());
    }
  }

  /// Get cattle by animal type
  Future<List<CattleIsar>> getCattleByAnimalType(String animalTypeId) async {
    try {
      if (animalTypeId.isEmpty) {
        throw ValidationException('Animal type ID is required');
      }

      return await _isar.cattleIsars
          .filter()
          .animalTypeIdEqualTo(animalTypeId)
          .findAll();
    } catch (e) {
      _logger.severe('Error getting cattle by animal type $animalTypeId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve cattle', e.toString());
    }
  }

  /// Add a new cattle record
  Future<void> addCattle(CattleIsar cattle) async {
    try {
      await _validateCattle(cattle, isNew: true);

      await _isar.writeTxn(() async {
        await _isar.cattleIsars.put(cattle);
      });

      // Notify about cattle addition
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyCattleChange({
        'action': 'add',
        'cattleId': cattle.tagId,
        'data': cattle.toMap(),
      });

      // Clear filter cache after cattle addition
      FilterDataService.instance.clearCache();

      _logger.info('Successfully added cattle: ${cattle.businessId}');
    } catch (e) {
      _logger.severe('Error adding cattle: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to add cattle', e.toString());
    }
  }

  /// Get milk records for a specific cattle
  Future<List<MilkRecordIsar>> getMilkRecords(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Business ID is required');
      }

      return await MilkHandler.instance.getMilkRecordsForCattle(businessId);
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $businessId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Update an existing cattle record
  Future<void> updateCattle(CattleIsar cattle) async {
    try {
      await _validateCattle(cattle, isNew: false);

      // First, find the existing record
      final existingCattle = await _isar.cattleIsars
          .filter()
          .businessIdEqualTo(cattle.businessId)
          .findFirst();

      if (existingCattle == null) {
        throw RecordNotFoundException(
            'Cattle not found with ID: ${cattle.businessId}');
      }

      // Log the before and after values to debug the update
      _logger.info(
          'Updating cattle: ${cattle.businessId}, name: ${existingCattle.name} -> ${cattle.name}');

      // Preserve the Isar ID for proper updating
      cattle.id = existingCattle.id;

      // Ensure we have a status
      cattle.status ??= existingCattle.status ?? 'Active';

      await _isar.writeTxn(() async {
        // Use put instead of update to ensure complete replacement
        final id = await _isar.cattleIsars.put(cattle);
        _logger.info('Database ID after update: $id');
      });

      // Verify the update was successful by reading it back
      final updatedRecord = await getCattleById(cattle.businessId ?? '');
      if (updatedRecord != null) {
        _logger.info(
            'Update verification: cattle ${cattle.businessId} name is now ${updatedRecord.name}');
      } else {
        _logger
            .warning('Could not verify update for cattle ${cattle.businessId}');
      }

      // Notify about cattle update
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyCattleChange({
        'action': 'update',
        'cattleId': cattle.tagId,
        'data': cattle.toMap(),
      });

      // Clear filter cache after cattle update
      FilterDataService.instance.clearCache();

      _logger.info('Successfully updated cattle: ${cattle.businessId}');
    } catch (e) {
      _logger.severe('Error updating cattle: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to update cattle', e.toString());
    }
  }

  /// Delete a cattle record
  Future<void> deleteCattle(String businessId) async {
    debugPrint(
        'DEBUG: Attempting to delete cattle with businessId: $businessId');

    try {
      if (businessId.isEmpty) {
        debugPrint('ERROR: Empty businessId provided to deleteCattle');
        throw ValidationException('Cattle ID is required');
      }

      await _isar.writeTxn(() async {
        final cattle = await _isar.cattleIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (cattle == null) {
          debugPrint('ERROR: Cattle with businessId $businessId not found');
          throw RecordNotFoundException('Cattle not found: $businessId');
        }

        debugPrint(
            'DEBUG: Found cattle to delete: id=${cattle.id}, businessId=$businessId, name=${cattle.name}');

        // Now delete the cattle
        final result = await _isar.cattleIsars.delete(cattle.id);
        debugPrint('DEBUG: Delete result: $result (1 means success)');
      });

      // Notify about cattle deletion
      final streamService = GetIt.instance<StreamService>();
      streamService.notifyCattleChange({
        'action': 'delete',
        'cattleId': businessId,
      });

      // Clear filter cache after cattle deletion
      FilterDataService.instance.clearCache();

      _logger.info('Successfully deleted cattle: $businessId');
      debugPrint('SUCCESS: Deleted cattle with businessId: $businessId');
    } catch (e, s) {
      _logger.severe('Error deleting cattle $businessId: $e');
      debugPrint('ERROR: Failed to delete cattle: $e');
      debugPrint('ERROR: Stack trace: $s');

      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete cattle', e.toString());
    }
  }

  /// Save a cattle record (add if new, update if existing)
  Future<void> saveCattle(CattleIsar cattle) async {
    try {
      // Check if the cattle has a business ID and if it already exists in the database
      if (cattle.businessId != null && cattle.businessId!.isNotEmpty) {
        // Check if it exists in the database
        final existing = await _isar.cattleIsars
            .filter()
            .businessIdEqualTo(cattle.businessId!)
            .findFirst();
        if (existing != null) {
          // Update existing record
          await updateCattle(cattle);
          return;
        }
      }

      // Add as a new record
      await addCattle(cattle);
    } catch (e) {
      _logger.severe('Error saving cattle: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to save cattle', e.toString());
    }
  }

  /// Search cattle by name
  Future<List<CattleIsar>> searchCattleByName(String searchTerm) async {
    try {
      if (searchTerm.isEmpty) {
        return await getAllCattle();
      }

      final searchLower = searchTerm.toLowerCase();

      // In a proper implementation, we would use Isar's full-text search
      // For now, we'll just filter the results manually after fetching
      final allCattle = await _isar.cattleIsars.where().findAll();
      return allCattle.where((cattle) {
        final name = cattle.name?.toLowerCase() ?? '';
        return name.contains(searchLower);
      }).toList();
    } catch (e) {
      _logger.severe('Error searching cattle by name $searchTerm: $e');
      throw DatabaseException('Failed to search cattle by name', e.toString());
    }
  }

  /// Validate cattle for adding/updating
  Future<void> _validateCattle(CattleIsar cattle, {bool isNew = true}) async {
    // Validation checks
    if (cattle.tagId?.isEmpty ?? true) {
      throw ValidationException('Tag ID is required');
    }

    // Check for duplicate tag ID
    if (isNew) {
      final existingCattle = await _isar.cattleIsars
          .filter()
          .tagIdEqualTo(cattle.tagId!)
          .findAll();

      if (existingCattle.isNotEmpty) {
        throw ValidationException('A cattle with this tag ID already exists');
      }
    }

    // Validate animal type and breed category
    final animalType = await _isar.animalTypeIsars
        .filter()
        .businessIdEqualTo(cattle.animalTypeId)
        .findFirst();

    if (animalType == null) {
      throw ValidationException('Invalid animal type ID');
    }

    final breedCategory = await _isar.breedCategoryIsars
        .filter()
        .businessIdEqualTo(cattle.breedId)
        .findFirst();

    if (breedCategory == null) {
      throw ValidationException('Invalid breed category ID');
    }

    // Ensure breed category belongs to the selected animal type
    if (breedCategory.animalTypeId != cattle.animalTypeId) {
      throw ValidationException(
          'Breed category does not belong to the selected animal type');
    }

    // Set default values for new records
    if (isNew) {
      cattle.createdAt = DateTime.now();
      cattle.updatedAt = DateTime.now();
      cattle.breedingStatus ??= BreedingStatus();
      cattle.healthInfo ??= HealthInfo();
      cattle.productionInfo ??= ProductionInfo();
    } else {
      cattle.updatedAt = DateTime.now();
    }
  }
}
