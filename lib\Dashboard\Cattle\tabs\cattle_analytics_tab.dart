import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../controllers/cattle_controller.dart';
import '../../widgets/index.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_layout.dart';

class CattleAnalyticsTab extends StatefulWidget {
  final CattleController controller;

  const CattleAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<CattleAnalyticsTab> createState() => _CattleAnalyticsTabState();
}

class _CattleAnalyticsTabState extends State<CattleAnalyticsTab> {
  int _selectedChartIndex = 0; // 0: Gender, 1: Type, 2: Age, 3: Breeds

  // Use global constants instead of local ones
  static const _fallbackColor = AppColors.fallback;

  // Common chart configuration
  static const _chartRadius = 80.0;
  static const _chartCenterSpace = 50.0;
  static const _chartSectionsSpace = 2.0;
  static const _chartHeight = 240.0;

  // Common text styles
  static const _chartTitleStyle = TextStyle(
    fontSize: 12,
    fontWeight: FontWeight.bold,
    color: Colors.white,
  );

  @override
  void initState() {
    super.initState();
    // Data loading is now handled by the controller
  }

  // Format currency based on controller settings
  String _formatCurrency(double amount) {
    final symbol = widget.controller.currencySymbol;
    // For simplicity, assume symbol before amount (can be enhanced later)
    return '$symbol${amount.toStringAsFixed(2)}';
  }

  // High-level abstraction for grid sections using universal components
  Widget _buildGridSection({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color headerColor,
    required List<Map<String, dynamic>> cardData,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 1. Build the section header using universal component
        UniversalFormField.sectionHeader(
          title: title,
          icon: icon,
          color: headerColor,
          subtitle: subtitle,
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium),

        // 2. Use ResponsiveGrid.cards for consistent responsive behavior
        ResponsiveGrid.cards(
          children: cardData.map((data) {
            return UniversalInfoCard(
              title: data['title'] as String,
              value: data['value'] as String,
              subtitle: data['subtitle'] as String?,
              icon: data['icon'] as IconData,
              color: data['color'] as Color,
              badge: data['badge'] as String?,
              insight: data['insight'] as String?,
            );
          }).toList(),
        ),
      ],
    );
  }



  // Universal pie chart builder - pure UI component without business logic
  Widget _buildUniversalPieChart(Map<String, int> data, Map<String, Color> colors, {String? emptyMessage}) {
    if (data.isEmpty || widget.controller.totalCattle == 0) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }

    // Ensure we have valid data before rendering
    final validEntries = data.entries.where((entry) => entry.value > 0).toList();
    if (validEntries.isEmpty) {
      return Center(child: Text(emptyMessage ?? 'No data available'));
    }

    final sections = validEntries.map((entry) {
      final percentage = (entry.value / widget.controller.totalCattle) * 100;
      final sectionColor = colors[entry.key] ?? _fallbackColor;

      return PieChartSectionData(
        color: sectionColor,
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: _chartRadius,
        titleStyle: _chartTitleStyle,
      );
    }).toList();

    // Add a small delay to ensure smooth rendering
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 300),
      child: PieChart(
        PieChartData(
          sections: sections,
          centerSpaceRadius: _chartCenterSpace,
          sectionsSpace: _chartSectionsSpace,
          borderData: FlBorderData(show: false),
        ),
      ),
    );
  }





  @override
  Widget build(BuildContext context) {
    // Check if we have data to display
    if (widget.controller.totalCattle == 0) {
      return UniversalEmptyState.cattle(
        title: 'No Cattle Data',
        message: 'Add cattle to your herd to view comprehensive analytics and insights.',
        action: EmptyStateActions.addFirstRecord(
          onPressed: () {
            // Navigate to add cattle screen
            Navigator.of(context).pushNamed('/cattle/add');
          },
          backgroundColor: UniversalEmptyStateTheme.cattle,
        ),
      );
    }

    // Define sections for clean, declarative layout
    final sections = [
      _buildEnhancedKPIDashboard(context),
      _buildHerdCompositionAnalytics(context),
      _buildAgeDemographics(context),
      _buildCattleFinancialOverview(context),
    ];

    // Use RefreshIndicator with SingleChildScrollView for pull-to-refresh functionality
    return RefreshIndicator(
      onRefresh: () async {
        await widget.controller.refresh();
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(kPaddingMedium), // Use global constant
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            for (int i = 0; i < sections.length; i++) ...[
              sections[i],
              if (i < sections.length - 1) const SizedBox(height: kSpacingLarge), // Use global constant
            ],
          ],
        ),
      ),
    );
  }



  Widget _buildEnhancedKPIDashboard(BuildContext context) {
    final totalCattle = widget.controller.totalCattle;
    final maleCattle = widget.controller.maleCattle;
    final femaleCattle = widget.controller.femaleCattle;
    final avgAge = widget.controller.averageAge;
    final activeCattle = widget.controller.activeCattleCount;
    final soldCattle = widget.controller.soldCattleCount;

    final kpiColors = _getKPIColors();

    return _buildGridSection(
      title: 'Key Performance Indicators',
      subtitle: 'Essential metrics for your cattle herd',
      icon: Icons.dashboard_outlined,
      headerColor: AppColors.cattleKpiSection,
      cardData: [
        {
          'title': 'Total Herd',
          'value': totalCattle.toString(),
          'subtitle': 'cattle in herd',
          'icon': Icons.groups,
          'color': kpiColors[0],
          'badge': null,
        },
        {
          'title': 'Active Cattle',
          'value': activeCattle.toString(),
          'subtitle': 'in active status',
          'icon': Icons.check_circle,
          'color': kpiColors[1],
          'badge': null,
        },
        {
          'title': 'Male Cattle',
          'value': maleCattle.toString(),
          'subtitle': 'bulls & steers',
          'icon': Icons.male,
          'color': kpiColors[2],
          'badge': null,
        },
        {
          'title': 'Female Cattle',
          'value': femaleCattle.toString(),
          'subtitle': 'cows & heifers',
          'icon': Icons.female,
          'color': kpiColors[3],
          'badge': null,
        },
        {
          'title': 'Average Age',
          'value': avgAge.toStringAsFixed(1),
          'subtitle': 'years old',
          'icon': Icons.schedule,
          'color': kpiColors[4],
          'badge': null,
        },
        {
          'title': 'Sold Cattle',
          'value': soldCattle.toString(),
          'subtitle': 'animals sold',
          'icon': Icons.sell,
          'color': kpiColors[5],
          'badge': null,
        },
      ],
    );
  }







  Widget _buildHerdCompositionAnalytics(BuildContext context) {
    final genderData = widget.controller.cattleByGender;
    final typeData = widget.controller.cattleByType;
    final ageDistribution = widget.controller.ageDistribution;
    final breedDistribution = widget.controller.breedDistribution;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        UniversalFormField.sectionHeader(
          title: 'Herd Composition Analytics',
          icon: Icons.pie_chart_outline,
          color: AppColors.cattleHerdComposition,
          subtitle: 'Detailed breakdown of your cattle demographics',
          filled: true,
          padding: EdgeInsets.zero,
        ),
        const SizedBox(height: kSpacingMedium), // Use global constant

        // Toggle buttons for chart selection
        _buildChartToggleButtons(context),
        const SizedBox(height: kSpacingMedium), // Use global constant

        // Single chart display based on selection
        _buildSelectedChart(context, genderData, typeData, ageDistribution, breedDistribution),
      ],
    );
  }

  Widget _buildChartToggleButtons(BuildContext context) {
    final chartOptions = [
      {'title': 'Gender', 'icon': Icons.wc},
      {'title': 'Types', 'icon': Icons.category},
      {'title': 'Age', 'icon': Icons.timeline},
      {'title': 'Breeds', 'icon': Icons.pets},
    ];

    // Use colors from AppColors instead of hardcoded values
    final toggleColors = [
      AppColors.cattleKpiColors[0], // Blue
      AppColors.cattleKpiColors[2], // Purple
      AppColors.cattleKpiColors[4], // Teal
      AppColors.cattleKpiColors[1], // Green
    ];

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: chartOptions.asMap().entries.map((entry) {
        final index = entry.key;
        final option = entry.value;
        final isSelected = _selectedChartIndex == index;

        return ChoiceChip(
          label: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                option['icon'] as IconData,
                size: 16,
                color: isSelected ? Colors.white : toggleColors[index],
              ),
              const SizedBox(width: 6),
              Text(
                option['title'] as String,
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                  color: isSelected ? Colors.white : toggleColors[index],
                ),
              ),
            ],
          ),
          selected: isSelected,
          onSelected: (selected) {
            if (selected) {
              setState(() {
                _selectedChartIndex = index;
              });
            }
          },
          selectedColor: toggleColors[index],
          backgroundColor: Colors.white,
          side: BorderSide(
            color: toggleColors[index],
            width: 2,
          ),
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        );
      }).toList(),
    );
  }

  Widget _buildSelectedChart(
    BuildContext context,
    Map<String, int> genderData,
    Map<String, int> typeData,
    Map<String, int> ageDistribution,
    Map<String, int> breedDistribution,
  ) {
    switch (_selectedChartIndex) {
      case 0:
        return _buildEnhancedChart(
          context,
          'Gender Distribution',
          _buildGenderPieChart(genderData),
          _buildEnhancedLegend(genderData, _getGenderColors()),
          Icons.wc,
        );
      case 1:
        return _buildEnhancedChart(
          context,
          'Animal Types',
          _buildTypePieChart(typeData),
          _buildEnhancedLegend(typeData, _getTypeColors()),
          Icons.category,
        );
      case 2:
        return _buildEnhancedChart(
          context,
          'Age Distribution',
          _buildUniversalPieChart(ageDistribution, _getAgeColors(), emptyMessage: 'No age data available'),
          _buildEnhancedLegend(ageDistribution, _getAgeColors()),
          Icons.timeline,
        );
      case 3:
        return _buildEnhancedChart(
          context,
          'Breed Distribution',
          _buildBreedPieChart(breedDistribution),
          _buildEnhancedLegend(breedDistribution, _getBreedColors()),
          Icons.pets,
        );
      default:
        return _buildEnhancedChart(
          context,
          'Gender Distribution',
          _buildGenderPieChart(genderData),
          _buildEnhancedLegend(genderData, _getGenderColors()),
          Icons.wc,
        );
    }
  }

  Widget _buildEnhancedChart(
    BuildContext context,
    String title,
    Widget chart,
    Widget? legend,
    IconData icon,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(kBorderRadius * 2), // Use global constant with multiplier
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(kPaddingLarge), // Use global constant
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: UniversalEmptyStateTheme.cattle.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Icon(
                    icon,
                    color: UniversalEmptyStateTheme.cattle,
                    size: 16,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: kSpacingLarge), // Use global constant
            SizedBox(height: _chartHeight, child: Center(child: chart)), // Chart height and centered
            if (legend != null) ...[
              const SizedBox(height: kSpacingMedium), // Use global constant
              legend,
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAgeDemographics(BuildContext context) {
    final youngStock = widget.controller.youngStockCount;
    final matureStock = widget.controller.matureStockCount;
    final seniorStock = widget.controller.seniorStockCount;
    final colors = _getInsightColors();

    return _buildGridSection(
      title: 'Age Demographics',
      subtitle: 'Age distribution and lifecycle stages of your herd',
      icon: Icons.timeline,
      headerColor: AppColors.cattleAgeDemographics,
      // Use responsive crossAxisCount for consistent behavior
      cardData: [
        {
          'title': 'Calves',
          'value': widget.controller.calfCount.toString(),
          'subtitle': '0-6 months old',
          'icon': Icons.child_care,
          'color': colors[0],
          'insight': '${widget.controller.calfPercentage}% of herd',
        },
        {
          'title': 'Young Stock',
          'value': youngStock.toString(),
          'subtitle': '6 months-2 years',
          'icon': Icons.child_friendly,
          'color': colors[1],
          'insight': '${widget.controller.youngStockPercentage}% of herd',
        },
        {
          'title': 'Mature Stock',
          'value': matureStock.toString(),
          'subtitle': '2-8 years old',
          'icon': Icons.pets,
          'color': colors[2],
          'insight': '${widget.controller.matureStockPercentage}% of herd',
        },
        {
          'title': 'Senior Stock',
          'value': seniorStock.toString(),
          'subtitle': '8+ years old',
          'icon': Icons.elderly,
          'color': colors[3],
          'insight': '${widget.controller.seniorStockPercentage}% of herd',
        },
      ],
    );
  }

  Widget _buildCattleFinancialOverview(BuildContext context) {
    final herdValue = widget.controller.herdValue;
    final avgValue = widget.controller.averageValue;

    // Use colors from AppColors instead of hardcoded values
    final financialColors = [
      AppColors.cattleKpiColors[1], // Green
      AppColors.cattleKpiColors[0], // Blue
      AppColors.cattleKpiColors[2], // Purple
      AppColors.cattleKpiColors[5], // Pink
    ];

    return _buildGridSection(
      title: 'Financial Overview',
      subtitle: 'Investment and value metrics for your herd',
      icon: Icons.account_balance_wallet,
      headerColor: AppColors.cattleFinancialOverview,
      // Use responsive crossAxisCount for consistent behavior
      cardData: [
        {
          'title': 'Total Herd Value',
          'value': _formatCurrency(herdValue),
          'subtitle': 'estimated worth',
          'icon': Icons.trending_up,
          'color': financialColors[0],
          'insight': 'Based on purchase prices',
        },
        {
          'title': 'Active Value',
          'value': _formatCurrency(widget.controller.activeCattleValue),
          'subtitle': 'active cattle worth',
          'icon': Icons.check_circle,
          'color': financialColors[1],
          'insight': 'Current active investment',
        },
        {
          'title': 'Average Value',
          'value': _formatCurrency(avgValue),
          'subtitle': 'per head',
          'icon': Icons.attach_money,
          'color': financialColors[2],
          'insight': 'Investment per animal',
        },
        {
          'title': 'Sold Value',
          'value': _formatCurrency(widget.controller.soldCattleValue),
          'subtitle': 'sold cattle worth',
          'icon': Icons.sell,
          'color': financialColors[3],
          'insight': 'Revenue from sales',
        },
      ],
    );
  }











  // All calculation methods moved to CattleController for better architecture









  // Chart builders - now using universal builder
  Widget _buildGenderPieChart(Map<String, int> data) {
    return _buildUniversalPieChart(data, _getGenderColors());
  }

  Widget _buildTypePieChart(Map<String, int> data) {
    return _buildUniversalPieChart(data, _getTypeColors());
  }







  Map<String, Color> _getGenderColors() => AppColors.cattleGenderColors;
  Map<String, Color> _getAgeColors() => AppColors.cattleAgeDistributionColors;
  Map<String, Color> _getTypeColors() => AppColors.cattleAnimalTypeColors;















  Widget _buildBreedPieChart(Map<String, int> data) {
    return _buildUniversalPieChart(data, _getBreedColors(), emptyMessage: 'No breed data available');
  }

  Map<String, Color> _getBreedColors() {
    final breedDistribution = widget.controller.breedDistribution;
    final breedColors = <String, Color>{};

    // Use colors from AppColors instead of hardcoded values
    const availableColors = AppColors.cattleKpiColors;

    int colorIndex = 0;
    for (final breedName in breedDistribution.keys) {
      // Always assign a bright color - don't rely on database colors that might be dark
      breedColors[breedName] = availableColors[colorIndex % availableColors.length];
      colorIndex++;
    }

    // Ensure we always have colors for common breeds using AppColors
    breedColors['Murrah'] = _fallbackColor; // Blue for Murrah
    breedColors['Unknown'] = AppColors.cattleKpiColors[1]; // Green for Unknown

    return breedColors;
  }

  Widget _buildEnhancedLegend(Map<String, int> data, Map<String, Color> colors) {
    return Wrap(
      spacing: 12,
      runSpacing: 8,
      children: data.entries.map((entry) {
        final percentage = widget.controller.totalCattle > 0
          ? ((entry.value / widget.controller.totalCattle) * 100).toStringAsFixed(1)
          : '0';
        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: colors[entry.key]?.withValues(alpha: 0.1) ?? _fallbackColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: colors[entry.key] ?? _fallbackColor,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 6),
              Text(
                '${entry.key} (${entry.value}) $percentage%',
                style: const TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: Colors.black87, // Remove hardcoded grey color
                ),
              ),
            ],
          ),
        );
      }).toList(),
    );
  }

  // Responsive logic is now handled by UniversalLayout and ResponsiveGrid

  // Color Management - Different colors for each KPI card (multi-color rule)
  List<Color> _getKPIColors() => AppColors.cattleKpiColors;
  List<Color> _getInsightColors() => AppColors.cattleAgeInsightColors;




}
