import 'package:flutter/material.dart';
import '../controllers/cattle_controller.dart';
import '../../widgets/index.dart';

class CattleInsightsTab extends StatelessWidget {
  final CattleController controller;

  const CattleInsightsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          _buildHeader(context),
          const SizedBox(height: 24),

          // Cattle Insights
          _buildCattleInsights(context),
          const SizedBox(height: 24),

          // Management Recommendations
          _buildManagementRecommendations(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            UniversalEmptyStateTheme.cattle,
            UniversalEmptyStateTheme.cattle.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: UniversalEmptyStateTheme.cattle.withValues(alpha: 0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Cattle Insights',
                      style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      'Smart recommendations for your cattle management',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              _buildHeaderStat('Total Cattle', controller.totalCattle.toString(), Icons.pets),
              const SizedBox(width: 24),
              _buildHeaderStat('Male', controller.maleCattle.toString(), Icons.male),
              const SizedBox(width: 24),
              _buildHeaderStat('Female', controller.femaleCattle.toString(), Icons.female),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderStat(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: Colors.white.withValues(alpha: 0.8), size: 16),
        const SizedBox(width: 6),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              value,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              label,
              style: TextStyle(
                color: Colors.white.withValues(alpha: 0.8),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildCattleInsights(BuildContext context) {
    final insights = _generateCattleInsights();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Cattle Management Insights',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        if (insights.isEmpty)
          _buildEmptyInsightsCard(context)
        else
          ...insights.map((insight) => _buildInsightCard(context, insight)),
      ],
    );
  }

  Widget _buildManagementRecommendations(BuildContext context) {
    final recommendations = _generateManagementRecommendations();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Management Recommendations',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...recommendations.map((rec) => _buildRecommendationCard(context, rec)),
      ],
    );
  }

  Widget _buildInsightCard(BuildContext context, CattleInsight insight) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(color: insight.color.withValues(alpha: 0.2)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(insight.icon, color: insight.color, size: 24),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    insight.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: insight.priority.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: insight.priority.color.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    insight.priority.label,
                    style: TextStyle(
                      color: insight.priority.color,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              insight.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (insight.recommendations.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Recommendations:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...insight.recommendations.map((rec) => Padding(
                padding: const EdgeInsets.only(left: 16, bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
                    Expanded(child: Text(rec)),
                  ],
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRecommendationCard(BuildContext context, ManagementRecommendation recommendation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: recommendation.color.withValues(alpha: 0.2)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(recommendation.icon, color: recommendation.color, size: 24),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  recommendation.title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            recommendation.description,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyInsightsCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        children: [
          Icon(Icons.lightbulb_outline, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 16),
          Text(
            'No Specific Insights Available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Add more cattle records to unlock personalized insights',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  // Data generation methods
  List<CattleInsight> _generateCattleInsights() {
    final insights = <CattleInsight>[];

    // Herd Size Insights
    if (controller.totalCattle == 0) {
      insights.add(CattleInsight(
        title: 'Start Your Cattle Management Journey',
        description: 'You haven\'t added any cattle records yet. Start by adding your first cattle to begin tracking and managing your herd.',
        icon: Icons.pets,
        color: UniversalEmptyStateTheme.cattle,
        priority: InsightPriority.high,
        recommendations: [
          'Add cattle records with complete information',
          'Include tag IDs for easy identification',
          'Record gender and animal type for better analytics',
        ],
      ));
    } else if (controller.totalCattle < 5) {
      insights.add(CattleInsight(
        title: 'Small Herd Management',
        description: 'You have a small herd of ${controller.totalCattle} cattle. This is perfect for detailed individual tracking and care.',
        icon: Icons.pets,
        color: Colors.green,
        priority: InsightPriority.medium,
        recommendations: [
          'Focus on individual health monitoring',
          'Maintain detailed breeding records',
          'Consider expansion planning',
        ],
      ));
    } else if (controller.totalCattle > 50) {
      insights.add(CattleInsight(
        title: 'Large Herd Management',
        description: 'You have a large herd of ${controller.totalCattle} cattle. Consider implementing systematic management practices.',
        icon: Icons.groups,
        color: Colors.orange,
        priority: InsightPriority.high,
        recommendations: [
          'Implement batch management systems',
          'Use automated tracking where possible',
          'Regular health screening schedules',
          'Consider herd segmentation strategies',
        ],
      ));
    }

    // Gender Distribution Insights
    final genderInsight = _analyzeGenderDistribution();
    if (genderInsight != null) {
      insights.add(genderInsight);
    }

    // Type Distribution Insights
    final typeInsight = _analyzeTypeDistribution();
    if (typeInsight != null) {
      insights.add(typeInsight);
    }

    return insights;
  }

  List<ManagementRecommendation> _generateManagementRecommendations() {
    return [
      ManagementRecommendation(
        title: 'Record Keeping Best Practices',
        description: 'Maintain comprehensive records to optimize your cattle management and ensure regulatory compliance.',
        icon: Icons.assignment,
        color: Colors.blue,
      ),
      ManagementRecommendation(
        title: 'Regular Health Monitoring',
        description: 'Establish a routine health monitoring schedule to catch issues early and maintain herd health.',
        icon: Icons.health_and_safety,
        color: UniversalEmptyStateTheme.health,
      ),
      ManagementRecommendation(
        title: 'Identification System',
        description: 'Ensure all cattle have proper identification tags for easy tracking and management.',
        icon: Icons.qr_code,
        color: UniversalEmptyStateTheme.cattle,
      ),
      ManagementRecommendation(
        title: 'Data Backup',
        description: 'Regularly backup your cattle data to prevent loss of important information.',
        icon: Icons.backup,
        color: Colors.green,
      ),
    ];
  }

  CattleInsight? _analyzeGenderDistribution() {
    if (controller.totalCattle < 2) return null;

    final maleCount = controller.maleCattle;
    final maleRatio = maleCount / controller.totalCattle;

    if (maleRatio > 0.7) {
      return CattleInsight(
        title: 'High Male Ratio Detected',
        description: 'Your herd has a high proportion of males (${(maleRatio * 100).toStringAsFixed(1)}%). This may impact breeding efficiency.',
        icon: Icons.male,
        color: Colors.blue,
        priority: InsightPriority.medium,
        recommendations: [
          'Consider increasing female cattle for breeding',
          'Evaluate market strategy for male cattle',
          'Plan breeding program optimization',
        ],
      );
    } else if (maleRatio < 0.1 && maleCount == 0) {
      return CattleInsight(
        title: 'No Male Cattle Detected',
        description: 'Your herd has no male cattle. Consider breeding requirements and genetic diversity.',
        icon: Icons.male,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Consider acquiring breeding bulls',
          'Evaluate artificial insemination options',
          'Plan for genetic diversity maintenance',
        ],
      );
    }

    return null;
  }

  CattleInsight? _analyzeTypeDistribution() {
    final typeData = controller.cattleByType;
    if (typeData.isEmpty) return null;

    final hasUnknownType = typeData.containsKey('Unknown') && typeData['Unknown']! > 0;

    if (hasUnknownType) {
      final unknownCount = typeData['Unknown']!;
      final unknownPercentage = (unknownCount / controller.totalCattle) * 100;

      return CattleInsight(
        title: 'Incomplete Type Information',
        description: '$unknownCount cattle (${unknownPercentage.toStringAsFixed(1)}%) have unknown animal types. Complete this information for better management.',
        icon: Icons.help_outline,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Update animal type information for all cattle',
          'Use standardized type classifications',
          'Ensure consistent data entry practices',
        ],
      );
    }

    return null;
  }
}

// Data classes for cattle insights
class CattleInsight {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final InsightPriority priority;
  final List<String> recommendations;

  CattleInsight({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.priority,
    this.recommendations = const [],
  });
}

class ManagementRecommendation {
  final String title;
  final String description;
  final IconData icon;
  final Color color;

  ManagementRecommendation({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
  });
}

enum InsightPriority {
  low(Colors.green, 'Low'),
  medium(Colors.orange, 'Medium'),
  high(Colors.red, 'High');

  const InsightPriority(this.color, this.label);
  final Color color;
  final String label;
}