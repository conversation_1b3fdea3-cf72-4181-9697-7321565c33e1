import 'package:flutter/material.dart';
import '../controllers/cattle_controller.dart';
import '../../widgets/universal_record_card.dart';
import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';

import '../../widgets/filters/filters.dart';
import '../../widgets/filters/filter_layout.dart';
import '../../widgets/filters/filter_widgets.dart';
import '../details/cattle_detail_screen.dart';
import '../dialogs/cattle_form_dialog.dart';
import '../models/cattle_isar.dart';
import '../utils/cattle_age_calculator.dart';

class CattleRecordsTab extends StatefulWidget {
  final CattleController controller;

  const CattleRecordsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<CattleRecordsTab> createState() => _CattleRecordsTabState();
}

class _CattleRecordsTabState extends State<CattleRecordsTab> {
  late FilterController _filterController;

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  void _onFiltersChanged() {
    setState(() {
      // Trigger rebuild when filters change
    });
  }

  List<CattleIsar> get filteredCattle {
    List<CattleIsar> result = List.from(widget.controller.cattle);

    // Apply search filter using new handler
    result = FilterHandlers.applySearch<CattleIsar>(
      items: result,
      searchQuery: _filterController.searchQuery,
      searchFields: [
        (cattle) => cattle.name ?? '',
        (cattle) => cattle.tagId ?? '',
      ],
    );

    // Apply date range filter using new handler
    result = FilterHandlers.applyDateFilter<CattleIsar>(
      items: result,
      startDate: _filterController.startDate,
      endDate: _filterController.endDate,
      dateExtractor: (cattle) => cattle.dateOfBirth ?? cattle.purchaseDate ?? cattle.createdAt,
    );

    // Apply sorting using new handler
    result = FilterHandlers.applySort<CattleIsar>(
      items: result,
      sortField: _filterController.sortBy,
      isAscending: _filterController.isAscending,
      sortComparators: {
        'name': FilterHandlers.stringComparator<CattleIsar>((cattle) => cattle.name?.toLowerCase() ?? ''),
        'tagId': FilterHandlers.stringComparator<CattleIsar>((cattle) => cattle.tagId?.toLowerCase() ?? ''),
        'dateOfBirth': FilterHandlers.dateComparator<CattleIsar>((cattle) => cattle.dateOfBirth ?? DateTime(1900)),
        'purchaseDate': FilterHandlers.dateComparator<CattleIsar>((cattle) => cattle.purchaseDate ?? DateTime(1900)),
      },
    );

    // Apply global filters using new handler
    result = FilterHandlers.applyGlobalFilters<CattleIsar>(
      items: result,
      activeFilters: _filterController.globalFilters,
      filterPredicates: {
        AppFilterWidget.animalTypeKey: (cattle, value) {
          if (value == 'All') return true;
          return cattle.animalTypeId == value;
        },
        AppFilterWidget.breedKey: (cattle, value) {
          if (value == 'All') return true;
          return cattle.breedId == value;
        },
        AppFilterWidget.genderKey: (cattle, value) {
          if (value == 'All') return true;
          return cattle.gender?.toLowerCase() == value.toString().toLowerCase();
        },
        AppFilterWidget.cattleKey: (cattle, value) {
          if (value == 'All') return true;
          // Individual cattle selection - match by business ID, name, or tag ID
          return cattle.businessId == value ||
                 cattle.name == value ||
                 cattle.tagId == value;
        },
        AppFilterWidget.ageGroupKey: (cattle, value) {
          return CattleAgeCalculator.matchesAgeGroup(cattle, value);
        },
        // Status filter removed - keeping only 5 filters
      },
    );

    return result;
  }















  @override
  Widget build(BuildContext context) {
    return _buildContent();
  }

  Widget _buildContent() {
    // Check if we have data to display
    if (widget.controller.totalCattle == 0) {
      return _buildEmptyState(true); // Use consolidated empty state method
    }

    final allCattle = widget.controller.cattle;
    final cattle = filteredCattle;

    return Column(
      children: [
        // Universal Filter Layout with new consolidated system
        UniversalFilterLayout(
          controller: _filterController,
          theme: FilterTheme.cattle,
          moduleName: 'cattle',
          sortFields: [...SortField.commonFields, ...SortField.cattleFields],
          searchHint: 'Search cattle by name or tag ID...',
          totalCount: allCattle.length,
          filteredCount: cattle.length,
        ),

        // Cattle List
        Expanded(
          child: cattle.isEmpty
              ? _buildEmptyState(allCattle.isEmpty)
              : RefreshIndicator(
                  onRefresh: () async {
                    await widget.controller.refresh();
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.all(kPaddingMedium),
                    itemCount: cattle.length,
                    itemBuilder: (context, index) {
                      final cattleItem = cattle[index];
                      return _buildCattleCard(cattleItem);
                    },
                  ),
                ),
        ),
      ],
    );
  }



  /// Consolidated empty state builder - single source of truth for all empty states
  ///
  /// [isCompletelyEmpty] - true when no cattle exist at all, false when cattle exist but filters hide them
  Widget _buildEmptyState(bool isCompletelyEmpty) {
    // Get the tab color for Records tab (index 1) from cattle module
    const tabColor = Color(0xFF2E7D32); // Green color for Records tab

    if (isCompletelyEmpty) {
      // No cattle records exist - show call-to-action to add first cattle
      return UniversalTabEmptyState.forTab(
        title: 'No Cattle Records',
        message: 'Add your first cattle to start managing your herd.',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        moduleName: 'cattle',
        action: TabEmptyStateActions.addFirstRecord(
          onPressed: () => _showAddCattleDialog(),
          tabColor: tabColor,
        ),
      );
    } else {
      // Cattle exist but are filtered out - show filter adjustment message
      return UniversalTabEmptyState.forTab(
        title: 'No Matching Cattle',
        message: 'Try adjusting your filters or search terms',
        tabColor: tabColor,
        tabIndex: 1, // Records tab
        moduleName: 'cattle',
        action: TabEmptyStateActions.clearFilters(
          onPressed: () => _clearAllFilters(),
          tabColor: tabColor,
        ),
      );
    }
  }

  Widget _buildCattleCard(CattleIsar cattle) {
    final animalType = widget.controller.getAnimalType(cattle.animalTypeId);
    final breed = widget.controller.getBreed(cattle.breedId);

    // Calculate age with unified logic
    String age = CattleAgeCalculator.calculateAgeDisplay(cattle);

    // Format row 1: Name (TagID) + age
    String nameWithTag = cattle.name ?? 'Unnamed Cattle';
    if (cattle.tagId != null && cattle.tagId!.isNotEmpty) {
      nameWithTag += ' (${cattle.tagId!.toUpperCase()})';
    }

    // Format row 2: Animal type and breed
    String animalTypeText = animalType?.name ?? 'Unknown type';
    String breedText = breed?.name ?? 'Unknown breed';

    return UniversalRecordCard(
      row1Left: nameWithTag,
      row1Right: age,
      row1LeftIcon: Icons.pets,
      row1RightIcon: Icons.cake,
      row2Left: animalTypeText,
      row2Right: breedText,
      row2LeftIcon: Icons.category,
      row2RightIcon: Icons.pets,
      notes: cattle.notes?.isNotEmpty == true ? cattle.notes : null,
      primaryColor: AppColors.cattleHeader,
      onTap: () => _navigateToCattleDetail(cattle),
      onEdit: () => _showEditCattleDialog(cattle),
      onDelete: () => _showDeleteConfirmation(cattle),
    );
  }



  void _navigateToCattleDetail(CattleIsar cattle) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CattleDetailScreen(
          existingCattle: cattle,
          businessId: widget.controller.businessId,
          onCattleUpdated: (updatedCattle) {
            widget.controller.updateCattle(updatedCattle);
          },
        ),
      ),
    );
  }

  /// Clear all active filters
  void _clearAllFilters() {
    _filterController.clearAllApplied();
  }

  /// Consolidated dialog helper for both add and edit operations
  ///
  /// [cattle] - null for add operation, existing cattle for edit operation
  void _showCattleFormDialog([CattleIsar? cattle]) {
    final isEditing = cattle != null;

    showDialog(
      context: context,
      builder: (context) => CattleFormDialog(
        cattle: cattle, // null for add, existing cattle for edit
        existingCattle: widget.controller.cattle,
        businessId: widget.controller.businessId,
        animalTypes: widget.controller.animalTypes,
        onSave: (cattleData) async {
          if (isEditing) {
            await widget.controller.updateCattle(cattleData);
          } else {
            await widget.controller.addCattle(cattleData);
          }
        },
      ),
    );
  }

  /// Show dialog to add new cattle
  void _showAddCattleDialog() => _showCattleFormDialog();

  /// Show dialog to edit existing cattle
  void _showEditCattleDialog(CattleIsar cattle) => _showCattleFormDialog(cattle);

  void _showDeleteConfirmation(CattleIsar cattle) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Cattle'),
        content: Text('Are you sure you want to delete ${cattle.name ?? 'this cattle'}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await widget.controller.deleteCattle(cattle.businessId!);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}