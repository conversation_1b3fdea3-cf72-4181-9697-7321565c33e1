import 'package:flutter/material.dart';
import '../tabs/all_events_tab.dart';
import '../tabs/event_history_tab.dart';
import '../tabs/event_alerts_tab.dart';

class EventsScreen extends StatefulWidget {
  const EventsScreen({Key? key}) : super(key: key);

  @override
  State<EventsScreen> createState() => _EventsScreenState();
}

class _EventsScreenState extends State<EventsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Events'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'All Events'),
            Tab(text: 'History'),
            Tab(text: 'Alerts'),
          ],
        ),
      ),
      body: Tab<PERSON><PERSON><PERSON><PERSON><PERSON>(
        controller: _tabController,
        children: const [
          AllEventsTab(),
          EventHistoryTab(),
          EventAlertsTab(),
        ],
      ),
    );
  }
}
