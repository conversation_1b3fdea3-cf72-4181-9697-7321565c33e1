import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';
import '../models/event_isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';
import '../../../Dashboard/Cattle/models/cattle_isar.dart';

/// Consolidated handler for all Events module database operations
class EventsHandler {
  static final Logger _logger = Logger('EventsHandler');
  final IsarService _isarService;

  // Singleton instance
  static final EventsHandler _instance = EventsHandler._internal();
  static EventsHandler get instance => _instance;

  // Private constructor
  EventsHandler._internal() : _isarService = GetIt.instance<IsarService>();

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== EVENTS ===//

  /// Get all events
  Future<List<EventIsar>> getAllEvents() async {
    try {
      return await _isar.eventIsars.where().sortByEventDateDesc().findAll();
    } catch (e) {
      _logger.severe('Error getting all events: $e');
      throw DatabaseException('Failed to retrieve events', e.toString());
    }
  }

  /// Get events for a date range
  Future<List<EventIsar>> getEventsForDateRange(
      DateTime startDate, DateTime endDate) async {
    try {
      return await _isar.eventIsars
          .filter()
          .eventDateBetween(startDate, endDate)
          .sortByEventDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting events for date range: $e');
      throw DatabaseException('Failed to retrieve events', e.toString());
    }
  }

  /// Get events by type
  Future<List<EventIsar>> getEventsByType(EventType type) async {
    try {
      return await _isar.eventIsars
          .filter()
          .type((q) => q.valueEqualTo(type.toString()))
          .sortByEventDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting events by type: $e');
      throw DatabaseException('Failed to retrieve events', e.toString());
    }
  }

  /// Get events by cattle
  Future<List<EventIsar>> getEventsByCattle(String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.eventIsars
          .filter()
          .cattleIdEqualTo(cattleId)
          .sortByEventDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting events by cattle $cattleId: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to retrieve events', e.toString());
    }
  }

  /// Add a new event
  Future<void> addEvent(EventIsar event) async {
    try {
      await _validateEvent(event, isNew: true);

      await _isar.writeTxn(() async {
        await _isar.eventIsars.put(event);
      });

      _logger.info('Successfully added event: ${event.businessId}');
    } catch (e) {
      _logger.severe('Error adding event: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to add event', e.toString());
    }
  }

  /// Update an existing event
  Future<void> updateEvent(EventIsar event) async {
    try {
      await _validateEvent(event, isNew: false);

      await _isar.writeTxn(() async {
        await _isar.eventIsars.put(event);
      });

      _logger.info('Successfully updated event: ${event.businessId}');
    } catch (e) {
      _logger.severe('Error updating event: $e');
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to update event', e.toString());
    }
  }

  /// Delete an event
  Future<void> deleteEvent(String businessId) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Event ID is required');
      }

      await _isar.writeTxn(() async {
        final event = await _isar.eventIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (event == null) {
          throw RecordNotFoundException('Event not found: $businessId');
        }

        await _isar.eventIsars.delete(event.id);
      });

      _logger.info('Successfully deleted event: $businessId');
    } catch (e) {
      _logger.severe('Error deleting event: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete event', e.toString());
    }
  }

  /// Mark event as completed
  Future<void> markEventAsCompleted(String businessId, bool completed) async {
    try {
      if (businessId.isEmpty) {
        throw ValidationException('Event ID is required');
      }

      await _isar.writeTxn(() async {
        final event = await _isar.eventIsars
            .filter()
            .businessIdEqualTo(businessId)
            .findFirst();

        if (event == null) {
          throw RecordNotFoundException('Event not found: $businessId');
        }

        event.isCompleted = completed;
        event.updatedAt = DateTime.now();

        await _isar.eventIsars.put(event);
      });

      _logger.info('Successfully updated event completion status: $businessId');
    } catch (e) {
      _logger.severe('Error updating event completion status: $businessId: $e');
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to update event', e.toString());
    }
  }

  //=== PRIVATE HELPER METHODS ===//

  /// Validate event
  Future<void> _validateEvent(EventIsar event, {required bool isNew}) async {
    if (event.title == null || event.title!.isEmpty) {
      throw ValidationException('Event title is required');
    }

    if (event.eventDate == null) {
      throw ValidationException('Event date is required');
    }

    // Validate cattle exists if specified
    if (event.cattleId != null && event.cattleId!.isNotEmpty) {
      final cattle = await _isar
          .collection<CattleIsar>()
          .filter()
          .businessIdEqualTo(event.cattleId!)
          .findFirst();

      if (cattle == null) {
        throw ValidationException('Invalid cattle ID');
      }
    }

    // Set default values for new records
    if (isNew) {
      event.createdAt = DateTime.now();
      event.updatedAt = DateTime.now();
    } else {
      event.updatedAt = DateTime.now();
    }
  }
}
