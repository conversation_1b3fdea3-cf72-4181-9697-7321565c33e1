import 'package:flutter/material.dart';
import '../models/event_isar.dart';
import '../dialogs/event_form_dialog.dart';
import '../../../services/database/database_helper.dart';

class AllEventsTab extends StatefulWidget {
  const AllEventsTab({Key? key}) : super(key: key);

  @override
  State<AllEventsTab> createState() => _AllEventsTabState();
}

class _AllEventsTabState extends State<AllEventsTab> {
  final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  String _selectedType = 'all';
  String _searchQuery = '';
  List<EventIsar> _events = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadEvents();
  }

  Future<void> _loadEvents() async {
    try {
      setState(() => _isLoading = true);
      final events = await _dbHelper.eventsHandler.getAllEvents();
      
      setState(() {
        _events = events;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading events: $e');
      setState(() {
        _events = [];
        _isLoading = false;
      });
    }
  }

  List<EventIsar> _getFilteredEvents() {
    return _events.where((event) {
      bool matchesType = _selectedType == 'all' || 
          (event.type != null && 
          event.type!.value != null && 
          event.type!.value!.contains(_selectedType));
      
      bool matchesSearch = _searchQuery.isEmpty ||
          (event.title != null && event.title!.toLowerCase().contains(_searchQuery.toLowerCase())) ||
          (event.notes != null && event.notes!.toLowerCase().contains(_searchQuery.toLowerCase()));
      
      return matchesType && matchesSearch;
    }).toList();
  }

  Future<void> _addEvent(EventIsar event) async {
    await _dbHelper.eventsHandler.addEvent(event);
    _loadEvents();
  }

  Future<void> _updateEvent(EventIsar updatedEvent) async {
    await _dbHelper.eventsHandler.updateEvent(updatedEvent);
    _loadEvents();
  }

  Future<void> _deleteEvent(String eventId) async {
    await _dbHelper.eventsHandler.deleteEvent(eventId);
    _loadEvents();
  }

  Future<void> _toggleEventCompletion(String businessId) async {
    final event = _events.firstWhere((e) => e.businessId == businessId);
    await _dbHelper.eventsHandler.markEventAsCompleted(businessId, !event.isCompleted);
    _loadEvents();
  }

  Future<void> _showEventDialog(BuildContext context, {EventIsar? event, String cattleId = ''}) async {
    final result = await showDialog<EventIsar>(
      context: context,
      builder: (context) => EventFormDialog(
        event: event,
        cattleId: cattleId,
      ),
    );

    if (result != null) {
      if (event != null) {
        await _updateEvent(result);
      } else {
        await _addEvent(result);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final filteredEvents = _getFilteredEvents();

    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return Column(
      children: [
        // Event filters
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 16.0),
          child: Row(
            children: [
              Expanded(
                flex: 2,
                child: DropdownButtonFormField<String>(
                  isExpanded: true,
                  decoration: const InputDecoration(
                    labelText: 'Filter by Type',
                    border: OutlineInputBorder(),
                  ),
                  value: _selectedType,
                  items: [
                    const DropdownMenuItem(
                      value: 'all',
                      child: Text('All Events'),
                    ),
                    ...EventType.values.map((type) {
                      final name = type.toString().split('.').last;
                      return DropdownMenuItem(
                        value: name,
                        child: Text(name),
                      );
                    }),
                  ],
                  onChanged: (value) {
                    setState(() => _selectedType = value!);
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                flex: 3,
                child: TextField(
                  decoration: const InputDecoration(
                    labelText: 'Search Events',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.search),
                  ),
                  onChanged: (value) {
                    setState(() => _searchQuery = value);
                  },
                ),
              ),
            ],
          ),
        ),
        // Events list
        Expanded(
          child: filteredEvents.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.event_busy,
                        size: 64,
                        color: Theme.of(context).primaryColor.withAlpha(128),
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'No events found',
                        style: TextStyle(fontSize: 18),
                      ),
                      const SizedBox(height: 8),
                      ElevatedButton.icon(
                        onPressed: () => _showEventDialog(context),
                        icon: const Icon(Icons.add),
                        label: const Text('Add Event'),
                      ),
                    ],
                  ),
                )
              : ListView.builder(
                  itemCount: filteredEvents.length,
                  itemBuilder: (context, index) {
                    final event = filteredEvents[index];
                    final icon = event.type?.getIcon() ?? Icons.event;
                    final color = event.type?.getColor() ?? Colors.grey;
                    
                    return Card(
                      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: color.withAlpha(40),
                          child: Icon(icon, color: color),
                        ),
                        title: Text(
                          event.title ?? 'Untitled Event',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            decoration: event.isCompleted ? TextDecoration.lineThrough : null,
                          ),
                        ),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (event.type != null)
                              Text(event.type!.getDisplayName()),
                            if (event.eventDate != null)
                              Text(
                                '${event.eventDate!.toLocal().toString().split(' ')[0]} ${event.time?.toTimeOfDay().format(context) ?? ''}',
                              ),
                          ],
                        ),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: Icon(
                                event.isCompleted
                                    ? Icons.check_circle
                                    : Icons.check_circle_outline,
                                color: event.isCompleted ? Colors.green : Colors.grey,
                              ),
                              onPressed: () {
                                if (event.businessId != null) {
                                  _toggleEventCompletion(event.businessId!);
                                }
                              },
                            ),
                            PopupMenuButton<String>(
                              itemBuilder: (context) => [
                                const PopupMenuItem<String>(
                                  value: 'edit',
                                  child: Text('Edit'),
                                ),
                                const PopupMenuItem<String>(
                                  value: 'delete',
                                  child: Text('Delete'),
                                ),
                              ],
                              onSelected: (value) async {
                                if (value == 'edit') {
                                  _showEventDialog(context, event: event);
                                } else if (value == 'delete' && event.businessId != null) {
                                  _deleteEvent(event.businessId!);
                                }
                              },
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
        ),
      ],
    );
  }
}

