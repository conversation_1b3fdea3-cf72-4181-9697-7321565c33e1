// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'animal_type_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetAnimalTypeIsarCollection on Isar {
  IsarCollection<AnimalTypeIsar> get animalTypeIsars => this.collection();
}

const AnimalTypeIsarSchema = CollectionSchema(
  name: r'AnimalTypeIsar',
  id: -6865362688294164439,
  properties: {
    r'businessId': PropertySchema(
      id: 0,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'colorValue': PropertySchema(
      id: 1,
      name: r'colorValue',
      type: IsarType.long,
    ),
    r'createdAt': PropertySchema(
      id: 2,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'defaultBreedingAge': PropertySchema(
      id: 3,
      name: r'defaultBreedingAge',
      type: IsarType.long,
    ),
    r'defaultEmptyPeriodDays': PropertySchema(
      id: 4,
      name: r'defaultEmptyPeriodDays',
      type: IsarType.long,
    ),
    r'defaultGestationDays': PropertySchema(
      id: 5,
      name: r'defaultGestationDays',
      type: IsarType.long,
    ),
    r'defaultHeatCycleDays': PropertySchema(
      id: 6,
      name: r'defaultHeatCycleDays',
      type: IsarType.long,
    ),
    r'iconCodePoint': PropertySchema(
      id: 7,
      name: r'iconCodePoint',
      type: IsarType.long,
    ),
    r'iconFontFamily': PropertySchema(
      id: 8,
      name: r'iconFontFamily',
      type: IsarType.string,
    ),
    r'name': PropertySchema(
      id: 9,
      name: r'name',
      type: IsarType.string,
    ),
    r'updatedAt': PropertySchema(
      id: 10,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _animalTypeIsarEstimateSize,
  serialize: _animalTypeIsarSerialize,
  deserialize: _animalTypeIsarDeserialize,
  deserializeProp: _animalTypeIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'name': IndexSchema(
      id: 879695947855722453,
      name: r'name',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'name',
          type: IndexType.hash,
          caseSensitive: false,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _animalTypeIsarGetId,
  getLinks: _animalTypeIsarGetLinks,
  attach: _animalTypeIsarAttach,
  version: '3.1.0+1',
);

int _animalTypeIsarEstimateSize(
  AnimalTypeIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.iconFontFamily;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.name;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _animalTypeIsarSerialize(
  AnimalTypeIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeString(offsets[0], object.businessId);
  writer.writeLong(offsets[1], object.colorValue);
  writer.writeDateTime(offsets[2], object.createdAt);
  writer.writeLong(offsets[3], object.defaultBreedingAge);
  writer.writeLong(offsets[4], object.defaultEmptyPeriodDays);
  writer.writeLong(offsets[5], object.defaultGestationDays);
  writer.writeLong(offsets[6], object.defaultHeatCycleDays);
  writer.writeLong(offsets[7], object.iconCodePoint);
  writer.writeString(offsets[8], object.iconFontFamily);
  writer.writeString(offsets[9], object.name);
  writer.writeDateTime(offsets[10], object.updatedAt);
}

AnimalTypeIsar _animalTypeIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = AnimalTypeIsar();
  object.businessId = reader.readStringOrNull(offsets[0]);
  object.colorValue = reader.readLongOrNull(offsets[1]);
  object.createdAt = reader.readDateTimeOrNull(offsets[2]);
  object.defaultBreedingAge = reader.readLongOrNull(offsets[3]);
  object.defaultEmptyPeriodDays = reader.readLongOrNull(offsets[4]);
  object.defaultGestationDays = reader.readLongOrNull(offsets[5]);
  object.defaultHeatCycleDays = reader.readLongOrNull(offsets[6]);
  object.iconCodePoint = reader.readLongOrNull(offsets[7]);
  object.iconFontFamily = reader.readStringOrNull(offsets[8]);
  object.id = id;
  object.name = reader.readStringOrNull(offsets[9]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[10]);
  return object;
}

P _animalTypeIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readStringOrNull(offset)) as P;
    case 1:
      return (reader.readLongOrNull(offset)) as P;
    case 2:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 3:
      return (reader.readLongOrNull(offset)) as P;
    case 4:
      return (reader.readLongOrNull(offset)) as P;
    case 5:
      return (reader.readLongOrNull(offset)) as P;
    case 6:
      return (reader.readLongOrNull(offset)) as P;
    case 7:
      return (reader.readLongOrNull(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readStringOrNull(offset)) as P;
    case 10:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _animalTypeIsarGetId(AnimalTypeIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _animalTypeIsarGetLinks(AnimalTypeIsar object) {
  return [];
}

void _animalTypeIsarAttach(
    IsarCollection<dynamic> col, Id id, AnimalTypeIsar object) {
  object.id = id;
}

extension AnimalTypeIsarByIndex on IsarCollection<AnimalTypeIsar> {
  Future<AnimalTypeIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  AnimalTypeIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<AnimalTypeIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<AnimalTypeIsar?> getAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(AnimalTypeIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(AnimalTypeIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<AnimalTypeIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<AnimalTypeIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension AnimalTypeIsarQueryWhereSort
    on QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QWhere> {
  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension AnimalTypeIsarQueryWhere
    on QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QWhereClause> {
  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause> idNotEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause> idGreaterThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause> idLessThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause> nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'name',
        value: [null],
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause>
      nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'name',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause> nameEqualTo(
      String? name) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'name',
        value: [name],
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterWhereClause>
      nameNotEqualTo(String? name) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [],
              upper: [name],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [name],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [name],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'name',
              lower: [],
              upper: [name],
              includeUpper: false,
            ));
      }
    });
  }
}

extension AnimalTypeIsarQueryFilter
    on QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QFilterCondition> {
  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      colorValueIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'colorValue',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      colorValueIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'colorValue',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      colorValueEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'colorValue',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      colorValueGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'colorValue',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      colorValueLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'colorValue',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      colorValueBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'colorValue',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultBreedingAgeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'defaultBreedingAge',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultBreedingAgeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'defaultBreedingAge',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultBreedingAgeEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'defaultBreedingAge',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultBreedingAgeGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'defaultBreedingAge',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultBreedingAgeLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'defaultBreedingAge',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultBreedingAgeBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'defaultBreedingAge',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultEmptyPeriodDaysIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'defaultEmptyPeriodDays',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultEmptyPeriodDaysIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'defaultEmptyPeriodDays',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultEmptyPeriodDaysEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'defaultEmptyPeriodDays',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultEmptyPeriodDaysGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'defaultEmptyPeriodDays',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultEmptyPeriodDaysLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'defaultEmptyPeriodDays',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultEmptyPeriodDaysBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'defaultEmptyPeriodDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultGestationDaysIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'defaultGestationDays',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultGestationDaysIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'defaultGestationDays',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultGestationDaysEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'defaultGestationDays',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultGestationDaysGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'defaultGestationDays',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultGestationDaysLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'defaultGestationDays',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultGestationDaysBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'defaultGestationDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultHeatCycleDaysIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'defaultHeatCycleDays',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultHeatCycleDaysIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'defaultHeatCycleDays',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultHeatCycleDaysEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'defaultHeatCycleDays',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultHeatCycleDaysGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'defaultHeatCycleDays',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultHeatCycleDaysLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'defaultHeatCycleDays',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      defaultHeatCycleDaysBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'defaultHeatCycleDays',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconCodePointIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'iconCodePoint',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconCodePointIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'iconCodePoint',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconCodePointEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'iconCodePoint',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconCodePointGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'iconCodePoint',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconCodePointLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'iconCodePoint',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconCodePointBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'iconCodePoint',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconFontFamilyIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'iconFontFamily',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconFontFamilyIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'iconFontFamily',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconFontFamilyEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'iconFontFamily',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconFontFamilyGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'iconFontFamily',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconFontFamilyLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'iconFontFamily',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconFontFamilyBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'iconFontFamily',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconFontFamilyStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'iconFontFamily',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconFontFamilyEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'iconFontFamily',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconFontFamilyContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'iconFontFamily',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconFontFamilyMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'iconFontFamily',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconFontFamilyIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'iconFontFamily',
        value: '',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      iconFontFamilyIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'iconFontFamily',
        value: '',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition> idEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      nameIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      nameIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'name',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      nameEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      nameGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      nameLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      nameBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'name',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      nameStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      nameEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      nameContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'name',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      nameMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'name',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      nameIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      nameIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'name',
        value: '',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension AnimalTypeIsarQueryObject
    on QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QFilterCondition> {}

extension AnimalTypeIsarQueryLinks
    on QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QFilterCondition> {}

extension AnimalTypeIsarQuerySortBy
    on QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QSortBy> {
  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByColorValue() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'colorValue', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByColorValueDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'colorValue', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy> sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByDefaultBreedingAge() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultBreedingAge', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByDefaultBreedingAgeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultBreedingAge', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByDefaultEmptyPeriodDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultEmptyPeriodDays', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByDefaultEmptyPeriodDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultEmptyPeriodDays', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByDefaultGestationDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultGestationDays', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByDefaultGestationDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultGestationDays', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByDefaultHeatCycleDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultHeatCycleDays', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByDefaultHeatCycleDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultHeatCycleDays', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByIconCodePoint() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconCodePoint', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByIconCodePointDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconCodePoint', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByIconFontFamily() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconFontFamily', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByIconFontFamilyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconFontFamily', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy> sortByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy> sortByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy> sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension AnimalTypeIsarQuerySortThenBy
    on QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QSortThenBy> {
  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByColorValue() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'colorValue', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByColorValueDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'colorValue', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy> thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByDefaultBreedingAge() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultBreedingAge', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByDefaultBreedingAgeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultBreedingAge', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByDefaultEmptyPeriodDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultEmptyPeriodDays', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByDefaultEmptyPeriodDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultEmptyPeriodDays', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByDefaultGestationDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultGestationDays', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByDefaultGestationDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultGestationDays', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByDefaultHeatCycleDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultHeatCycleDays', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByDefaultHeatCycleDaysDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'defaultHeatCycleDays', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByIconCodePoint() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconCodePoint', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByIconCodePointDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconCodePoint', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByIconFontFamily() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconFontFamily', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByIconFontFamilyDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'iconFontFamily', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy> thenByName() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy> thenByNameDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'name', Sort.desc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy> thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension AnimalTypeIsarQueryWhereDistinct
    on QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QDistinct> {
  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QDistinct> distinctByBusinessId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QDistinct>
      distinctByColorValue() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'colorValue');
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QDistinct>
      distinctByDefaultBreedingAge() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'defaultBreedingAge');
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QDistinct>
      distinctByDefaultEmptyPeriodDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'defaultEmptyPeriodDays');
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QDistinct>
      distinctByDefaultGestationDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'defaultGestationDays');
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QDistinct>
      distinctByDefaultHeatCycleDays() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'defaultHeatCycleDays');
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QDistinct>
      distinctByIconCodePoint() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'iconCodePoint');
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QDistinct>
      distinctByIconFontFamily({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'iconFontFamily',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QDistinct> distinctByName(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'name', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension AnimalTypeIsarQueryProperty
    on QueryBuilder<AnimalTypeIsar, AnimalTypeIsar, QQueryProperty> {
  QueryBuilder<AnimalTypeIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<AnimalTypeIsar, String?, QQueryOperations> businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<AnimalTypeIsar, int?, QQueryOperations> colorValueProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'colorValue');
    });
  }

  QueryBuilder<AnimalTypeIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<AnimalTypeIsar, int?, QQueryOperations>
      defaultBreedingAgeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'defaultBreedingAge');
    });
  }

  QueryBuilder<AnimalTypeIsar, int?, QQueryOperations>
      defaultEmptyPeriodDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'defaultEmptyPeriodDays');
    });
  }

  QueryBuilder<AnimalTypeIsar, int?, QQueryOperations>
      defaultGestationDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'defaultGestationDays');
    });
  }

  QueryBuilder<AnimalTypeIsar, int?, QQueryOperations>
      defaultHeatCycleDaysProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'defaultHeatCycleDays');
    });
  }

  QueryBuilder<AnimalTypeIsar, int?, QQueryOperations> iconCodePointProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'iconCodePoint');
    });
  }

  QueryBuilder<AnimalTypeIsar, String?, QQueryOperations>
      iconFontFamilyProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'iconFontFamily');
    });
  }

  QueryBuilder<AnimalTypeIsar, String?, QQueryOperations> nameProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'name');
    });
  }

  QueryBuilder<AnimalTypeIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
