import 'package:flutter/material.dart';
import '../models/alert_settings_isar.dart';
import '../services/farm_setup_handler.dart';
import 'package:logging/logging.dart';
import '../../../constants/app_colors.dart';

class AlertsSettingsScreen extends StatefulWidget {
  const AlertsSettingsScreen({Key? key}) : super(key: key);

  @override
  State<AlertsSettingsScreen> createState() => _AlertsSettingsScreenState();
}

class _AlertsSettingsScreenState extends State<AlertsSettingsScreen> {
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;
  final _logger = Logger('AlertsSettingsScreen');
  bool _isLoading = true;
  bool _isSaving = false;
  late AlertSettingsIsar _alertSettings;

  // Define spacing constants locally
  static const double kSpacingSmall = 8.0;
  static const double kSpacingMedium = 16.0;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final settings = await _farmSetupHandler.getAlertSettings();
      _logger.info('Alert settings loaded successfully');
      setState(() {
        _alertSettings = settings;
        _isLoading = false;
      });
    } catch (e) {
      _logger.severe('Failed to load alert settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load settings: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
      setState(() {
        _alertSettings = AlertSettingsIsar.create();
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    if (_isSaving) return; // Prevent multiple save operations

    setState(() {
      _isSaving = true;
    });

    try {
      // Update farm ID if needed
      final farmId = await _farmSetupHandler.getSelectedFarmId();
      _alertSettings.farmBusinessId = farmId;

      await _farmSetupHandler.saveAlertSettings(_alertSettings);
      _logger.info('Alert settings saved successfully');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings saved successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      _logger.severe('Failed to save alert settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save settings: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  // Check if all individual alerts are enabled
  bool _areAllIndividualAlertsEnabled() {
    return _alertSettings.vaccinationAlerts &&
        _alertSettings.healthCheckAlerts &&
        _alertSettings.dewormingAlerts &&
        _alertSettings.pregnancyCheckAlerts &&
        _alertSettings.calvingAlerts &&
        _alertSettings.heatDetectionAlerts &&
        _alertSettings.milkProductionAlerts &&
        _alertSettings.weightChangeAlerts;
  }

  // Get icon for section header based on title

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Alert Settings'),
        actions: [
          _isSaving
              ? Container(
                  width: 48,
                  height: 48,
                  padding: const EdgeInsets.all(12),
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: theme.colorScheme.onPrimary,
                  ),
                )
              : IconButton(
                  icon: const Icon(Icons.save),
                  tooltip: 'Save Settings',
                  onPressed: _saveSettings,
                ),
        ],
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator(color: colorScheme.primary))
          : SingleChildScrollView(
              padding: const EdgeInsets.all(kSpacingMedium),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: kSpacingMedium),
                    child: Text(
                      'Configure when you want to receive alerts:',
                      style: theme.textTheme.titleMedium,
                    ),
                  ),
                  const SizedBox(height: kSpacingMedium),
                  _buildSettingSection(
                    title: 'Health Alerts',
                    icon: Icons.medical_services,
                    theme: theme,
                    colorScheme: colorScheme,
                    children: [
                      SwitchListTile(
                        title: Text('Vaccination Due',
                            style: theme.textTheme.bodyLarge),
                        subtitle: Text('Get notified when vaccinations are due',
                            style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurfaceVariant)),
                        value: _alertSettings.vaccinationAlerts,
                        activeColor: colorScheme.primary,
                        onChanged: (value) {
                          setState(() {
                            _alertSettings.vaccinationAlerts = value;
                            _alertSettings.allAlertsEnabled =
                                _areAllIndividualAlertsEnabled();
                          });
                        },
                      ),
                      SwitchListTile(
                        title: Text('Health Check Due',
                            style: theme.textTheme.bodyLarge),
                        subtitle: Text(
                            'Get notified when health checks are due',
                            style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurfaceVariant)),
                        value: _alertSettings.healthCheckAlerts,
                        activeColor: colorScheme.primary,
                        onChanged: (value) {
                          setState(() {
                            _alertSettings.healthCheckAlerts = value;
                            _alertSettings.allAlertsEnabled =
                                _areAllIndividualAlertsEnabled();
                          });
                        },
                      ),
                      SwitchListTile(
                        title: Text('Deworming Due',
                            style: theme.textTheme.bodyLarge),
                        subtitle: Text('Get notified when deworming is due',
                            style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurfaceVariant)),
                        value: _alertSettings.dewormingAlerts,
                        activeColor: colorScheme.primary,
                        onChanged: (value) {
                          setState(() {
                            _alertSettings.dewormingAlerts = value;
                            _alertSettings.allAlertsEnabled =
                                _areAllIndividualAlertsEnabled();
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: kSpacingMedium),
                  _buildSettingSection(
                    title: 'Reproduction Alerts',
                    icon: Icons.pregnant_woman,
                    theme: theme,
                    colorScheme: colorScheme,
                    children: [
                      SwitchListTile(
                        title: Text('Pregnancy Check Due',
                            style: theme.textTheme.bodyLarge),
                        subtitle: Text(
                            'Get notified when pregnancy checks are due',
                            style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurfaceVariant)),
                        value: _alertSettings.pregnancyCheckAlerts,
                        activeColor: colorScheme.primary,
                        onChanged: (value) {
                          setState(() {
                            _alertSettings.pregnancyCheckAlerts = value;
                            _alertSettings.allAlertsEnabled =
                                _areAllIndividualAlertsEnabled();
                          });
                        },
                      ),
                      SwitchListTile(
                        title: Text('Calving Due',
                            style: theme.textTheme.bodyLarge),
                        subtitle: Text('Get notified when calving is expected',
                            style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurfaceVariant)),
                        value: _alertSettings.calvingAlerts,
                        activeColor: colorScheme.primary,
                        onChanged: (value) {
                          setState(() {
                            _alertSettings.calvingAlerts = value;
                            _alertSettings.allAlertsEnabled =
                                _areAllIndividualAlertsEnabled();
                          });
                        },
                      ),
                      SwitchListTile(
                        title: Text('Heat Detection',
                            style: theme.textTheme.bodyLarge),
                        subtitle: Text(
                            'Get notified when cattle may be in heat',
                            style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurfaceVariant)),
                        value: _alertSettings.heatDetectionAlerts,
                        activeColor: colorScheme.primary,
                        onChanged: (value) {
                          setState(() {
                            _alertSettings.heatDetectionAlerts = value;
                            _alertSettings.allAlertsEnabled =
                                _areAllIndividualAlertsEnabled();
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: kSpacingMedium),
                  _buildSettingSection(
                    title: 'Production Alerts',
                    icon: Icons.bar_chart,
                    theme: theme,
                    colorScheme: colorScheme,
                    children: [
                      SwitchListTile(
                        title: Text('Milk Production',
                            style: theme.textTheme.bodyLarge),
                        subtitle: Text(
                            'Get notified of significant changes in milk production',
                            style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurfaceVariant)),
                        value: _alertSettings.milkProductionAlerts,
                        activeColor: colorScheme.primary,
                        onChanged: (value) {
                          setState(() {
                            _alertSettings.milkProductionAlerts = value;
                            _alertSettings.allAlertsEnabled =
                                _areAllIndividualAlertsEnabled();
                          });
                        },
                      ),
                      SwitchListTile(
                        title: Text('Weight Changes',
                            style: theme.textTheme.bodyLarge),
                        subtitle: Text(
                            'Get notified of significant weight changes',
                            style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurfaceVariant)),
                        value: _alertSettings.weightChangeAlerts,
                        activeColor: colorScheme.primary,
                        onChanged: (value) {
                          setState(() {
                            _alertSettings.weightChangeAlerts = value;
                            _alertSettings.allAlertsEnabled =
                                _areAllIndividualAlertsEnabled();
                          });
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: kSpacingMedium),
                  _buildSettingSection(
                    title: 'General Settings',
                    icon: Icons.settings,
                    theme: theme,
                    colorScheme: colorScheme,
                    children: [
                      SwitchListTile(
                        title: Text('Enable All Alerts',
                            style: theme.textTheme.bodyLarge),
                        subtitle: Text('Turn on or off all alerts at once',
                            style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurfaceVariant)),
                        value: _areAllIndividualAlertsEnabled(),
                        activeColor: colorScheme.primary,
                        onChanged: (value) {
                          setState(() {
                            _alertSettings.allAlertsEnabled = value;
                            _alertSettings.vaccinationAlerts = value;
                            _alertSettings.healthCheckAlerts = value;
                            _alertSettings.dewormingAlerts = value;
                            _alertSettings.pregnancyCheckAlerts = value;
                            _alertSettings.calvingAlerts = value;
                            _alertSettings.heatDetectionAlerts = value;
                            _alertSettings.milkProductionAlerts = value;
                            _alertSettings.weightChangeAlerts = value;
                          });
                        },
                      ),
                      SwitchListTile(
                        title: Text('Push Notifications',
                            style: theme.textTheme.bodyLarge),
                        subtitle: Text('Receive alerts as push notifications',
                            style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurfaceVariant)),
                        value: _alertSettings.pushNotifications,
                        activeColor: colorScheme.primary,
                        onChanged: (value) {
                          setState(() {
                            _alertSettings.pushNotifications = value;
                          });
                        },
                      ),
                      SwitchListTile(
                        title: Text('In-App Notifications',
                            style: theme.textTheme.bodyLarge),
                        subtitle: Text('Receive alerts within the app',
                            style: theme.textTheme.bodyMedium?.copyWith(
                                color: colorScheme.onSurfaceVariant)),
                        value: _alertSettings.inAppNotifications,
                        activeColor: colorScheme.primary,
                        onChanged: (value) {
                          setState(() {
                            _alertSettings.inAppNotifications = value;
                          });
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildSettingSection({
    required String title,
    required IconData icon,
    required ThemeData theme,
    required ColorScheme colorScheme,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(kSpacingMedium),
            child: Row(
              children: [
                Icon(icon, color: colorScheme.primary),
                const SizedBox(width: kSpacingSmall),
                Text(
                  title,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
          ),
          const Divider(),
          ...children,
        ],
      ),
    );
  }
}
