import 'package:flutter/material.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../services/farm_setup_handler.dart';
import '../models/animal_type_isar.dart';
import '../../../widgets/icon_picker.dart';
import '../../../constants/app_icons.dart';
import '../../../utils/message_utils.dart';
import 'package:flutter/services.dart';

// Define primary color constant
const Color kPrimaryAppColor = Color(0xFF2E7D32);
// Define common spacing constants
const double kSpacing = 8.0;
const double kSpacingMedium = 16.0;
const double kSpacingLarge = 24.0;

class AnimalTypesScreen extends StatefulWidget {
  const AnimalTypesScreen({Key? key}) : super(key: key);

  @override
  State<AnimalTypesScreen> createState() => _AnimalTypesScreenState();
}

// Reusable form for adding and editing animal types
class _AnimalTypeForm extends StatefulWidget {
  final AnimalTypeIsar? initialData; // Null for Add mode
  final Function(AnimalTypeIsar) onSave;
  final String actionButtonText;

  const _AnimalTypeForm({
    Key? key,
    this.initialData,
    required this.onSave,
    required this.actionButtonText,
  }) : super(key: key);

  @override
  _AnimalTypeFormState createState() => _AnimalTypeFormState();
}

class _AnimalTypeFormState extends State<_AnimalTypeForm> {
  final formKey = GlobalKey<FormState>();
  late TextEditingController nameController;
  late TextEditingController gestationController;
  late TextEditingController heatCycleController;
  late TextEditingController emptyPeriodController;
  late TextEditingController breedingAgeController;
  late IconData selectedIcon;
  late Color selectedColor;

  @override
  void initState() {
    super.initState();

    // Initialize controllers with initial data if available
    final initialData = widget.initialData;
    nameController = TextEditingController(text: initialData?.name ?? '');
    gestationController = TextEditingController(
        text: initialData?.defaultGestationDays?.toString() ?? '283');
    heatCycleController = TextEditingController(
        text: initialData?.defaultHeatCycleDays?.toString() ?? '21');
    emptyPeriodController = TextEditingController(
        text: initialData?.defaultEmptyPeriodDays?.toString() ?? '');
    breedingAgeController = TextEditingController(
        text: initialData?.defaultBreedingAge?.toString() ?? '');

    // Initialize icon and color
    selectedIcon = initialData?.icon ?? AppIcons.cow;
    selectedColor = initialData?.color ?? AppIcons.getAnimalColor('Cow');
  }

  @override
  void dispose() {
    nameController.dispose();
    gestationController.dispose();
    heatCycleController.dispose();
    emptyPeriodController.dispose();
    breedingAgeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Form(
      key: formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                widget.initialData == null ? Icons.add_circle : Icons.edit_note,
                color: theme.colorScheme.primary,
                size: 28,
              ),
              const SizedBox(width: kSpacingMedium - 4),
              Text(
                widget.initialData == null
                    ? 'Add Animal Type'
                    : 'Edit Animal Type',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: kSpacingLarge),

          // Icon & Color Selection Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Icon preview
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(kSpacingMedium),
                    decoration: BoxDecoration(
                      color: selectedColor.withAlpha(25),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      selectedIcon,
                      color: selectedColor,
                      size: 40,
                    ),
                  ),
                  const SizedBox(height: kSpacing),
                  Text(
                    'Preview',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.black87, // Use solid readable color
                    ),
                  ),
                ],
              ),

              // Buttons column
              Column(
                children: [
                  // Icon picker button
                  OutlinedButton.icon(
                    onPressed: () async {
                      final pickedIcon = await showDialog<IconData>(
                        context: context,
                        builder: (context) => IconPicker(
                          selectedIcon: selectedIcon,
                          onIconSelected: (_) {}, // No debug printing
                        ),
                      );

                      if (pickedIcon != null) {
                        setState(() {
                          selectedIcon = pickedIcon;
                        });
                      }
                    },
                    icon: const Icon(Icons.image),
                    label: const Text('Select Icon'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: kSpacingMedium, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),

                  const SizedBox(height: kSpacing),

                  // Color picker button
                  OutlinedButton.icon(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: const Text('Pick a color'),
                            content: SingleChildScrollView(
                              child: BlockPicker(
                                pickerColor: selectedColor,
                                onColorChanged: (Color color) {
                                  setState(() {
                                    selectedColor = color;
                                  });
                                  Navigator.of(context).pop();
                                },
                              ),
                            ),
                          );
                        },
                      );
                    },
                    icon: Icon(Icons.color_lens, color: selectedColor),
                    label: const Text('Select Color'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: kSpacingMedium, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: kSpacingLarge),
          const Divider(),
          const SizedBox(height: kSpacingMedium),

          // Name field
          TextFormField(
            controller: nameController,
            decoration: InputDecoration(
              labelText: 'Animal Type Name',
              hintText: 'Enter animal type name',
              prefixIcon: Icon(Icons.pets, color: theme.colorScheme.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(
                  horizontal: kSpacingMedium, vertical: kSpacingMedium),
            ),
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter an animal type name';
              }
              return null;
            },
            onChanged: (value) {
              if (widget.initialData == null && value.isNotEmpty) {
                final newColor = AppIcons.getAnimalColor(value);
                if (newColor != selectedColor) {
                  setState(() {
                    selectedColor = newColor;
                  });
                }
              }
            },
          ),
          const SizedBox(height: kSpacingMedium),

          // Gestation days field
          TextFormField(
            controller: gestationController,
            decoration: InputDecoration(
              labelText: 'Default Gestation Days',
              hintText: 'Enter default gestation period',
              prefixIcon:
                  Icon(Icons.calendar_today, color: theme.colorScheme.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(
                  horizontal: kSpacingMedium, vertical: kSpacingMedium),
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter gestation days';
              }
              if (int.tryParse(value) == null) {
                return 'Please enter a valid number';
              }
              return null;
            },
          ),
          const SizedBox(height: kSpacingMedium),

          // Heat cycle field
          TextFormField(
            controller: heatCycleController,
            decoration: InputDecoration(
              labelText: 'Default Heat Cycle Days',
              hintText: 'Enter default heat cycle period',
              prefixIcon: Icon(Icons.replay, color: theme.colorScheme.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(
                  horizontal: kSpacingMedium, vertical: kSpacingMedium),
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter heat cycle days';
              }
              if (int.tryParse(value) == null) {
                return 'Please enter a valid number';
              }
              return null;
            },
          ),
          const SizedBox(height: kSpacingMedium),

          // Empty period field
          TextFormField(
            controller: emptyPeriodController,
            decoration: InputDecoration(
              labelText: 'Empty Period Days (Optional)',
              hintText: 'Days between calving and breeding',
              prefixIcon:
                  Icon(Icons.hourglass_empty, color: theme.colorScheme.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(
                  horizontal: kSpacingMedium, vertical: kSpacingMedium),
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            validator: (value) {
              if (value != null &&
                  value.isNotEmpty &&
                  int.tryParse(value) == null) {
                return 'Please enter a valid number';
              }
              return null;
            },
          ),
          const SizedBox(height: kSpacingMedium),

          // Breeding age field
          TextFormField(
            controller: breedingAgeController,
            decoration: InputDecoration(
              labelText: 'Breeding Age (Months, Optional)',
              hintText: 'Age when animal is mature for breeding',
              prefixIcon: Icon(Icons.cake, color: theme.colorScheme.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding: const EdgeInsets.symmetric(
                  horizontal: kSpacingMedium, vertical: kSpacingMedium),
            ),
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            validator: (value) {
              if (value != null &&
                  value.isNotEmpty &&
                  int.tryParse(value) == null) {
                return 'Please enter a valid number';
              }
              return null;
            },
          ),
          const SizedBox(height: kSpacingLarge),

          // Action buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                style: TextButton.styleFrom(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'Cancel',
                  style: TextStyle(color: theme.colorScheme.error),
                ),
              ),
              const SizedBox(width: kSpacing),
              Flexible(
                child: ElevatedButton(
                  onPressed: () {
                    if (formKey.currentState!.validate()) {
                      final name = nameController.text.trim();
                      final gestationDays = int.parse(gestationController.text);
                      final heatCycleDays = int.parse(heatCycleController.text);

                      final emptyPeriodDays =
                          emptyPeriodController.text.isNotEmpty
                              ? int.tryParse(emptyPeriodController.text)
                              : null;
                      final breedingAge = breedingAgeController.text.isNotEmpty
                          ? int.tryParse(breedingAgeController.text)
                          : null;

                      // Create or update the animal type
                      AnimalTypeIsar resultType;
                      if (widget.initialData == null) {
                        // Create new animal type
                        resultType = AnimalTypeIsar.create(
                          name: name,
                          icon: selectedIcon,
                          color: selectedColor,
                          defaultGestationDays: gestationDays,
                          defaultHeatCycleDays: heatCycleDays,
                          defaultEmptyPeriodDays: emptyPeriodDays,
                          defaultBreedingAge: breedingAge,
                        );
                      } else {
                        // Update existing animal type
                        resultType = widget.initialData!.copyWith(
                          name: name,
                          icon: selectedIcon,
                          color: selectedColor,
                          defaultGestationDays: gestationDays,
                          defaultHeatCycleDays: heatCycleDays,
                          defaultEmptyPeriodDays: emptyPeriodDays,
                          defaultBreedingAge: breedingAge,
                        );
                      }

                      // Pass the result back
                      widget.onSave(resultType);
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: kPrimaryAppColor,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 12, vertical: 12),
                    child: Text(widget.actionButtonText),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _AnimalTypesScreenState extends State<AnimalTypesScreen>
    with SingleTickerProviderStateMixin {
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;
  List<AnimalTypeIsar> _animalTypes = [];
  bool _isLoading = true;
  late AnimationController _animationController;
  bool _isInitialLoad = true; // Track if this is the first load

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _loadAnimalTypes();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadAnimalTypes() async {
    try {
      final typesData = await _farmSetupHandler.getAllAnimalTypes();

      // Add mounted check before setState
      if (!mounted) return;

      setState(() {
        _animalTypes = typesData;
        _isLoading = false;
      });

      // Only play animation on initial load
      if (_isInitialLoad) {
        _animationController.forward();
        _isInitialLoad = false;
      }
    } catch (e) {
      // Keep error logging but remove debug prints
      debugPrint('Error loading animal types: $e'); // Use debugPrint instead of print
      if (mounted) {
        FarmSetupMessageUtils.showError(context, 'Failed to load animal types: $e');
        setState(() {
          _animalTypes = [];
          _isLoading = false;
        });
      }
    }
  }

  // Refactor to use the new form widget
  void _showAnimalTypeDialog([AnimalTypeIsar? animalType]) {
    final bool isEditing = animalType != null;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          elevation: 5,
          backgroundColor: Colors.white,
          child: Container(
            padding: const EdgeInsets.all(kSpacingLarge),
            child: SingleChildScrollView(
              child: _AnimalTypeForm(
                initialData: animalType,
                actionButtonText: isEditing ? 'Update' : 'Add Animal Type',
                onSave: (AnimalTypeIsar resultType) async {
                  // Store context-dependent objects before async gap
                  final navigator = Navigator.of(dialogContext);
                  final messenger = ScaffoldMessenger.of(dialogContext);

                  try {
                    await _farmSetupHandler.addOrUpdateAnimalType(resultType);

                    // Update local state directly
                    if (mounted) {
                      setState(() {
                        if (isEditing) {
                          final index = _animalTypes.indexWhere(
                              (at) => at.businessId == resultType.businessId);
                          if (index != -1) {
                            _animalTypes[index] = resultType;
                          }
                        } else {
                          _animalTypes.add(resultType);
                        }
                        // Keep list sorted
                        _animalTypes.sort(
                            (a, b) => (a.name ?? '').compareTo(b.name ?? ''));
                      });
                    }

                    // Close dialog and show success message
                    if (navigator.mounted) {
                      navigator.pop();
                    }

                    if (messenger.mounted) {
                      FarmSetupMessageUtils.showSuccess(context,
                          FarmSetupMessageUtils.animalTypeCreated());
                    }
                  } catch (e) {
                    if (messenger.mounted) {
                      FarmSetupMessageUtils.showError(context,
                          'Failed to ${isEditing ? 'update' : 'add'} animal type: $e');
                    }
                  }
                },
              ),
            ),
          ),
        );
      },
    );
  }

  // Replace the old methods with the new shared dialog
  void _showAddAnimalTypeDialog() => _showAnimalTypeDialog();
  void _showEditAnimalTypeDialog(AnimalTypeIsar animalType) =>
      _showAnimalTypeDialog(animalType);

  Future<void> _deleteAnimalType(AnimalTypeIsar animalType) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Animal Type'),
        content: Text('Are you sure you want to delete "${animalType.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      await _farmSetupHandler.deleteAnimalType(animalType.businessId ?? '');

      // Update local state directly instead of reloading
      if (mounted) {
        setState(() {
          _animalTypes
              .removeWhere((at) => at.businessId == animalType.businessId);
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Animal type deleted successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to delete animal type: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Animal Types',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: kPrimaryAppColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : _animalTypes.isEmpty
              ? _buildEmptyState()
              : _buildAnimalTypesList(),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddAnimalTypeDialog,
        backgroundColor: kPrimaryAppColor,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            AppIcons.cow,
            size: 80,
            color: kPrimaryAppColor,
          ),
          const SizedBox(height: kSpacingLarge),
          const Text(
            'No Animal Types Found',
            style: TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: kSpacingMedium),
          const Padding(
            padding: EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              'Add your first animal type to start tracking and managing your livestock',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 16,
                color: Colors.black87,
                height: 1.4,
              ),
            ),
          ),
          const SizedBox(height: kSpacingLarge + kSpacing),
          ElevatedButton.icon(
            onPressed: _showAddAnimalTypeDialog,
            icon: const Icon(Icons.add, size: 24),
            label: const Text(
              'Add Animal Type',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: kPrimaryAppColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(
                  horizontal: kSpacingLarge, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimalTypesList() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(
          kSpacingMedium, kSpacing, kSpacingMedium, 80),
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return ListView.builder(
            itemCount: _animalTypes.length,
            itemBuilder: (context, index) {
              final animalType = _animalTypes[index];

              // Calculate staggered animation delay
              final Animation<double> animation = CurvedAnimation(
                parent: _animationController,
                curve: Interval(
                  index / _animalTypes.length * 0.5,
                  (index + 1) / _animalTypes.length * 0.5 + 0.5,
                  curve: Curves.easeOutBack,
                ),
              );

              return Padding(
                padding: const EdgeInsets.only(bottom: kSpacingMedium),
                child: FadeTransition(
                  opacity: animation,
                  child: SlideTransition(
                    position: Tween<Offset>(
                      begin: const Offset(0, 0.2),
                      end: Offset.zero,
                    ).animate(animation),
                    child: _buildAnimalTypeCard(animalType),
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildAnimalTypeCard(AnimalTypeIsar animalType) {
    final theme = Theme.of(context);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: () => _showEditAnimalTypeDialog(animalType),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(kSpacingMedium),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Icon in a circle with the animal's color
                  Container(
                    padding: const EdgeInsets.all(10),
                    decoration: BoxDecoration(
                      color: animalType.color.withAlpha(51),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      animalType.icon,
                      color: animalType.color,
                      size: 32,
                    ),
                  ),
                  const SizedBox(width: kSpacingMedium),
                  // Animal type name
                  Expanded(
                    child: Text(
                      animalType.name ?? '',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                        fontSize: 20,
                      ),
                      softWrap: true,
                    ),
                  ),
                  // More menu
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert, size: 24),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.edit,
                                size: 20, color: theme.colorScheme.primary),
                            const SizedBox(width: kSpacing),
                            const Text('Edit'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.delete, size: 20, color: Colors.red),
                            SizedBox(width: kSpacing),
                            Text(
                              'Delete',
                              style: TextStyle(color: Colors.red),
                            ),
                          ],
                        ),
                      ),
                    ],
                    onSelected: (value) {
                      if (value == 'edit') {
                        _showEditAnimalTypeDialog(animalType);
                      } else if (value == 'delete') {
                        _deleteAnimalType(animalType);
                      }
                    },
                  ),
                ],
              ),
              const SizedBox(height: kSpacingMedium),
              const Divider(),
              const SizedBox(height: kSpacingMedium),
              // Info rows in a grid
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                mainAxisSpacing: kSpacingMedium,
                crossAxisSpacing: kSpacingLarge,
                childAspectRatio: 2.5,
                children: [
                  _buildInfoRow(
                    Icons.calendar_today,
                    'Gestation',
                    '${animalType.defaultGestationDays} days',
                    animalType.color,
                  ),
                  _buildInfoRow(
                    Icons.replay,
                    'Heat Cycle',
                    '${animalType.defaultHeatCycleDays} days',
                    animalType.color,
                  ),
                  if (animalType.defaultEmptyPeriodDays != null)
                    _buildInfoRow(
                      Icons.hourglass_empty,
                      'Empty Period',
                      '${animalType.defaultEmptyPeriodDays} days',
                      animalType.color,
                    ),
                  if (animalType.defaultBreedingAge != null)
                    _buildInfoRow(
                      Icons.cake,
                      'Breeding Age',
                      '${animalType.defaultBreedingAge} months',
                      animalType.color,
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: kSpacing, vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: color),
          const SizedBox(width: kSpacing),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$label:',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: kPrimaryAppColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
