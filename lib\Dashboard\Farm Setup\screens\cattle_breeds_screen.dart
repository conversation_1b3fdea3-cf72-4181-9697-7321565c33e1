// import 'package:flutter/material.dart';
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../services/farm_setup_handler.dart';
import '../models/breed_category_isar.dart';
import '../models/animal_type_isar.dart';
import '../../../widgets/icon_picker.dart';
import 'package:flutter_colorpicker/flutter_colorpicker.dart';
import '../../../services/database/breed_initializer.dart';
import '../../../utils/message_utils.dart';

// Define primary color as a constant for easier maintenance
const Color kPrimaryAppColor = Color(0xFF2E7D32);

class CattleBreedsScreen extends StatefulWidget {
  const CattleBreedsScreen({Key? key}) : super(key: key);

  @override
  State<CattleBreedsScreen> createState() => _CattleBreedsScreenState();
}

/// Reusable form for adding and editing breed entries
class _BreedForm extends StatefulWidget {
  final List<AnimalTypeIsar> animalTypes;
  final BreedCategoryIsar? initialData; // Null for add mode
  final Function(BreedCategoryIsar) onSave;
  final String actionButtonText;
  final List<BreedCategoryIsar> existingBreedsForValidation;

  const _BreedForm({
    Key? key,
    required this.animalTypes,
    this.initialData,
    required this.onSave,
    required this.actionButtonText,
    required this.existingBreedsForValidation,
  }) : super(key: key);

  @override
  _BreedFormState createState() => _BreedFormState();
}

class _BreedFormState extends State<_BreedForm> {
  final formKey = GlobalKey<FormState>();
  late TextEditingController nameController;
  late TextEditingController descriptionController;
  String? selectedAnimalTypeId;
  late IconData selectedIcon;
  late Color selectedColor;

  @override
  void initState() {
    super.initState();

    // Initialize controllers with initial data if available
    final initialData = widget.initialData;
    nameController = TextEditingController(text: initialData?.name ?? '');
    descriptionController =
        TextEditingController(text: initialData?.description ?? '');

    // Set initial icon and color
    selectedIcon = initialData?.icon ?? Icons.pets;
    selectedColor = initialData?.color ?? kPrimaryAppColor;

    // Validate and set selected animal type ID
    selectedAnimalTypeId = initialData?.animalTypeId;
    if (selectedAnimalTypeId != null) {
      // Check if the animal type exists in our current list
      final animalTypeExists = widget.animalTypes
          .any((type) => type.businessId == selectedAnimalTypeId);
      if (!animalTypeExists) {
        // Either set to null or to the first available animal type
        selectedAnimalTypeId = widget.animalTypes.isNotEmpty
            ? widget.animalTypes.first.businessId
            : null;
      }
    }
  }

  @override
  void dispose() {
    nameController.dispose();
    descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Form(
      key: formKey,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                widget.initialData == null ? Icons.add_circle : Icons.edit,
                color: theme.colorScheme.primary,
                size: 28,
              ),
              const SizedBox(width: 12),
              Text(
                widget.initialData == null ? 'Add New Breed' : 'Edit Breed',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),

          // Icon & Color Selection Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Icon preview
              Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: selectedColor.withAlpha(25),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      selectedIcon,
                      color: selectedColor,
                      size: 40,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Preview',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.black87, // Use solid readable color
                    ),
                  ),
                ],
              ),

              // Buttons column
              Column(
                children: [
                  // Icon picker button
                  OutlinedButton.icon(
                    onPressed: () async {
                      final pickedIcon = await showDialog<IconData>(
                        context: context,
                        builder: (context) => IconPicker(
                          selectedIcon: selectedIcon,
                          onIconSelected: (_) {}, // No debug prints
                        ),
                      );

                      if (pickedIcon != null) {
                        setState(() {
                          selectedIcon = pickedIcon;
                        });
                      }
                    },
                    icon: const Icon(Icons.image),
                    label: const Text('Select Icon'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Color picker button
                  OutlinedButton.icon(
                    onPressed: () {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return AlertDialog(
                            title: const Text('Pick a color'),
                            content: SingleChildScrollView(
                              child: BlockPicker(
                                pickerColor: selectedColor,
                                onColorChanged: (Color color) {
                                  setState(() {
                                    selectedColor = color;
                                  });
                                  Navigator.of(context).pop();
                                },
                              ),
                            ),
                          );
                        },
                      );
                    },
                    icon: Icon(Icons.color_lens, color: selectedColor),
                    label: const Text('Select Color'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: 24),
          const Divider(),
          const SizedBox(height: 16),

          // Animal Type dropdown
          DropdownButtonFormField<String>(
            value: selectedAnimalTypeId,
            decoration: InputDecoration(
              labelText: 'Animal Type',
              hintText: 'Select animal type',
              prefixIcon:
                  Icon(Icons.category, color: theme.colorScheme.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            items: widget.animalTypes.map((type) {
              return DropdownMenuItem(
                value: type.businessId,
                child: Text(type.name ?? ''),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                selectedAnimalTypeId = value;
              });
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please select an animal type';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Name field
          TextFormField(
            controller: nameController,
            decoration: InputDecoration(
              labelText: 'Breed Name',
              hintText: 'Enter breed name',
              prefixIcon: Icon(Icons.pets, color: theme.colorScheme.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter a breed name';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),

          // Description field
          TextFormField(
            controller: descriptionController,
            decoration: InputDecoration(
              labelText: 'Description (Optional)',
              hintText: 'Enter breed description',
              prefixIcon:
                  Icon(Icons.description, color: theme.colorScheme.primary),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
            ),
            maxLines: 3,
          ),
          const SizedBox(height: 24),

          // Buttons row
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'Cancel',
                  style: TextStyle(
                    color: theme.colorScheme.primary.withAlpha(179),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              ElevatedButton(
                onPressed: () {
                  if (formKey.currentState!.validate()) {
                    final name = nameController.text.trim();
                    final description = descriptionController.text.trim();

                    // Check for duplicate names within the same animal type
                    final isEditing = widget.initialData != null;
                    final existingBreeds = widget.existingBreedsForValidation;
                    final hasDuplicate = existingBreeds.any((b) =>
                        b.animalTypeId == selectedAnimalTypeId &&
                        b.name?.toLowerCase() == name.toLowerCase() &&
                        (!isEditing ||
                            b.businessId != widget.initialData?.businessId));

                    if (hasDuplicate) {
                      FarmSetupMessageUtils.showError(context,
                          'A breed with this name already exists for this animal type');
                      return;
                    }

                    // Create breed object
                    BreedCategoryIsar resultBreed;
                    if (isEditing) {
                      // Update existing breed
                      resultBreed = widget.initialData!.copyWith(
                        name: name,
                        animalTypeId: selectedAnimalTypeId!,
                        description: description,
                        icon: selectedIcon,
                        color: selectedColor,
                      );
                    } else {
                      // Create new breed
                      resultBreed = BreedCategoryIsar()
                        ..businessId = const Uuid().v4()
                        ..name = name
                        ..animalTypeId = selectedAnimalTypeId!
                        ..description = description
                        ..icon = selectedIcon
                        ..color = selectedColor
                        ..createdAt = DateTime.now()
                        ..updatedAt = DateTime.now();
                    }

                    // Pass the result back
                    widget.onSave(resultBreed);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: kPrimaryAppColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(widget.actionButtonText),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class _CattleBreedsScreenState extends State<CattleBreedsScreen> {
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;
  List<BreedCategoryIsar> _breeds = [];
  bool _isLoading = true;
  List<AnimalTypeIsar> _animalTypes = [];
  Map<String?, AnimalTypeIsar> _animalTypesMap = {};

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    try {
      // Use Future.wait to fetch animal types and breeds in parallel
      final results = await Future.wait([
        _farmSetupHandler.getAllAnimalTypes(),
        _farmSetupHandler.getAllBreedCategories(),
      ]);

      // Extract results
      final animalTypes = results[0] as List<AnimalTypeIsar>;
      final breeds = results[1] as List<BreedCategoryIsar>;

      // Create a map for more efficient lookups
      final animalTypesMap = <String?, AnimalTypeIsar>{};
      for (var type in animalTypes) {
        animalTypesMap[type.businessId] = type;
      }

      // Check for and fix breeds with invalid animal type IDs
      final fixedBreeds = await _validateAndFixBreeds(breeds, animalTypes);

      // Update all state in a single setState call
      if (mounted) {
        setState(() {
          _animalTypes = animalTypes;
          _animalTypesMap = animalTypesMap;
          _breeds = fixedBreeds;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        String errorMessage = 'Failed to load data';

        if (e.toString().contains('database')) {
          errorMessage = 'Database error: Could not load breeds or animal types';
        } else if (e.toString().contains('connection')) {
          errorMessage = 'Connection error: Please check your network';
        }

        FarmSetupMessageUtils.showError(context, errorMessage);
      }
    }
  }

  /// Validates breeds and flags any with invalid animal type IDs
  Future<List<BreedCategoryIsar>> _validateAndFixBreeds(
      List<BreedCategoryIsar> breeds, List<AnimalTypeIsar> animalTypes) async {
    // Check if any breeds have invalid animal type IDs
    final invalidBreeds = <BreedCategoryIsar>[];
    final defaultAnimalTypeId =
        animalTypes.isNotEmpty ? animalTypes.first.businessId : null;

    if (animalTypes.isEmpty) {
      // Can't validate without any animal types
      return breeds;
    }

    // First, identify all invalid breeds
    for (final breed in breeds) {
      final animalTypeValid =
          animalTypes.any((type) => type.businessId == breed.animalTypeId);

      if (!animalTypeValid) {
        invalidBreeds.add(breed);
      }
    }

    // If we found invalid breeds, notify the user
    if (invalidBreeds.isNotEmpty && mounted) {
      FarmSetupMessageUtils.showWarning(context,
          '${invalidBreeds.length} breeds have invalid animal types');

      // Fix all invalid breeds by assigning the default animal type
      if (defaultAnimalTypeId != null) {
        for (final breed in invalidBreeds) {
          final fixedBreed = breed.copyWith(
            animalTypeId: defaultAnimalTypeId,
          );
          await _farmSetupHandler.addOrUpdateBreedCategory(fixedBreed);

          // Update the breed in our local list
          final index = _breeds.indexWhere((b) => b.businessId == breed.businessId);
          if (index != -1 && mounted) {
            setState(() {
              _breeds[index] = fixedBreed;
            });
          }
        }

        // Show success message
        if (mounted) {
          FarmSetupMessageUtils.showSuccess(context,
              'All invalid breeds have been fixed');
        }
      }
    }

    return breeds;
  }

  /// Helper method to sort breeds by animal type name, then by breed name
  void _sortBreedsByAnimalType(List<BreedCategoryIsar> breeds) {
    breeds.sort((a, b) {
      // Get animal type names
      final animalTypeA = _animalTypesMap[a.animalTypeId]?.name ?? '';
      final animalTypeB = _animalTypesMap[b.animalTypeId]?.name ?? '';

      // First sort by animal type name
      final typeComparison = animalTypeA.compareTo(animalTypeB);
      if (typeComparison != 0) {
        return typeComparison;
      }

      // Then sort by breed name
      return (a.name ?? '').compareTo(b.name ?? '');
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Cattle Breeds',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: kPrimaryAppColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _breeds.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.pets_outlined,
                        size: 64,
                        color: Theme.of(context)
                            .colorScheme
                            .primary
                            .withAlpha(128),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'No Breeds Added Yet',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'Add your first breed to get started',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .primary
                                  .withAlpha(179),
                            ),
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          ElevatedButton.icon(
                            onPressed: () => _showBreedDialog(),
                            icon: const Icon(Icons.add),
                            label: const Text('Add Breed'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: kPrimaryAppColor,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          ElevatedButton.icon(
                            onPressed: _initializeDefaultBreeds,
                            icon: const Icon(Icons.auto_fix_high),
                            label: const Text('Initialize Default Breeds'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.orange,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 12,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              : _buildAnimalTypesList(),
      floatingActionButton: UniversalFAB.add(
        onPressed: () => _showBreedDialog(),
        tooltip: 'Add Breed',
      ),
    );
  }

  // New unified method for showing the breed dialog
  void _showBreedDialog([BreedCategoryIsar? breed]) {
    final bool isEditing = breed != null;

    // Filter relevant breeds for validation
    // When editing, we only need to check breeds of the same animal type
    final relevantBreeds = isEditing
        ? _breeds.where((b) => b.animalTypeId == breed.animalTypeId && b.businessId != breed.businessId).toList()
        : _breeds;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(24),
          ),
          elevation: 5,
          backgroundColor: Colors.white,
          child: Container(
            padding: const EdgeInsets.all(24),
            child: SingleChildScrollView(
              child: _BreedForm(
                animalTypes: _animalTypes,
                initialData: breed,
                actionButtonText: isEditing ? 'Update Breed' : 'Add Breed',
                existingBreedsForValidation: relevantBreeds,
                onSave: (BreedCategoryIsar resultBreed) async {
                  // Store context-dependent objects before async gap
                  final navigator = Navigator.of(dialogContext);
                  final messenger = ScaffoldMessenger.of(dialogContext);

                  try {
                    if (isEditing) {
                      await _updateBreed(resultBreed);
                    } else {
                      await _addBreed(resultBreed);
                    }

                    // Close dialog and show success message
                    if (navigator.mounted) {
                      navigator.pop();
                    }

                    if (messenger.mounted) {
                      FarmSetupMessageUtils.showSuccess(context,
                          'Breed ${isEditing ? 'updated' : 'added'} successfully');
                    }
                  } catch (e) {
                    if (messenger.mounted) {
                      String errorMessage = 'Failed to ${isEditing ? 'update' : 'add'} breed';

                      if (e.toString().contains('duplicate')) {
                        errorMessage = 'A breed with this name already exists for this animal type';
                      } else if (e.toString().contains('validation')) {
                        errorMessage = 'Please check all fields and try again';
                      } else if (e.toString().contains('not found') && isEditing) {
                        errorMessage = 'Breed not found. It may have been deleted';
                      }

                      FarmSetupMessageUtils.showError(context, errorMessage);
                    }
                  }
                },
              ),
            ),
          ),
        );
      },
    );
  }

  // Replace old method with new approach

  Future<void> _addBreed(BreedCategoryIsar breed) async {
    try {
      await _farmSetupHandler.addOrUpdateBreedCategory(breed);
      if (mounted) {
        setState(() {
          _breeds.add(breed);
          // Sort by animal type name, then by breed name
          _sortBreedsByAnimalType(_breeds);
        });
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Failed to add breed';

        if (e.toString().contains('duplicate')) {
          errorMessage = 'A breed with this name already exists';
        } else if (e.toString().contains('validation')) {
          errorMessage = 'Invalid breed data';
        }

        FarmSetupMessageUtils.showError(context, errorMessage);
      }
    }
  }

  Future<void> _updateBreed(BreedCategoryIsar breed) async {
    try {
      await _farmSetupHandler.addOrUpdateBreedCategory(breed);
      if (mounted) {
        setState(() {
          final index =
              _breeds.indexWhere((b) => b.businessId == breed.businessId);
          if (index != -1) {
            _breeds[index] = breed;
          }
        });
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Failed to update breed';

        if (e.toString().contains('duplicate')) {
          errorMessage = 'A breed with this name already exists';
        } else if (e.toString().contains('validation')) {
          errorMessage = 'Invalid breed data';
        } else if (e.toString().contains('not found')) {
          errorMessage = 'Breed not found';
        }

        FarmSetupMessageUtils.showError(context, errorMessage);
      }
    }
  }

  /// Initialize default breeds
  Future<void> _initializeDefaultBreeds() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await BreedInitializer.initializeDefaultBreeds();

      if (mounted) {
        if (success) {
          FarmSetupMessageUtils.showSuccess(context,
              'Default breeds initialized successfully');
          // Reload breeds
          await _loadData();
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Failed to initialize default breeds'),
              backgroundColor: Colors.red,
            ),
          );
          setState(() {
            _isLoading = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        FarmSetupMessageUtils.showError(context, 'Error initializing breeds: $e');
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteBreed(BreedCategoryIsar breed) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Breed'),
        content: Text('Are you sure you want to delete "${breed.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    try {
      await _farmSetupHandler.deleteBreedCategory(breed.businessId ?? '');
      if (mounted) {
        setState(() {
          _breeds.removeWhere((b) => b.businessId == breed.businessId);
        });

        FarmSetupMessageUtils.showSuccess(context,
            'Breed "${breed.name}" deleted successfully');
      }
    } catch (e) {
      if (mounted) {
        String errorMessage = 'Failed to delete breed';

        if (e.toString().contains('in use')) {
          errorMessage = 'Cannot delete breed that is in use by cattle';
        } else if (e.toString().contains('not found')) {
          errorMessage = 'Breed not found';
        }

        FarmSetupMessageUtils.showError(context, errorMessage);
      }
    }
  }

  Widget _buildAnimalTypesList() {
    // First, group the breeds by animal type
    final Map<String?, List<BreedCategoryIsar>> breedsByAnimalType = {};

    // Group breeds by animal type
    for (final breed in _breeds) {
      final animalTypeId = breed.animalTypeId;
      if (!breedsByAnimalType.containsKey(animalTypeId)) {
        breedsByAnimalType[animalTypeId] = [];
      }
      breedsByAnimalType[animalTypeId]!.add(breed);
    }

    // Sort breeds within each animal type group
    for (final breeds in breedsByAnimalType.values) {
      breeds.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
    }

    // Get animal types that have breeds, sorted by name
    final sortedAnimalTypesWithBreeds = _animalTypes
        .where((type) => breedsByAnimalType.containsKey(type.businessId))
        .toList()
      ..sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));

    // Build sections for each animal type
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: sortedAnimalTypesWithBreeds.length,
      itemBuilder: (context, index) {
        // Get the animal type for this index
        final animalType = sortedAnimalTypesWithBreeds[index];
        final animalTypeId = animalType.businessId;

        final breedsForType = breedsByAnimalType[animalTypeId] ?? [];

        // Create a section for this animal type
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Animal Type Header
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Color(animalType.colorValue ?? 0xFF2E7D32)
                          .withAlpha(51),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      animalType.icon,
                      color: Color(animalType.colorValue ?? 0xFF2E7D32),
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    animalType.name ?? 'Unknown Type',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(animalType.colorValue ?? 0xFF2E7D32),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Color(animalType.colorValue ?? 0xFF2E7D32)
                          .withAlpha(25),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${breedsForType.length} ${breedsForType.length == 1 ? 'breed' : 'breeds'}',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(animalType.colorValue ?? 0xFF2E7D32),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Breeds for this animal type
            ...breedsForType
                .map((breed) => _buildBreedCard(breed, animalType))
                .toList(),

            // Add space between sections
            const SizedBox(height: 16),
          ],
        );
      },
    );
  }

  Widget _buildBreedCard(BreedCategoryIsar breed, AnimalTypeIsar animalType) {
    final animalTypeColor = Color(animalType.colorValue ?? 0xFF2E7D32);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                // Breed icon with breed color
                Container(
                  padding: const EdgeInsets.all(10),
                  decoration: BoxDecoration(
                    color: breed.color.withAlpha(51),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    breed.icon,
                    color: breed.color,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Row(
                    children: [
                      // Breed name with breed color
                      Expanded(
                        child: Text(
                          breed.name ?? '',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: breed.color,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Animal type badge with animal type color
                      Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: animalTypeColor.withAlpha(51),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: animalTypeColor.withAlpha(153),
                            width: 1.5,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              animalType.icon,
                              size: 16,
                              color: animalTypeColor.withAlpha(255),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              animalType.name ?? 'Unknown Type',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: animalTypeColor.withAlpha(255),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                // Menu with breed color
                PopupMenuButton<String>(
                  icon: Icon(Icons.more_vert, color: breed.color),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.edit, size: 20, color: breed.color),
                          const SizedBox(width: 8),
                          Text('Edit', style: TextStyle(color: breed.color)),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.delete, size: 20, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) {
                    if (value == 'edit') {
                      _showBreedDialog(breed);
                    } else if (value == 'delete') {
                      _deleteBreed(breed);
                    }
                  },
                ),
              ],
            ),
            if (breed.description?.isNotEmpty == true) ...[
              const SizedBox(height: 12),
              Text(
                breed.description!,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[700],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
