import 'package:flutter/material.dart';
import '../services/farm_setup_handler.dart';
import '../models/backup_settings_isar.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class DataBackupScreen extends StatefulWidget {
  const DataBackupScreen({Key? key}) : super(key: key);

  @override
  State<DataBackupScreen> createState() => _DataBackupScreenState();
}

class _DataBackupScreenState extends State<DataBackupScreen> {
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;
  bool _isLoading = true;
  String? _errorMessage;
  late BackupSettingsIsar _backupSettings;
  bool _backupInProgress = false;
  bool _restoreInProgress = false;
  String? _backupLocation;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      _backupSettings = await _farmSetupHandler.getBackupSettings();
      _backupLocation = _backupSettings.backupLocation;

      // If the backup location is 'local' or empty, update it to a real path
      if (_backupLocation == 'local' ||
          _backupLocation == null ||
          _backupLocation!.isEmpty) {
        _backupLocation = await _getDefaultBackupLocation();
        _backupSettings.backupLocation = _backupLocation!;
        await _saveSettings();
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load backup settings: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _saveSettings() async {
    try {
      await _farmSetupHandler.saveBackupSettings(_backupSettings);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Backup settings saved')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to save settings: $e')),
        );
      }
    }
  }

  Future<bool> _requestStoragePermission() async {
    try {
      // For Android 13+ (API level 33+)
      if (Platform.isAndroid) {
        // First request the basic storage permission
        var storageStatus = await Permission.storage.request();
        if (storageStatus.isGranted) {
          return true;
        }

        // For Android 13+, try media permissions instead
        final mediaStatus = await [
          Permission.photos,
          Permission.videos,
          Permission.audio,
        ].request();

        // Check if all media permissions are granted
        bool allMediaGranted =
            mediaStatus.values.every((status) => status.isGranted);
        if (allMediaGranted) {
          return true;
        }
      } else {
        // For iOS or other platforms
        if (await Permission.storage.request().isGranted) {
          return true;
        }
      }

      // If we get here, show a dialog explaining permissions
      if (mounted) {
        await showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Storage Permission Required'),
            content: const Text(
                'This app needs storage access to backup and restore your data. '
                'Please grant storage permission in your device settings.'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
              TextButton(
                onPressed: () => openAppSettings(),
                child: const Text('Open Settings'),
              ),
            ],
          ),
        );
      }

      return false;
    } catch (e) {
      // If we get a MissingPluginException, we need to restart the app
      if (e.toString().contains('MissingPluginException')) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Please restart the app to enable storage access'),
              duration: Duration(seconds: 5),
            ),
          );
        }
      }
      return false;
    }
  }

  Future<String> _getDefaultBackupLocation() async {
    try {
      // For most reliable access, prefer the app-specific directory
      final directory = await getApplicationDocumentsDirectory();
      final backupPath = '${directory.path}/CattleManager/Backups';

      // Create the directory if it doesn't exist
      try {
        final dir = Directory(backupPath);
        if (!await dir.exists()) {
          await dir.create(recursive: true);
        }

        // Test write access by creating a test file
        final testFile = File('$backupPath/test_access.tmp');
        await testFile.writeAsString('test');
        await testFile.delete(); // Clean up test file

        return backupPath;
      } catch (e) {
        // Fall back to Downloads directory which may be more accessible
        if (Platform.isAndroid) {
          return '/storage/emulated/0/Download/CattleManager/Backups';
        } else {
          rethrow; // Re-throw for other platforms
        }
      }
    } catch (e) {
      // Last resort fallback
      return '/storage/emulated/0/Download/CattleManager/Backups';
    }
  }

  Future<void> _createBackup() async {
    setState(() {
      _backupInProgress = true;
    });

    try {
      if (_backupLocation == null || _backupLocation!.isEmpty) {
        throw Exception('Please select a backup location first');
      }

      // Request storage permission
      final hasPermission = await _requestStoragePermission();
      if (!hasPermission) {
        throw Exception('Storage permission denied');
      }

      // Get the directory for backups
      final backupDir = Directory(_backupLocation!);
      if (!await backupDir.exists()) {
        try {
          await backupDir.create(recursive: true);
        } catch (e) {
          // Try a different location if creation fails
          _backupLocation = await _getDefaultBackupLocation();
          _backupSettings.backupLocation = _backupLocation!;
          await _saveSettings();

          // Try again with the new location
          final newBackupDir = Directory(_backupLocation!);
          if (!await newBackupDir.exists()) {
            await newBackupDir.create(recursive: true);
          }
        }
      }

      // Create a timestamp-based filename for the backup
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final backupFilePath =
          '${_backupLocation!}/cattle_manager_$timestamp.isar';

      // Perform the actual backup
      final success = await _farmSetupHandler.backupDatabase(backupFilePath);

      if (!success) {
        throw Exception('Backup operation failed');
      }

      // Update the last backup date
      _backupSettings.lastBackupDate = DateTime.now();
      await _farmSetupHandler.saveBackupSettings(_backupSettings);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Backup created: $backupFilePath')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to create backup: $e'),
            duration: const Duration(seconds: 5),
          ),
        );
      }
    } finally {
      setState(() {
        _backupInProgress = false;
      });
    }
  }

  Future<void> _selectBackupLocation() async {
    try {
      // Request storage permission
      final hasPermission = await _requestStoragePermission();
      if (!hasPermission) {
        throw Exception('Storage permission denied');
      }

      String? selectedDirectory = await FilePicker.platform.getDirectoryPath();

      if (selectedDirectory != null) {
        setState(() {
          _backupSettings.backupLocation = selectedDirectory;
          _backupLocation = selectedDirectory;
        });
        await _saveSettings();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to select directory: $e')),
        );
      }
    }
  }

  Future<void> _restoreBackup() async {
    setState(() {
      _restoreInProgress = true;
    });

    // We'll collect results here instead of showing snackbars directly
    String? resultMessage;
    bool isError = false;
    
    try {
      // Request storage permission
      final hasPermission = await _requestStoragePermission();
      if (!mounted) return;
      
      if (!hasPermission) {
        throw Exception('Storage permission denied');
      }

      // Show warning dialog
      final confirm = await showDialog<bool>(
        context: context,
        builder: (dialogContext) => AlertDialog(
          title: const Text('Warning'),
          content: const Text(
              'Restoring from backup will overwrite your current data. '
              'This action cannot be undone. Are you sure you want to proceed?'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: const Text('Cancel'),
            ),
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              style: TextButton.styleFrom(
                foregroundColor: Colors.red,
              ),
              child: const Text('Restore'),
            ),
          ],
        ),
      );

      if (!mounted) return;
      
      if (confirm != true) {
        return;
      }

      // Pick the backup file
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['isar'],
        dialogTitle: 'Select a backup file',
      );

      if (!mounted) return;
      
      if (result == null || result.files.single.path == null) {
        throw Exception('No backup file selected');
      }

      final filePath = result.files.single.path!;

      // Perform the actual restore
      final success = await _farmSetupHandler.restoreDatabase(filePath);

      if (!mounted) return;
      
      if (!success) {
        throw Exception('Restore operation failed');
      }

      resultMessage = 'Backup restored successfully. Restart the app to see changes.';
    } catch (e) {
      resultMessage = 'Failed to restore backup: $e';
      isError = true;
    } finally {
      // Only setState and show result if we're still mounted
      if (mounted) {
        setState(() {
          _restoreInProgress = false;
        });
        
        // Show result message if we have one
        if (resultMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(resultMessage),
              duration: Duration(seconds: isError ? 3 : 5),
            ),
          );
        }
      }
    }
  }

  String formatLastBackupDate(DateTime? date) {
    if (date == null) return 'No backup created yet';
    // Create formatter instance here
    final DateFormat formatter = DateFormat("MMM d, yyyy 'at' h:mm a");
    try {
      // Use the formatter instance
      return formatter.format(date);
    } catch (e) {
      // Consider logging the error e
      return 'Invalid date';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        appBar: AppBar(title: const Text('Data Backup')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(_errorMessage!, style: const TextStyle(color: Colors.red)),
              ElevatedButton(
                onPressed: _loadSettings,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Backup & Restore'),
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
            tooltip: 'Save Settings',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.settings_backup_restore),
                        SizedBox(width: 8),
                        Text(
                          'Backup Settings',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    SwitchListTile(
                      title: const Text('Auto Backup'),
                      subtitle: const Text(
                          'Automatically backup your data regularly'),
                      value: _backupSettings.autoBackupEnabled,
                      onChanged: (value) {
                        setState(() {
                          _backupSettings.autoBackupEnabled = value;
                        });
                      },
                    ),
                    if (_backupSettings.autoBackupEnabled)
                      ListTile(
                        title: const Text('Backup Frequency'),
                        subtitle: const Text('How often to create backups'),
                        trailing: DropdownButton<int>(
                          value: _backupSettings.autoBackupFrequency,
                          items: [1, 3, 7, 14, 30].map((days) {
                            return DropdownMenuItem(
                              value: days,
                              child:
                                  Text('$days ${days == 1 ? 'day' : 'days'}'),
                            );
                          }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                _backupSettings.autoBackupFrequency = value;
                              });
                            }
                          },
                        ),
                      ),
                    ListTile(
                      title: const Text('Backup Location'),
                      subtitle: Text(_backupLocation ?? 'Not set'),
                      trailing: IconButton(
                        icon: const Icon(Icons.folder_open),
                        onPressed: _selectBackupLocation,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.backup),
                        SizedBox(width: 8),
                        Text(
                          'Manual Backup',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    ListTile(
                      title: const Text('Last Backup'),
                      subtitle: Text(
                          formatLastBackupDate(_backupSettings.lastBackupDate)),
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        icon: _backupInProgress
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : const Icon(Icons.backup),
                        label: Text(_backupInProgress
                            ? 'Creating Backup...'
                            : 'Create Backup Now'),
                        onPressed: _backupInProgress ? null : _createBackup,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Row(
                      children: [
                        Icon(Icons.restore),
                        SizedBox(width: 8),
                        Text(
                          'Restore from Backup',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    const Text(
                        'Restore your farm data from a previously created backup file.'),
                    const SizedBox(height: 16),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        icon: _restoreInProgress
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  color: Colors.white,
                                ),
                              )
                            : const Icon(Icons.restore),
                        label: Text(_restoreInProgress
                            ? 'Restoring...'
                            : 'Restore from Backup'),
                        onPressed: _restoreInProgress ? null : _restoreBackup,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.orange,
                        ),
                      ),
                    ),
                    if (!_restoreInProgress)
                      const Padding(
                        padding: EdgeInsets.only(top: 8.0),
                        child: Text(
                          'Warning: This will overwrite your current data!',
                          style: TextStyle(
                            color: Colors.red,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
