import 'package:flutter/material.dart';
import 'package:logging/logging.dart';
import '../models/farm_isar.dart';
import '../services/farm_setup_handler.dart';
import '../dialogs/farm_form_dialog.dart';
import '../../../utils/message_utils.dart';

class FarmInfoScreen extends StatefulWidget {
  const FarmInfoScreen({Key? key}) : super(key: key);

  @override
  State<FarmInfoScreen> createState() => _FarmInfoScreenState();
}

class _FarmInfoScreenState extends State<FarmInfoScreen> {
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;
  final _logger = Logger('FarmInfoScreen');
  List<FarmIsar> _farms = [];
  String? _selectedFarmId;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadFarms();
  }

  Future<void> _loadFarms() async {
    try {
      final farmsData = await _farmSetupHandler.getAllFarms();
      final selectedFarmId = await _farmSetupHandler.getSelectedFarmId();
      setState(() {
        _farms = farmsData;
        _selectedFarmId = selectedFarmId;
        _isLoading = false;
      });
    } catch (e) {
      _logger.severe('Error loading farms: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        _showErrorSnackBar(context, 'Error loading farms', e);
      }
    }
  }

  Future<void> _addFarm(BuildContext context) async {
    final result = await showDialog<FarmIsar>(
      context: context, 
      builder: (dialogContext) => const FarmFormDialog(),
    );

    if (!mounted || result == null) return;

    try {
      await _farmSetupHandler.addFarm(result);

      if (!mounted) return;

      setState(() {
        _farms.add(result);
        _farms.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
      });
    } catch (e) {
      _logger.severe('Error adding farm: $e');
      if (mounted) {
        // ignore: use_build_context_synchronously
        _showErrorSnackBar(context, 'Error adding farm', e);
      }
    }
  }

  Future<void> _editFarm(BuildContext context, FarmIsar farm) async {
    final result = await showDialog<FarmIsar>(
      context: context,
      builder: (dialogContext) => FarmFormDialog(farm: farm),
    );

    if (!mounted || result == null) return;

    try {
      await _farmSetupHandler.updateFarm(result);

      if (!mounted) return;

      setState(() {
        final index = _farms.indexWhere((f) => f.farmBusinessId == result.farmBusinessId);
        if (index != -1) {
          _farms[index] = result;
        }

        if (farm.farmBusinessId == _selectedFarmId &&
            farm.farmBusinessId != result.farmBusinessId) {
          _selectedFarmId = result.farmBusinessId;
        }

        _farms.sort((a, b) => (a.name ?? '').compareTo(b.name ?? ''));
      });
    } catch (e) {
      _logger.severe('Error updating farm: $e');
      if (mounted) {
        // ignore: use_build_context_synchronously
        _showErrorSnackBar(context, 'Error updating farm', e);
      }
    }
  }

  Future<void> _deleteFarm(BuildContext context, FarmIsar farm) async {
    final confirm = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Delete Farm'),
        content: Text('Are you sure you want to delete ${farm.name}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(dialogContext, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(dialogContext, true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(dialogContext).colorScheme.error,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (!mounted || confirm != true) return;

    try {
      await _farmSetupHandler.deleteFarm(farm.farmBusinessId ?? '');

      if (!mounted) return;

      setState(() {
        _farms.removeWhere((f) => f.farmBusinessId == farm.farmBusinessId);
      });
    } catch (e) {
      _logger.severe('Error deleting farm: $e');
      if (mounted) {
        // ignore: use_build_context_synchronously
        _showErrorSnackBar(context, 'Error deleting farm', e);
      }
    }
  }

  Future<void> _selectFarm(BuildContext context, FarmIsar farm) async {
    try {
      await _farmSetupHandler.setActiveFarm(farm.farmBusinessId ?? '');
      
      if (!mounted) return;
      
      setState(() {
        _selectedFarmId = farm.farmBusinessId;
      });
      
      // ignore: use_build_context_synchronously
      Navigator.pop(context); 
    } catch (e) {
      _logger.severe('Error selecting farm: $e');
      if (mounted) {
        // ignore: use_build_context_synchronously
        _showErrorSnackBar(context, 'Error selecting farm', e);
      }
    }
  }

  // Helper to show snackbar, ensuring context is used safely
  void _showErrorSnackBar(BuildContext context, String message, Object error) {
    if (!mounted) return; // Already checked, but double-check
    FarmSetupMessageUtils.showError(context, '$message: $error');
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(child: CircularProgressIndicator()),
      );
    }

    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Farm Information'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _addFarm(context),
            tooltip: 'Add New Farm',
          ),
        ],
      ),
      body: _farms.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'No farms added yet',
                    style: textTheme.titleLarge,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton.icon(
                    onPressed: () => _addFarm(context),
                    icon: const Icon(Icons.add),
                    label: const Text('Add Farm'),
                  ),
                ],
              ),
            )
          : ListView.builder(
              itemCount: _farms.length,
              itemBuilder: (context, index) {
                final farm = _farms[index];
                final isSelected = farm.farmBusinessId == _selectedFarmId;

                return Card(
                  margin:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: ListTile(
                    leading: Icon(
                      _getFarmTypeIcon(farm.farmType),
                      color: colorScheme.primary,
                    ),
                    title: Text(
                      farm.name ?? 'Unnamed Farm',
                      style: textTheme.titleMedium,
                    ),
                    subtitle: Text(
                      'Owner: ${farm.ownerName ?? 'Unknown'}',
                      style: textTheme.bodyMedium,
                    ),
                    trailing: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (isSelected)
                          Icon(Icons.check_circle, color: colorScheme.primary)
                        else
                          IconButton(
                            icon: const Icon(Icons.check_circle_outline),
                            onPressed: () => _selectFarm(context, farm),
                            tooltip: 'Select Farm',
                          ),
                        IconButton(
                          icon: const Icon(Icons.edit),
                          onPressed: () => _editFarm(context, farm),
                          tooltip: 'Edit Farm',
                        ),
                        IconButton(
                          icon: Icon(Icons.delete, color: colorScheme.error),
                          onPressed: () => _deleteFarm(context, farm),
                          tooltip: 'Delete Farm',
                        ),
                      ],
                    ),
                    onTap: () => _editFarm(context, farm),
                  ),
                );
              },
            ),
    );
  }

  IconData _getFarmTypeIcon(FarmType? type) {
    switch (type) {
      case FarmType.dairy:
        return Icons.local_drink;
      case FarmType.breeding:
        return Icons.pets;
      case FarmType.mixed:
        return Icons.all_inclusive;
      default:
        return Icons.agriculture;
    }
  }
}
