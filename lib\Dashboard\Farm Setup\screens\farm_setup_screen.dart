import 'package:flutter/material.dart';
import '../../../widgets/setup_menu_item.dart';
import '../screens/income_categories_screen.dart';
import '../screens/expense_categories_screen.dart';
import '../screens/cattle_breeds_screen.dart';
import '../screens/animal_types_screen.dart';
import '../screens/gestation_settings_screen.dart';
import '../screens/currency_setup_screen.dart';
import '../screens/farm_info_screen.dart';
import '../screens/milk_settings_screen.dart';
import '../screens/users_roles_screen.dart';
import '../screens/data_backup_screen.dart';
import '../screens/alerts_settings_screen.dart';
import '../screens/event_types_screen.dart';

class FarmSetupScreen extends StatelessWidget {
  const FarmSetupScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final availableHeight = screenSize.height -
        MediaQuery.of(context).padding.top -
        MediaQuery.of(context).padding.bottom -
        kToolbarHeight -
        32;

    int crossAxisCount = screenSize.width > 600 ? 3 : 2;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Farm Setup'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: LayoutBuilder(
          builder: (context, constraints) {
            const buttonHeight = 48.0;
            final buttonPadding = availableHeight * 0.02;
            final gridHeight =
                constraints.maxHeight - (buttonHeight + buttonPadding * 3);
            final itemWidth =
                (constraints.maxWidth - ((crossAxisCount + 1) * 12)) /
                    crossAxisCount;
            final itemHeight = (gridHeight - 24) / 4;

            return Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: GridView.count(
                      crossAxisCount: crossAxisCount,
                      mainAxisSpacing: 12.0,
                      crossAxisSpacing: 12.0,
                      childAspectRatio: itemWidth / itemHeight,
                      children: [
                        SetupMenuItem(
                          title: 'Income\nCategories',
                          icon: Icons.trending_up,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    const IncomeCategoriesScreen()),
                          ),
                        ),
                        SetupMenuItem(
                          title: 'Expense\nCategories',
                          icon: Icons.trending_down,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    const ExpenseCategoriesScreen()),
                          ),
                        ),
                        SetupMenuItem(
                          title: 'Cattle\nBreeds',
                          icon: Icons.pets,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    const CattleBreedsScreen()),
                          ),
                        ),
                        SetupMenuItem(
                          title: 'Animal\nTypes',
                          icon: Icons
                              .emoji_nature, // This shows a more animal-like icon
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    const AnimalTypesScreen()),
                          ),
                        ),
                        SetupMenuItem(
                          title: 'Gestation\nSettings',
                          icon: Icons.pregnant_woman,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    const GestationSettingsScreen()),
                          ),
                        ),
                        SetupMenuItem(
                          title: 'Currency\nSetup',
                          icon: Icons.attach_money,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    const CurrencySetupScreen()),
                          ),
                        ),
                        SetupMenuItem(
                          title: 'Farm\nInformation',
                          icon: Icons.business,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const FarmInfoScreen()),
                          ),
                        ),
                        SetupMenuItem(
                          title: 'Milk\nSettings',
                          icon: Icons.local_drink,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    const MilkSettingsScreen()),
                          ),
                        ),
                        SetupMenuItem(
                          title: 'Users &\nRoles',
                          icon: Icons.people,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const UsersRolesScreen()),
                          ),
                        ),
                        SetupMenuItem(
                          title: 'Data\nBackup',
                          icon: Icons.backup,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const DataBackupScreen()),
                          ),
                        ),
                        SetupMenuItem(
                          title: 'Alert\nSettings',
                          icon: Icons.notifications,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    const AlertsSettingsScreen()),
                          ),
                        ),
                        SetupMenuItem(
                          title: 'Event\nTypes',
                          icon: Icons.event_note,
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => const EventTypesScreen()),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
