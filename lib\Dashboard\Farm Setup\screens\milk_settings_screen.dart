import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/milk_settings_isar.dart';
import '../services/farm_setup_handler.dart';
import '../../../constants/app_colors.dart';

// Define spacing constants for consistency
const double kSpacing = 8.0;
const double kSpacingMedium = 16.0;
const double kSpacingLarge = 24.0;

class MilkSettingsScreen extends StatefulWidget {
  const MilkSettingsScreen({Key? key}) : super(key: key);

  @override
  State<MilkSettingsScreen> createState() => _MilkSettingsScreenState();
}

class _MilkSettingsScreenState extends State<MilkSettingsScreen> {
  final _formKey = GlobalKey<FormState>();
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;

  // Settings state
  late MilkSettingsIsar _settings;
  String _currencySymbol =
      '\$'; // Default, will be updated from currency settings
  bool _isLoading = true;

  // Controllers for text fields
  late TextEditingController _regularRateController;
  late TextEditingController _premiumRateController;
  late TextEditingController _bulkRateController;

  @override
  void initState() {
    super.initState();
    // Initialize controllers
    _regularRateController = TextEditingController();
    _premiumRateController = TextEditingController();
    _bulkRateController = TextEditingController();
    _loadSettings();
  }

  @override
  void dispose() {
    // Dispose controllers
    _regularRateController.dispose();
    _premiumRateController.dispose();
    _bulkRateController.dispose();
    super.dispose();
  }

  Future<void> _loadSettings() async {
    setState(() => _isLoading = true);
    try {
      // Load settings - getCurrencySettings now handles farm ID internally
      final milkSettings = await _farmSetupHandler.getMilkSettings();
      final currencySettings = await _farmSetupHandler.getCurrencySettings();

      // Log for debugging
      debugPrint(
          'Loaded currency: ${currencySettings.currencyCode}, symbol: ${currencySettings.currencySymbol}, farmId: ${currencySettings.farmId}');

      // Update controllers with loaded values
      _regularRateController.text = milkSettings.regularRate.toString();
      _premiumRateController.text = milkSettings.premiumRate.toString();
      _bulkRateController.text = milkSettings.bulkRate.toString();

      if (mounted) {
        setState(() {
          _settings = milkSettings;
          _currencySymbol = currencySettings.currencySymbol;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading settings: $e');
      // Create default settings
      _settings = MilkSettingsIsar.create(
        farmBusinessId: '0',
        unit: 'liters',
        regularRate: 1.0,
        premiumRate: 1.5,
        bulkRate: 0.8,
      );

      // Update controllers with default values
      _regularRateController.text = _settings.regularRate.toString();
      _premiumRateController.text = _settings.premiumRate.toString();
      _bulkRateController.text = _settings.bulkRate.toString();

      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _saveSettings() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      // Update _settings with values from controllers
      _settings = _settings.copyWith(
        regularRate: double.tryParse(_regularRateController.text) ?? 0.0,
        premiumRate: double.tryParse(_premiumRateController.text) ?? 0.0,
        bulkRate: double.tryParse(_bulkRateController.text) ?? 0.0,
      );

      final selectedFarmId = await _farmSetupHandler.getSelectedFarmId();
      _settings = _settings.copyWith(farmBusinessId: selectedFarmId);
      await _farmSetupHandler.saveMilkSettings(_settings);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Settings saved successfully'),
            backgroundColor: AppColors.success,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error saving milk settings: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving settings: $e'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text(
            'Milk Settings',
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: AppColors.primary,
        ),
        body: const Center(
          child: CircularProgressIndicator(color: AppColors.primary),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Milk Settings',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: AppColors.primary,
        actions: [
          IconButton(
            icon: const Icon(Icons.save, color: Colors.white),
            onPressed: _saveSettings,
            tooltip: 'Save Settings',
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        autovalidateMode: AutovalidateMode.onUserInteraction,
        child: ListView(
          padding: const EdgeInsets.all(kSpacingMedium),
          children: [
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(kSpacingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.water_drop,
                          color: AppColors.primary,
                          size: 20,
                        ),
                        const SizedBox(width: kSpacing),
                        Text(
                          'Measurement Unit',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: kSpacingMedium),
                    SegmentedButton<String>(
                      segments: const [
                        ButtonSegment<String>(
                          value: 'liters',
                          label: Text('Liters'),
                          icon: Icon(Icons.water),
                        ),
                        ButtonSegment<String>(
                          value: 'gallons',
                          label: Text('Gallons'),
                          icon: Icon(Icons.local_drink),
                        ),
                      ],
                      selected: {_settings.unit ?? 'liters'},
                      onSelectionChanged: (Set<String> newSelection) {
                        setState(() {
                          _settings = _settings.copyWith(
                            unit: newSelection.first,
                          );
                        });
                      },
                      style: ButtonStyle(
                        backgroundColor: WidgetStateProperty.resolveWith<Color>(
                          (states) {
                            if (states.contains(WidgetState.selected)) {
                              return AppColors.primary;
                            }
                            return Colors.transparent;
                          },
                        ),
                        foregroundColor: WidgetStateProperty.resolveWith<Color>(
                          (states) {
                            if (states.contains(WidgetState.selected)) {
                              return Colors.white;
                            }
                            return AppColors.primary;
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: kSpacingMedium),
            Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Padding(
                padding: const EdgeInsets.all(kSpacingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.attach_money,
                          color: AppColors.primary,
                          size: 20,
                        ),
                        const SizedBox(width: kSpacing),
                        Text(
                          'Milk Sale Rates',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: kSpacingMedium),
                    TextFormField(
                      controller: _regularRateController,
                      decoration: InputDecoration(
                        labelText: 'Regular Rate (per ${_settings.unit})',
                        prefixText: _currencySymbol,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: Icon(
                          Icons.price_change,
                          color: AppColors.primary.withAlpha(179),
                        ),
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d*\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a rate';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: kSpacingMedium),
                    TextFormField(
                      controller: _premiumRateController,
                      decoration: InputDecoration(
                        labelText: 'Premium Rate (per ${_settings.unit})',
                        prefixText: _currencySymbol,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: Icon(
                          Icons.star,
                          color: AppColors.primary.withAlpha(179),
                        ),
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d*\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a rate';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: kSpacingMedium),
                    TextFormField(
                      controller: _bulkRateController,
                      decoration: InputDecoration(
                        labelText: 'Bulk Rate (per ${_settings.unit})',
                        prefixText: _currencySymbol,
                        helperText: 'Special rate for bulk purchases',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        prefixIcon: Icon(
                          Icons.inventory,
                          color: AppColors.primary.withAlpha(179),
                        ),
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                            RegExp(r'^\d*\.?\d{0,2}')),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter a rate';
                        }
                        if (double.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: kSpacingLarge),
            ElevatedButton.icon(
              icon: const Icon(Icons.save_alt_outlined, color: Colors.white),
              label: const Text('Save Settings'),
              onPressed: _saveSettings,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: kSpacingMedium),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12)),
                minimumSize: const Size(double.infinity, 50),
                textStyle: theme.textTheme.labelLarge
                    ?.copyWith(fontWeight: FontWeight.bold),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
