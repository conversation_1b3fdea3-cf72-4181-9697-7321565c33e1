import 'package:flutter/material.dart';
import 'dart:convert';
import '../models/user_role_isar.dart';
import '../models/farm_user_isar.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/farm_setup_handler.dart';
import '../../../constants/app_colors.dart';
import '../../../constants/app_constants.dart';
import '../../../constants/app_tabs.dart';

class UsersRolesScreen extends StatefulWidget {
  const UsersRolesScreen({Key? key}) : super(key: key);

  @override
  State<UsersRolesScreen> createState() => _UsersRolesScreenState();
}

class _UsersRolesScreenState extends State<UsersRolesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;
  List<FarmUserIsar> _users = [];
  List<UserRoleIsar> _roles = [];
  bool _isLoading = true;
  String? _errorMessage;
  bool _isDataMigrated = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // First try to load from Isar
      final users = await _farmSetupHandler.getFarmUsers();
      final roles = await _farmSetupHandler.getAllUserRoles();

      // If no roles exist in Isar but we might have data in SharedPreferences
      if (roles.isEmpty && !_isDataMigrated) {
        await _checkAndMigrateFromSharedPreferences();
      } else {
        setState(() {
          _users = users;
          _roles = roles;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading data: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _checkAndMigrateFromSharedPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final activeFarm = await _farmSetupHandler.getActiveFarm();
      final farmBusinessId = activeFarm?.farmBusinessId ?? '';

      // Check if there's data in SharedPreferences
      final usersKey = '${farmBusinessId}_farm_users';
      final rolesKey = '${farmBusinessId}_user_roles';

      final usersJson = prefs.getStringList(usersKey) ?? [];
      final rolesJson = prefs.getStringList(rolesKey) ?? [];

      List<FarmUserIsar> spUsers = [];
      List<UserRoleIsar> spRoles = [];

      // If we have data in SharedPreferences, migrate it
      if (usersJson.isNotEmpty || rolesJson.isNotEmpty) {
        if (rolesJson.isNotEmpty) {
          spRoles = rolesJson
              .map((json) => UserRoleIsar.fromMap(jsonDecode(json)))
              .toList();
        } else {
          spRoles = UserRoleIsar.defaultRoles;
        }

        if (usersJson.isNotEmpty) {
          spUsers = usersJson
              .map((json) => FarmUserIsar.fromMap(jsonDecode(json)))
              .toList();
        }

        // Migrate data to Isar
        await _farmSetupHandler.migrateUsersFromSharedPreferences(
            spUsers, spRoles);

        // After migration, clear SharedPreferences data
        await prefs.remove(usersKey);
        await prefs.remove(rolesKey);

        // Set migration flag
        _isDataMigrated = true;

        // Load data from Isar after migration
        await _loadData();

        // Show migration success message
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
                content: Text('User data successfully migrated to database')),
          );
        }
      } else {
        // If no data in SharedPreferences, ensure default roles exist
        await _farmSetupHandler.ensureDefaultUserRoles();

        // Load data after ensuring defaults
        final users = await _farmSetupHandler.getFarmUsers();
        final roles = await _farmSetupHandler.getAllUserRoles();

        setState(() {
          _users = users;
          _roles = roles;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error migrating data: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _showAddUserDialog() async {
    final result = await showDialog<FarmUserIsar>(
      context: context,
      builder: (context) => _UserFormDialog(
        roles: _roles,
      ),
    );

    if (result != null && mounted) {
      // Capture messenger before async gap
      final messenger = ScaffoldMessenger.of(context);
      try {
        await _farmSetupHandler.saveUser(result);
        // Refresh the users list
        final users = await _farmSetupHandler.getFarmUsers();

        // Check mounted status again after await
        if (!mounted) return;

        setState(() {
          _users = users;
        });

        // Use captured messenger
        messenger.showSnackBar(
          SnackBar(
            content: Text('User "${result.name}" added successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        // Use captured messenger
        messenger.showSnackBar(
          SnackBar(
            content: Text('Error adding user: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _editUser(FarmUserIsar user) async {
    final result = await showDialog<FarmUserIsar>(
      context: context,
      builder: (context) => _UserFormDialog(
        roles: _roles,
        user: user,
      ),
    );

    if (result != null && mounted) {
      // Capture messenger before async gap
      final messenger = ScaffoldMessenger.of(context);
      try {
        await _farmSetupHandler.saveUser(result);
        // Refresh the users list
        final users = await _farmSetupHandler.getFarmUsers();

        // Check mounted status again after await
        if (!mounted) return;

        setState(() {
          _users = users;
        });

        // Use captured messenger
        messenger.showSnackBar(
          SnackBar(
            content: Text('User "${result.name}" updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        // Use captured messenger
        messenger.showSnackBar(
          SnackBar(
            content: Text('Error updating user: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteUser(FarmUserIsar user) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete User'),
        content: Text(
            'Are you sure you want to delete ${user.name ?? 'this user'}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      // Capture messenger before async gap
      final messenger = ScaffoldMessenger.of(context);
      try {
        await _farmSetupHandler.deleteUser(user.businessId!);
        // Refresh the users list
        final users = await _farmSetupHandler.getFarmUsers();

        // Check mounted status again after await
        if (!mounted) return;

        setState(() {
          _users = users;
        });

        // Use captured messenger
        messenger.showSnackBar(
          SnackBar(
            content: Text('User "${user.name}" deleted successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        // Use captured messenger
        messenger.showSnackBar(
          SnackBar(
            content: Text('Error deleting user: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _editRolePermissions(UserRoleIsar role) async {
    final result = await showDialog<UserRoleIsar>(
      context: context,
      builder: (context) => _RolePermissionsDialog(role: role),
    );

    if (result != null && mounted) {
      // Capture messenger before async gap
      final messenger = ScaffoldMessenger.of(context);
      try {
        await _farmSetupHandler.saveUserRole(result);
        // Refresh the roles list
        final roles = await _farmSetupHandler.getAllUserRoles();

        // Check mounted status again after await
        if (!mounted) return;

        setState(() {
          _roles = roles;
        });

        // Use captured messenger
        messenger.showSnackBar(
          SnackBar(
            content: Text('Role "${result.name}" updated successfully'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        // Use captured messenger
        messenger.showSnackBar(
          SnackBar(
            content: Text('Error updating role: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Users & Roles'),
          backgroundColor: AppColors.primaryColor,
        ),
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    if (_errorMessage != null) {
      return Scaffold(
        appBar: AppBar(
          title: const Text('Users & Roles'),
          backgroundColor: AppColors.primaryColor,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 48, color: Colors.red.shade700),
              const SizedBox(height: kSpacingMedium),
              Text(
                'Error Loading Data',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: Colors.red.shade700,
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: kSpacingSmall),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: kSpacingLarge),
                child: Text(
                  _errorMessage!,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
              const SizedBox(height: kSpacingLarge),
              ElevatedButton.icon(
                onPressed: _loadData,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: kSpacingLarge,
                    vertical: kSpacingMedium,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Users & Roles'),
        backgroundColor: AppColors.primaryColor,
        toolbarHeight: 32,
        elevation: 4,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorWeight: 3,
          tabs: const [
            Tab(text: 'Users'),
            Tab(text: 'Roles'),
          ],
        ),
      ),
      floatingActionButton: _tabController.index == 0
          ? UniversalFAB.add(
              onPressed: _showAddUserDialog,
              tooltip: 'Add User',
              backgroundColor: AppColors.primaryColor,
            )
          : null,
      body: TabBarView(
        controller: _tabController,
        children: [
          // Users Tab
          _buildUsersTab(),
          // Roles Tab
          _buildRolesTab(),
        ],
      ),
    );
  }

  Widget _buildUsersTab() {
    if (_users.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: kSpacingMedium),
            Text(
              'No Users Found',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: kSpacingSmall),
            const Text(
              'Add users to manage access to your farm',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: kSpacingLarge),
            ElevatedButton.icon(
              onPressed: _showAddUserDialog,
              icon: const Icon(Icons.add),
              label: const Text('Add User'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.accentColor,
                padding: const EdgeInsets.symmetric(
                  horizontal: kSpacingLarge,
                  vertical: kSpacingMedium,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: _users.length,
      padding: const EdgeInsets.all(kSpacingMedium),
      itemBuilder: (context, index) {
        final user = _users[index];

        // Find the role for this user
        String roleName = 'Unknown Role';
        if (user.role.value != null) {
          roleName = user.role.value!.name ?? 'Unknown Role';
        } else if (user.roleBusinessId != null) {
          final role = _roles.firstWhere(
            (role) => role.businessId == user.roleBusinessId,
            orElse: () => UserRoleIsar()..name = 'Unknown Role',
          );
          roleName = role.name ?? 'Unknown Role';
        }

        return Card(
          elevation: 2,
          margin: const EdgeInsets.only(bottom: kSpacingMedium),
          child: ListTile(
            contentPadding: const EdgeInsets.all(kSpacingMedium),
            leading: CircleAvatar(
              backgroundColor: AppColors.accentColor,
              child: Text(
                (user.name?.isNotEmpty ?? false)
                    ? user.name![0].toUpperCase()
                    : '?',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              user.name ?? 'Unnamed User',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Row(
                  children: [
                    const Icon(Icons.email, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        user.email ?? 'No email',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    const Icon(Icons.phone, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      user.phoneNumber ?? 'No phone',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
                const SizedBox(height: 2),
                Row(
                  children: [
                    const Icon(Icons.badge, size: 16, color: Colors.grey),
                    const SizedBox(width: 4),
                    Text(
                      'Role: $roleName',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ],
                ),
              ],
            ),
            isThreeLine: true,
            trailing: PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              onSelected: (value) {
                if (value == 'edit') {
                  _editUser(user);
                } else if (value == 'delete') {
                  _deleteUser(user);
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, color: Colors.white),
                      SizedBox(width: kSpacingSmall),
                      Text('Edit', style: TextStyle(color: Colors.white)),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, color: Colors.white),
                      SizedBox(width: kSpacingSmall),
                      Text('Delete', style: TextStyle(color: Colors.white)),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildRolesTab() {
    return ListView.builder(
      itemCount: _roles.length,
      padding: const EdgeInsets.all(kSpacingMedium),
      itemBuilder: (context, index) {
        final role = _roles[index];
        return Card(
          elevation: 2,
          margin: const EdgeInsets.only(bottom: kSpacingMedium),
          child: ExpansionTile(
            title: Text(
              role.name ?? 'Unnamed Role',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            leading: const CircleAvatar(
              backgroundColor: AppColors.primaryColor,
              child: Icon(Icons.shield, color: Colors.white),
            ),
            children: [
              Padding(
                padding: const EdgeInsets.all(kSpacingMedium),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Permissions:',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    const SizedBox(height: kSpacingSmall),
                    Wrap(
                      spacing: 8,
                      runSpacing: 4,
                      children: role.permissions.map((permission) {
                        return Chip(
                          label: Text(
                            permission.replaceAll('_', ' ').toUpperCase(),
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          backgroundColor: AppColors.accentColor,
                          padding: const EdgeInsets.all(4),
                        );
                      }).toList(),
                    ),
                    const SizedBox(height: kSpacingMedium),
                    Center(
                      child: ElevatedButton.icon(
                        onPressed: () => _editRolePermissions(role),
                        icon: const Icon(Icons.edit, color: Colors.white),
                        label: const Text('Edit Permissions',
                            style: TextStyle(color: Colors.white)),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primaryColor,
                          padding: const EdgeInsets.symmetric(
                            horizontal: kSpacingLarge,
                            vertical: kSpacingSmall,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// User Form Dialog as a separate widget
class _UserFormDialog extends StatefulWidget {
  final FarmUserIsar? user;
  final List<UserRoleIsar> roles;

  const _UserFormDialog({
    Key? key,
    this.user,
    required this.roles,
  }) : super(key: key);

  @override
  _UserFormDialogState createState() => _UserFormDialogState();
}

class _UserFormDialogState extends State<_UserFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  String? _selectedRoleId;
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    // Initialize with existing values if editing
    if (widget.user != null) {
      _nameController.text = widget.user!.name ?? '';
      _emailController.text = widget.user!.email ?? '';
      _phoneController.text = widget.user!.phoneNumber ?? '';
      _selectedRoleId = widget.user!.roleBusinessId ??
          (widget.user!.role.value?.businessId ??
              (widget.roles.isNotEmpty ? widget.roles.first.businessId : null));
    } else {
      _selectedRoleId =
          widget.roles.isNotEmpty ? widget.roles.first.businessId : null;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate() || _selectedRoleId == null) {
      return;
    }

    // Capture context-dependent objects before async gap
    final navigator = Navigator.of(context);
    final messenger = ScaffoldMessenger.of(context);

    setState(() {
      _isProcessing = true;
    });

    try {
      // Create a new user or modify existing one
      final user = widget.user != null
          ? widget.user!.copyWith(
              name: _nameController.text,
              email: _emailController.text,
              phoneNumber: _phoneController.text,
              roleBusinessId: _selectedRoleId,
            )
          : FarmUserIsar.create(
              name: _nameController.text,
              email: _emailController.text,
              phoneNumber: _phoneController.text,
              roleBusinessId: _selectedRoleId!,
              farmBusinessId:
                  (await _farmSetupHandler.getActiveFarm())?.farmBusinessId ??
                      '',
            );

      // Check mounted status after await
      if (!mounted) return;

      // Find the role and set it
      if (_selectedRoleId != null) {
        final role = widget.roles.firstWhere(
          (role) => role.businessId == _selectedRoleId,
          orElse: () => UserRoleIsar.defaultRoles.first,
        );
        user.role.value = role;
      }

      // Use captured navigator
      navigator.pop(user);

    } catch (e) {
      // Use captured messenger
      messenger.showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.user == null ? 'Add New User' : 'Edit User'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'Name',
                  hintText: 'Enter user name',
                  prefixIcon: Icon(Icons.person),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: kSpacingMedium),
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(
                  labelText: 'Email',
                  hintText: 'Enter email address',
                  prefixIcon: Icon(Icons.email),
                ),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter an email';
                  }
                  // Simple email validation
                  if (!value.contains('@') || !value.contains('.')) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              const SizedBox(height: kSpacingMedium),
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(
                  labelText: 'Phone Number',
                  hintText: 'Enter phone number',
                  prefixIcon: Icon(Icons.phone),
                ),
                keyboardType: TextInputType.phone,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter a phone number';
                  }
                  return null;
                },
              ),
              const SizedBox(height: kSpacingMedium),
              DropdownButtonFormField<String>(
                value: _selectedRoleId,
                decoration: const InputDecoration(
                  labelText: 'Role',
                  prefixIcon: Icon(Icons.shield),
                ),
                items: widget.roles.map((role) {
                  return DropdownMenuItem(
                    value: role.businessId,
                    child: Text(role.name ?? 'Unknown Role'),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedRoleId = value;
                  });
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please select a role';
                  }
                  return null;
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isProcessing ? null : () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isProcessing ? null : _handleSubmit,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.accentColor,
          ),
          child: _isProcessing
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text(widget.user == null ? 'Add' : 'Save'),
        ),
      ],
    );
  }
}

// Role Permissions Dialog as a separate widget
class _RolePermissionsDialog extends StatefulWidget {
  final UserRoleIsar role;

  const _RolePermissionsDialog({
    Key? key,
    required this.role,
  }) : super(key: key);

  @override
  _RolePermissionsDialogState createState() => _RolePermissionsDialogState();
}

class _RolePermissionsDialogState extends State<_RolePermissionsDialog> {
  late List<String> _selectedPermissions;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _selectedPermissions = List<String>.from(widget.role.permissions);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Edit ${widget.role.name ?? 'Role'} Permissions'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Text(
              'Select Permissions:',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppColors.primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: kSpacingSmall),
            ...UserRoleIsar.allPermissions.map((permission) {
              final displayName = permission.replaceAll('_', ' ').toUpperCase();

              return CheckboxListTile(
                title: Text(displayName),
                subtitle: Text(_getPermissionDescription(permission)),
                value: _selectedPermissions.contains(permission),
                activeColor: AppColors.accentColor,
                checkColor: Colors.white,
                onChanged: (bool? value) {
                  setState(() {
                    if (value == true) {
                      _selectedPermissions.add(permission);
                    } else {
                      _selectedPermissions.remove(permission);
                    }
                  });
                },
                dense: true,
              );
            }).toList(),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isProcessing ? null : () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isProcessing
              ? null
              : () {
                  setState(() {
                    _isProcessing = true;
                  });

                  final updatedRole = widget.role.copyWith(
                    permissions: _selectedPermissions,
                  );

                  Navigator.of(context).pop(updatedRole);
                },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.accentColor,
          ),
          child: _isProcessing
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Save'),
        ),
      ],
    );
  }

  String _getPermissionDescription(String permission) {
    switch (permission) {
      case 'manage_cattle':
        return 'Add, edit, and delete cattle records';
      case 'view_cattle':
        return 'View cattle records';
      case 'manage_milk_records':
        return 'Add, edit, and delete milk records';
      case 'view_milk_records':
        return 'View milk production records';
      case 'manage_health_records':
        return 'Add, edit, and delete health records';
      case 'view_health_records':
        return 'View health records';
      case 'manage_breeding_records':
        return 'Add, edit, and delete breeding records';
      case 'view_breeding_records':
        return 'View breeding records';
      case 'manage_transactions':
        return 'Add, edit, and delete financial transactions';
      case 'view_transactions':
        return 'View financial transactions';
      case 'manage_users':
        return 'Add, edit, and delete users and roles';
      case 'view_reports':
        return 'View farm reports and analytics';
      case 'manage_farm_settings':
        return 'Modify farm settings and configurations';
      default:
        return 'Allows access to $permission functionality';
    }
  }
}
