import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import '../models/health_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/health_handler.dart';
import '../../Cattle/services/cattle_handler.dart';


/// Controller state enum
enum ControllerState { initial, loading, loaded, error, empty }

class HealthController extends ChangeNotifier {
  final HealthHandler _healthHandler = GetIt.instance<HealthHandler>();
  final CattleHandler _cattleHandler = GetIt.instance<CattleHandler>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;
  
  // Data
  List<HealthRecordIsar> _healthRecords = [];
  List<CattleIsar> _cattle = [];
  
  // Business ID - Generate unique ID for this controller instance
  final String businessId = const Uuid().v4();

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  List<HealthRecordIsar> get healthRecords => List.unmodifiable(_healthRecords);
  List<CattleIsar> get cattle => List.unmodifiable(_cattle);
  
  // Analytics data
  int get totalHealthRecords => _healthRecords.length;
  int get activeRecords => _healthRecords.where((r) => r.status?.toLowerCase() == 'active').length;
  int get completedRecords => _healthRecords.where((r) => r.status?.toLowerCase() == 'completed').length;
  int get treatmentRecords => _healthRecords.where((r) => r.recordType?.toLowerCase() == 'treatment').length;
  int get vaccinationRecords => _healthRecords.where((r) => r.recordType?.toLowerCase() == 'vaccination').length;
  
  Map<String, int> get recordsByType {
    final Map<String, int> typeCount = {};
    for (final record in _healthRecords) {
      final type = record.recordType ?? 'Unknown';
      typeCount[type] = (typeCount[type] ?? 0) + 1;
    }
    return typeCount;
  }

  Map<String, int> get recordsByStatus {
    final Map<String, int> statusCount = {};
    for (final record in _healthRecords) {
      final status = record.status ?? 'Unknown';
      statusCount[status] = (statusCount[status] ?? 0) + 1;
    }
    return statusCount;
  }

  // Initialize and load data
  Future<void> loadData() async {
    try {
      _setState(ControllerState.loading);

      await Future.wait([
        _loadHealthRecords(),
        _loadCattle(),
      ]);

      _setState(ControllerState.loaded);
    } catch (e, stackTrace) {
      debugPrint('Error loading health data: $e\n$stackTrace');
      _setError('Failed to load health data: ${e.toString()}');
    }
  }

  Future<void> refresh() async {
    await loadData();
  }

  Future<void> _loadHealthRecords() async {
    try {
      final healthData = await _healthHandler.getAllHealthRecords();
      _healthRecords = healthData;
    } catch (e) {
      debugPrint('Error loading health records: $e');
      throw Exception('Failed to load health records');
    }
  }

  Future<void> _loadCattle() async {
    try {
      final cattleData = await _cattleHandler.getAllCattle();
      _cattle = cattleData;
    } catch (e) {
      debugPrint('Error loading cattle: $e');
      throw Exception('Failed to load cattle');
    }
  }

  // Add new health record
  Future<void> addHealthRecord(HealthRecordIsar record) async {
    try {
      await _healthHandler.addHealthRecord(record);
      _healthRecords.add(record);
      _healthRecords.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding health record: $e');
      throw Exception('Failed to add health record');
    }
  }

  // Update health record
  void updateHealthRecord(HealthRecordIsar updatedRecord) {
    final index = _healthRecords.indexWhere((r) => r.businessId == updatedRecord.businessId);
    if (index >= 0) {
      _healthRecords[index] = updatedRecord;
      _healthRecords.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
      notifyListeners();
    }
  }

  // Helper methods
  CattleIsar? getCattle(String? cattleBusinessId) {
    if (cattleBusinessId == null) return null;
    try {
      return _cattle.firstWhere(
        (cattle) => cattle.businessId == cattleBusinessId,
      );
    } catch (e) {
      return null;
    }
  }

  String getCattleName(String? cattleBusinessId) {
    final cattle = getCattle(cattleBusinessId);
    return cattle?.name ?? 'Unknown Cattle';
  }

  // Filter methods
  List<HealthRecordIsar> getRecordsByType(String type) {
    return _healthRecords.where((r) => r.recordType?.toLowerCase() == type.toLowerCase()).toList();
  }

  List<HealthRecordIsar> getRecordsByStatus(String status) {
    return _healthRecords.where((r) => r.status?.toLowerCase() == status.toLowerCase()).toList();
  }

  List<HealthRecordIsar> getRecordsByCattle(String cattleBusinessId) {
    return _healthRecords.where((r) => r.cattleBusinessId == cattleBusinessId).toList();
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }
}
