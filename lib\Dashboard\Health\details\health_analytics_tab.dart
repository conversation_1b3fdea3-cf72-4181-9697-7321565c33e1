import 'package:flutter/material.dart';
import '../models/health_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../widgets/index.dart';
import '../../../constants/app_tabs.dart';
import 'package:fl_chart/fl_chart.dart';


class HealthAnalyticsTab extends StatefulWidget {
  final HealthRecordIsar healthRecord;
  final CattleIsar? cattle;
  final List<HealthRecordIsar> relatedRecords;

  const HealthAnalyticsTab({
    Key? key,
    required this.healthRecord,
    this.cattle,
    required this.relatedRecords,
  }) : super(key: key);

  @override
  State<HealthAnalyticsTab> createState() => _HealthAnalyticsTabState();
}

class _HealthAnalyticsTabState extends State<HealthAnalyticsTab>
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    if (widget.relatedRecords.isEmpty) {
      return UniversalEmptyState.health(
        title: 'No Analytics Data',
        message: 'Add more health records for ${widget.cattle?.name ?? 'this cattle'} to see detailed analytics.',
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Health Analytics for ${widget.cattle?.name ?? 'Cattle'}',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),

          // Summary Cards
          _buildSummaryCards(),
          const SizedBox(height: 24),

          // Record Type Distribution
          _buildRecordTypeDistribution(),
          const SizedBox(height: 24),

          // Status Distribution
          _buildStatusDistribution(),
          const SizedBox(height: 24),

          // Timeline Chart
          _buildTimelineChart(),
          const SizedBox(height: 24),

          // Health Insights
          _buildHealthInsights(),
        ],
      ),
    );
  }

  Widget _buildSummaryCards() {
    final totalRecords = widget.relatedRecords.length;
    final activeRecords = widget.relatedRecords.where((r) => r.status?.toLowerCase() == 'active').length;
    final completedRecords = widget.relatedRecords.where((r) => r.status?.toLowerCase() == 'completed').length;
    final treatmentRecords = widget.relatedRecords.where((r) => r.recordType?.toLowerCase() == 'treatment').length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Health Summary',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Total Records',
                totalRecords.toString(),
                Icons.medical_services,
                UniversalEmptyStateTheme.health,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'Active',
                activeRecords.toString(),
                Icons.pending,
                Colors.orange,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildSummaryCard(
                'Completed',
                completedRecords.toString(),
                Icons.check_circle,
                Colors.green,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildSummaryCard(
                'Treatments',
                treatmentRecords.toString(),
                Icons.healing,
                Colors.red,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordTypeDistribution() {
    final typeDistribution = <String, int>{};
    for (final record in widget.relatedRecords) {
      final type = record.recordType ?? 'Unknown';
      typeDistribution[type] = (typeDistribution[type] ?? 0) + 1;
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Record Type Distribution',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            if (typeDistribution.isNotEmpty)
              SizedBox(
                height: 200,
                child: PieChart(
                  PieChartData(
                    sections: _buildPieChartSections(typeDistribution, _getTypeColors()),
                    centerSpaceRadius: 40,
                    sectionsSpace: 2,
                  ),
                ),
              ),
            const SizedBox(height: 16),
            _buildLegend(typeDistribution, _getTypeColors()),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusDistribution() {
    final statusDistribution = <String, int>{};
    for (final record in widget.relatedRecords) {
      final status = record.status ?? 'Unknown';
      statusDistribution[status] = (statusDistribution[status] ?? 0) + 1;
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status Distribution',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ...statusDistribution.entries.map((entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: _getStatusColor(entry.key),
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(child: Text(entry.key)),
                  Text(
                    entry.value.toString(),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: _getStatusColor(entry.key),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildTimelineChart() {
    final sortedRecords = List<HealthRecordIsar>.from(widget.relatedRecords)
      ..sort((a, b) => (a.date ?? DateTime.now()).compareTo(b.date ?? DateTime.now()));

    if (sortedRecords.length < 2) {
      return Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Text(
                'Health Timeline',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              const Text('Need at least 2 records to show timeline'),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Health Timeline',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: LineChart(
                LineChartData(
                  gridData: const FlGridData(show: true),
                  titlesData: const FlTitlesData(
                    leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: true)),
                    bottomTitles: AxisTitles(sideTitles: SideTitles(showTitles: true)),
                    rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
                  ),
                  borderData: FlBorderData(show: true),
                  lineBarsData: [
                    LineChartBarData(
                      spots: _buildTimelineSpots(sortedRecords),
                      isCurved: false,
                      color: UniversalEmptyStateTheme.health,
                      barWidth: 3,
                      dotData: FlDotData(show: true),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHealthInsights() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Health Insights',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ..._generateInsights(),
          ],
        ),
      ),
    );
  }

  List<Widget> _generateInsights() {
    final insights = <Widget>[];
    
    // Recent activity insight
    final recentRecords = widget.relatedRecords.where((r) {
      if (r.date == null) return false;
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      return r.date!.isAfter(thirtyDaysAgo);
    }).length;

    insights.add(_buildInsightItem(
      'Recent Activity',
      '$recentRecords health records in the last 30 days',
      Icons.timeline,
      recentRecords > 3 ? Colors.orange : Colors.green,
    ));

    // Treatment frequency insight
    final treatmentCount = widget.relatedRecords.where((r) => 
      r.recordType?.toLowerCase() == 'treatment').length;
    final treatmentPercentage = widget.relatedRecords.isNotEmpty 
        ? (treatmentCount / widget.relatedRecords.length * 100).round()
        : 0;

    insights.add(_buildInsightItem(
      'Treatment Frequency',
      '$treatmentPercentage% of records are treatments',
      Icons.healing,
      treatmentPercentage > 50 ? Colors.red : Colors.green,
    ));

    // Vaccination status
    final vaccinationCount = widget.relatedRecords.where((r) => 
      r.recordType?.toLowerCase() == 'vaccination').length;

    insights.add(_buildInsightItem(
      'Vaccination Records',
      '$vaccinationCount vaccination records found',
      Icons.vaccines,
      vaccinationCount > 0 ? Colors.green : Colors.orange,
    ));

    return insights;
  }

  Widget _buildInsightItem(String title, String description, IconData icon, Color color) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(Map<String, int> data, Map<String, Color> colors) {
    final total = data.values.fold(0, (sum, value) => sum + value);
    return data.entries.map((entry) {
      final percentage = (entry.value / total) * 100;
      return PieChartSectionData(
        color: colors[entry.key] ?? Colors.grey,
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildLegend(Map<String, int> data, Map<String, Color> colors) {
    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: data.entries.map((entry) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: colors[entry.key] ?? Colors.grey,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text('${entry.key} (${entry.value})'),
          ],
        );
      }).toList(),
    );
  }

  List<FlSpot> _buildTimelineSpots(List<HealthRecordIsar> sortedRecords) {
    return sortedRecords.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.key.toDouble());
    }).toList();
  }

  Map<String, Color> _getTypeColors() {
    return {
      'Treatment': Colors.orange,
      'Vaccination': Colors.green,
      'Checkup': Colors.blue,
      'Surgery': Colors.red,
      'Unknown': Colors.grey,
    };
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'active':
        return Colors.orange;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }
}
