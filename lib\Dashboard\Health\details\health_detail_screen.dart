import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../models/health_record_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/health_handler.dart';
import '../../Cattle/services/cattle_handler.dart';
import '../../../utils/message_utils.dart';
import '../../../widgets/reusable_tab_bar.dart';
import '../../widgets/index.dart';
import 'health_analytics_tab.dart';
import 'health_detail_records_tab.dart';

class HealthDetailScreen extends StatefulWidget {
  final HealthRecordIsar healthRecord;
  final String title;
  final VoidCallback? onRefresh;

  const HealthDetailScreen({
    Key? key,
    required this.healthRecord,
    required this.title,
    this.onRefresh,
  }) : super(key: key);

  @override
  State<HealthDetailScreen> createState() => _HealthDetailScreenState();
}

class _HealthDetailScreenState extends State<HealthDetailScreen>
    with SingleTickerProviderStateMixin, UniversalScreenState, UniversalDataRefresh {
  late TabController _tabController;
  final HealthHandler _healthHandler = GetIt.instance<HealthHandler>();
  final CattleHandler _cattleHandler = GetIt.instance<CattleHandler>();

  HealthRecordIsar? _healthRecord;
  CattleIsar? _cattle;
  List<HealthRecordIsar> _relatedRecords = [];
  bool _isLoading = true;

  // Tab configuration
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(() {
      setState(() {}); // Rebuild to update colors
    });

    _healthRecord = widget.healthRecord;
    _loadData();

    // Initialize tabs
    _tabs = [
      const TabItem(
        icon: Icons.analytics,
        label: 'Analytics',
        color: UniversalEmptyStateTheme.health,
      ),
      const TabItem(
        icon: Icons.list_alt,
        label: 'Records',
        color: UniversalEmptyStateTheme.health,
      ),
    ];
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // Load cattle information
      if (_healthRecord?.cattleBusinessId != null) {
        final cattle = await _cattleHandler.getCattleById(_healthRecord!.cattleBusinessId!);
        
        // Load related health records for the same cattle
        final relatedRecords = await _healthHandler.getHealthRecordsForCattle(
          _healthRecord!.cattleBusinessId!,
        );

        if (mounted) {
          setState(() {
            _cattle = cattle;
            _relatedRecords = relatedRecords;
            _isLoading = false;
          });
        }
      } else {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        HealthMessageUtils.showError(context, 'Failed to load health details: $e');
      }
    }
  }

  Future<void> _refreshData() async {
    await performRefresh(() => _loadData());
    widget.onRefresh?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        backgroundColor: UniversalEmptyStateTheme.health,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: _isLoading
          ? UniversalLoadingIndicator.health()
          : _healthRecord == null
              ? UniversalEmptyState.health(
                  title: 'Health Record Not Found',
                  message: 'The requested health record could not be loaded.',
                )
              : Column(
                  children: [
                    // Header with basic info
                    _buildHeaderCard(),
                    
                    // Tab Bar
                    ReusableTabBar.controlled(
                      controller: _tabController,
                      tabs: _tabs,
                      useMulticolor: false,
                      indicatorColor: UniversalEmptyStateTheme.health,
                    ),
                    
                    // Tab Content
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          // Analytics Tab
                          HealthAnalyticsTab(
                            healthRecord: _healthRecord!,
                            cattle: _cattle,
                            relatedRecords: _relatedRecords,
                          ),
                          
                          // Records Tab
                          HealthDetailRecordsTab(
                            healthRecord: _healthRecord!,
                            cattle: _cattle,
                            relatedRecords: _relatedRecords,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                CircleAvatar(
                  backgroundColor: UniversalEmptyStateTheme.health,
                  child: Icon(
                    _getRecordTypeIcon(_healthRecord?.recordType),
                    color: Colors.white,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _healthRecord?.recordType ?? 'Health Record',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      if (_cattle?.name != null)
                        Text(
                          'Cattle: ${_cattle!.name}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getStatusColor(_healthRecord?.status).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: _getStatusColor(_healthRecord?.status).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    _healthRecord?.status ?? 'Unknown',
                    style: TextStyle(
                      color: _getStatusColor(_healthRecord?.status),
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildInfoItem(
                    'Date',
                    _healthRecord?.date != null
                        ? _formatDate(_healthRecord!.date!)
                        : 'Unknown',
                    Icons.calendar_today,
                  ),
                ),
                Expanded(
                  child: _buildInfoItem(
                    'Type',
                    _healthRecord?.recordType ?? 'Unknown',
                    Icons.medical_services,
                  ),
                ),
              ],
            ),
            if (_healthRecord?.description != null && _healthRecord!.description!.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Description',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                _healthRecord!.description!,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ],
    );
  }

  IconData _getRecordTypeIcon(String? recordType) {
    switch (recordType?.toLowerCase()) {
      case 'vaccination':
        return Icons.vaccines;
      case 'treatment':
        return Icons.healing;
      case 'checkup':
        return Icons.medical_services;
      case 'surgery':
        return Icons.local_hospital;
      default:
        return Icons.health_and_safety;
    }
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Colors.orange;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
