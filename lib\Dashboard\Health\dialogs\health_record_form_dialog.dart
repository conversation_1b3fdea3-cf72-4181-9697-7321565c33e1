import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../models/health_record_isar.dart';
import '../../../utils/message_utils.dart'; // Assuming this is your custom message utility

// --- Constants for better organization and reusability ---
class _Constants {
  static const String addTitle = 'Add Health Record';
  static const String editTitle = 'Edit Health Record';
  static const String cattleLabel = 'Cattle';
  static const String dateLabel = 'Date';
  static const String conditionLabel = 'Condition/Diagnosis';
  static const String treatmentLabel = 'Treatment';
  static const String veterinarianLabel = 'Veterinarian';
  static const String costLabel = 'Cost';
  static const String notesLabel = 'Notes';
  static const String selectCattleHint = 'Please select a cattle';
  static const String conditionRequiredError = 'Please enter a condition';
  static const String saveButtonText = 'Save';
  static const String cancelButtonText = 'Cancel';

  static const String saveError = 'Failed to save health record. Please check your inputs and try again.';
  
  static const inputPadding = EdgeInsets.symmetric(horizontal: 16, vertical: 12);
  static const formPadding = EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0);
  static final borderRadius = BorderRadius.circular(8);
}

class HealthRecordFormDialog extends StatefulWidget {
  final HealthRecordIsar? healthRecord;
  final List<CattleIsar> cattle;
  final Future<void> Function(HealthRecordIsar)? onSave;

  const HealthRecordFormDialog({
    Key? key,
    this.healthRecord,
    required this.cattle,
    this.onSave,
  }) : super(key: key);

  @override
  State<HealthRecordFormDialog> createState() => _HealthRecordFormDialogState();
}

class _HealthRecordFormDialogState extends State<HealthRecordFormDialog> {
  final _formKey = GlobalKey<FormState>();

  // --- State Controllers and Variables ---
  // Use controllers for text fields and simple variables for other types.
  late final TextEditingController _conditionController;
  late final TextEditingController _treatmentController;
  late final TextEditingController _veterinarianController;
  late final TextEditingController _costController;
  late final TextEditingController _notesController;

  String? _selectedCattleId;
  DateTime _selectedDate = DateTime.now();
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    final record = widget.healthRecord;

    // Initialize controllers with existing data or empty strings
    _conditionController = TextEditingController(text: record?.condition ?? '');
    _treatmentController = TextEditingController(text: record?.treatment ?? '');
    _veterinarianController = TextEditingController(text: record?.veterinarian ?? '');
    _costController = TextEditingController(text: record?.cost?.toString() ?? '');
    _notesController = TextEditingController(text: record?.notes ?? '');

    // Set initial values for non-text fields
    _selectedDate = record?.date ?? DateTime.now();

    // Safely set the initial cattle ID
    if (record != null && widget.cattle.any((c) => c.tagId == record.cattleId)) {
      _selectedCattleId = record.cattleId;
    } else if (widget.cattle.isNotEmpty) {
      _selectedCattleId = widget.cattle.first.tagId;
    }
  }

  @override
  void dispose() {
    // Dispose all controllers
    _conditionController.dispose();
    _treatmentController.dispose();
    _veterinarianController.dispose();
    _costController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _handleSave() async {
    // Validate the form. If it fails, do nothing.
    if (!(_formKey.currentState?.validate() ?? false)) {
      return;
    }

    setState(() => _isSaving = true);

    try {
      // Create the record object from form data
      final record = HealthRecordIsar.create(
        recordId: widget.healthRecord?.recordId,
        cattleId: _selectedCattleId ?? '',
        date: _selectedDate,
        diagnosis: _conditionController.text.trim(),
        treatment: _treatmentController.text.trim(),
        notes: _notesController.text.trim(),
        cost: double.tryParse(_costController.text.trim()) ?? 0.0,
        veterinarian: _veterinarianController.text.trim(),
      );

      // Await the save operation if a callback is provided
      if (widget.onSave != null) {
        await widget.onSave!(record);
      }
      
      // Pop the dialog if the widget is still mounted
      if (mounted) {
        Navigator.of(context).pop(record); // Pop with the saved record
      }
    } catch (e) {
      if (mounted) {
        HealthMessageUtils.showError(context, _Constants.saveError);
      }
    } finally {
      // Ensure the saving state is always reset
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    // Show a loading dialog if cattle list is empty and being fetched
    if (widget.cattle.isEmpty) {
      return const Dialog(
        child: SizedBox(
          height: 200,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text("Loading Cattle Data..."),
              ],
            ),
          ),
        ),
      );
    }
    
    return AlertDialog(
      title: Text(widget.healthRecord == null ? _Constants.addTitle : _Constants.editTitle),
      content: _buildFormContent(),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text(_Constants.cancelButtonText),
        ),
        ElevatedButton(
          onPressed: _isSaving ? null : _handleSave,
          child: _isSaving
              ? const SizedBox(
                  width: 16, height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                )
              : const Text(_Constants.saveButtonText),
        ),
      ],
    );
  }

  /// **FIXED LAYOUT**: Builds the form content inside a sized box with a scroll view.
  Widget _buildFormContent() {
    return SizedBox(
      width: MediaQuery.of(context).size.width * 0.9, // Constrain width
      height: MediaQuery.of(context).size.height * 0.7, // Constrain height
      child: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: _Constants.formPadding,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildCattleDropdown(),
              const SizedBox(height: 16),
              _buildDatePicker(),
              const SizedBox(height: 16),
              _buildTextField(
                controller: _conditionController,
                label: _Constants.conditionLabel,
                icon: Icons.medical_information,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return _Constants.conditionRequiredError;
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              _buildTextField(
                controller: _treatmentController,
                label: _Constants.treatmentLabel,
                icon: Icons.healing,
              ),
              const SizedBox(height: 16),
              _buildTextField(
                controller: _veterinarianController,
                label: _Constants.veterinarianLabel,
                icon: Icons.person,
              ),
              const SizedBox(height: 16),
              _buildTextField(
                controller: _costController,
                label: _Constants.costLabel,
                icon: Icons.attach_money,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
              ),
              const SizedBox(height: 16),
              _buildTextField(
                controller: _notesController,
                label: _Constants.notesLabel,
                icon: Icons.note_alt,
                maxLines: 3,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // --- Reusable Widget Builders ---

  Widget _buildCattleDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedCattleId,
      decoration: InputDecoration(
        labelText: _Constants.cattleLabel,
        border: OutlineInputBorder(borderRadius: _Constants.borderRadius),
        contentPadding: _Constants.inputPadding,
        prefixIcon: const Icon(Icons.pets, color: Colors.brown),
      ),
      items: widget.cattle.map((cattle) {
        return DropdownMenuItem<String>(
          value: cattle.tagId,
          child: Text('${cattle.name ?? "Unnamed"} (${cattle.tagId})'),
        );
      }).toList(),
      onChanged: (value) {
        if (value != null) {
          setState(() => _selectedCattleId = value);
        }
      },
      validator: (value) => value == null ? _Constants.selectCattleHint : null,
    );
  }

  Widget _buildDatePicker() {
    return InkWell(
      onTap: () async {
        final pickedDate = await showDatePicker(
          context: context,
          initialDate: _selectedDate,
          firstDate: DateTime(2000),
          lastDate: DateTime.now().add(const Duration(days: 365)),
        );
        if (pickedDate != null) {
          setState(() => _selectedDate = pickedDate);
        }
      },
      child: InputDecorator(
        decoration: InputDecoration(
          labelText: _Constants.dateLabel,
          border: OutlineInputBorder(borderRadius: _Constants.borderRadius),
          contentPadding: _Constants.inputPadding,
          prefixIcon: const Icon(Icons.calendar_today, color: Colors.blue),
        ),
        child: Text(DateFormat.yMMMd().format(_selectedDate)),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    int? maxLines = 1,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(borderRadius: _Constants.borderRadius),
        contentPadding: _Constants.inputPadding,
        prefixIcon: Icon(icon),
      ),
      keyboardType: keyboardType,
      validator: validator,
      maxLines: maxLines,
    );
  }
}