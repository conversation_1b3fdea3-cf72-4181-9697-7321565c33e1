import 'package:flutter/material.dart';

import '../../../widgets/reusable_tab_bar.dart';
import '../controllers/health_controller.dart';
import '../dialogs/health_record_form_dialog.dart';
import '../tabs/health_records_tab.dart';
import '../tabs/health_analytics_tab.dart';
import '../tabs/health_insights_tab.dart';
import '../tabs/treatments_tab.dart';
import '../tabs/vaccinations_tab.dart';
import '../../../utils/message_utils.dart';
import '../../../routes/app_routes.dart';
import '../../../services/database/database_helper.dart';
import '../../widgets/index.dart'; // Import Universal Components

class HealthScreen extends StatefulWidget {
  const HealthScreen({super.key});

  @override
  State<HealthScreen> createState() => _HealthScreenState();
}

class _HealthScreenState extends State<HealthScreen>
    with TickerProviderStateMixin, UniversalScreenState, UniversalDataRefresh {
  late TabController _tabController;
  late HealthController _healthController;

  final List<TabItem> _tabs = const [
    TabItem(icon: Icons.analytics, label: 'Analytics'),
    TabItem(icon: Icons.list, label: 'Records'),
    TabItem(icon: Icons.healing, label: 'Treatments'),
    TabItem(icon: Icons.vaccines, label: 'Vaccinations'),
    TabItem(icon: Icons.lightbulb, label: 'Insights'),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _healthController = HealthController();
    _healthController.loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _healthController.dispose();
    super.dispose();
  }

  ScreenState _mapControllerStateToScreenState(ControllerState controllerState) {
    switch (controllerState) {
      case ControllerState.initial:
        return ScreenState.initial;
      case ControllerState.loading:
        return ScreenState.loading;
      case ControllerState.loaded:
        return ScreenState.loaded;
      case ControllerState.error:
        return ScreenState.error;
      case ControllerState.empty:
        return ScreenState.empty;
    }
  }

  void _showAddHealthRecordDialog() async {
    // Fetch cattle data from database using DatabaseHelper
    final dbHelper = DatabaseHelper.instance;
    final allCattle = await dbHelper.cattleHandler.getAllCattle();

    if (!context.mounted) return;

    if (allCattle.isEmpty) {
      // Handle the empty case, e.g., show a message or return
      return;
    }

    if (!context.mounted) return;

    final dialogContext = context;
    showDialog(
      context: dialogContext,
      builder: (context) => HealthRecordFormDialog(
        cattle: allCattle,
      ),
    ).then((record) async {
      if (record != null) {
        try {
          await _healthController.addHealthRecord(record);

          if (!mounted) return;

          // Use standardized success message
          if (mounted) {
            HealthMessageUtils.showSuccess(dialogContext,
                HealthMessageUtils.healthRecordCreated());
          }

        } catch (e) {
          debugPrint('Error adding health record: $e');

          if (!mounted) return;

          // Use standardized error message
          if (mounted) {
            HealthMessageUtils.showError(dialogContext, 'Error adding health record');
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Health Management'),
        backgroundColor: UniversalEmptyStateTheme.health,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.healthReport,
            ),
            tooltip: 'View Health Reports',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => performRefresh(() => _healthController.refresh()),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar with multicolor support
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
            useMulticolor: true,
            indicatorColor: UniversalEmptyStateTheme.health,
          ),
          // TabBarView
          Expanded(
            child: ListenableBuilder(
              listenable: _healthController,
              builder: (context, child) {
                final controllerState = _healthController.state;

                return UniversalStateBuilder(
                  state: _mapControllerStateToScreenState(controllerState),
                  errorMessage: _healthController.errorMessage,
                  onRetry: () => performRefresh(() => _healthController.refresh()),
                  moduleColor: UniversalEmptyStateTheme.health,
                  loadingWidget: UniversalLoadingIndicator.health(),
                  errorWidget: UniversalErrorIndicator.health(
                    message: _healthController.errorMessage ?? 'Failed to load health data',
                    onRetry: () => performRefresh(() => _healthController.refresh()),
                  ),
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      HealthAnalyticsTab(controller: _healthController),
                      HealthRecordsTab(controller: _healthController),
                      TreatmentsTab(controller: _healthController),
                      VaccinationsTab(controller: _healthController),
                      HealthInsightsTab(controller: _healthController),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddHealthRecordDialog,
        backgroundColor: UniversalEmptyStateTheme.health,
        foregroundColor: Colors.white,
        tooltip: 'Add Health Record',
        child: const Icon(Icons.add),
      ),
    );
  }
}


