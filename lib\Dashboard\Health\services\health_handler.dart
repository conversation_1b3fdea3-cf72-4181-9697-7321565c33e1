import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';

import '../models/health_record_isar.dart';
import '../models/medication_isar.dart';
import '../models/treatment_isar.dart';
import '../models/vaccination_record_isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

/// Consolidated handler for all Health module database operations
class HealthHandler {
  static final Logger _logger = Logger('HealthHandler');
  final IsarService _isarService;

  // Singleton instance
  static final HealthHandler _instance = HealthHandler._internal();
  static HealthHandler get instance => _instance;

  // Private constructor
  HealthHandler._internal() : _isarService = GetIt.instance<IsarService>();

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== HEALTH RECORDS ===//

  /// Retrieves health records for a specific cattle
  Future<List<HealthRecordIsar>> getHealthRecordsForCattle(
      String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.healthRecordIsars
          .filter()
          .cattleIdEqualTo(cattleId)
          .findAll();
    } catch (e) {
      _logger.severe('Error retrieving health records for cattle $cattleId', e);
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to retrieve health records', e.toString());
    }
  }

  /// Adds a new health record for a cattle
  Future<void> addHealthRecord(HealthRecordIsar record) async {
    try {
      if (record.cattleId?.isEmpty ?? true) {
        throw ValidationException('Cattle ID is required');
      }

      await _isar.writeTxn(() async {
        await _isar.healthRecordIsars.put(record);
      });
    } catch (e) {
      _logger.severe(
          'Error adding health record for cattle ${record.cattleId}', e);
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to add health record', e.toString());
    }
  }

  /// Retrieves all health records across all cattle
  Future<List<HealthRecordIsar>> getAllHealthRecords() async {
    try {
      return await _isar.healthRecordIsars.where().findAll();
    } catch (e) {
      _logger.severe('Error retrieving all health records', e);
      throw DatabaseException(
          'Failed to retrieve all health records', e.toString());
    }
  }

  /// Adds or updates a health record
  /// Returns the saved record with its ID
  Future<HealthRecordIsar> addOrUpdateHealthRecord(HealthRecordIsar record) async {
    try {
      // Validate the health record
      _validateHealthRecord(record);

      if (record.recordId?.isEmpty ?? true) {
        // New record without ID - use generateRecordId with required params
        record.recordId = HealthRecordIsar.generateRecordId(
            record.cattleId ?? '',
            record.date ?? DateTime.now(),
            record.recordType ?? 'general');
      }

      late int recordId;
      // Add/update the record
      await _isar.writeTxn(() async {
        recordId = await _isar.healthRecordIsars.put(record);
      });

      // Fetch the saved record to ensure we have the complete object with ID
      final savedRecord = await _isar.healthRecordIsars.get(recordId);
      if (savedRecord == null) {
        throw DatabaseException('Failed to retrieve saved record', 'Record not found after saving');
      }

      _logger
          .info('Successfully added/updated health record: ${savedRecord.recordId}');

      return savedRecord;
    } catch (e) {
      _logger.severe('Error adding/updating health record', e);
      if (e is ValidationException) rethrow;
      throw DatabaseException(
          'Failed to add/update health record', e.toString());
    }
  }

  /// Adds a new treatment record
  /// Returns the saved treatment with its ID
  Future<MedicationIsar> addTreatment(MedicationIsar treatment) async {
    try {
      // Validate the treatment
      if (treatment.name?.isEmpty ?? true) {
        throw ValidationException('Treatment name is required');
      }
      if (treatment.cattleId?.isEmpty ?? true) {
        throw ValidationException('Cattle ID is required');
      }
      if (treatment.dosage?.isEmpty ?? true) {
        throw ValidationException('Dosage is required');
      }

      // Generate ID if needed
      if (treatment.businessId?.isEmpty ?? true) {
        treatment.businessId = MedicationIsar.generateBusinessId(
            treatment.cattleBusinessId ?? '',
            treatment.startDate ?? DateTime.now(),
            treatment.name ?? '');
      }

      late int recordId;
      // Add the treatment
      await _isar.writeTxn(() async {
        recordId = await _isar.medicationIsars.put(treatment);
      });

      // Fetch the saved record to ensure we have the complete object with ID
      final savedTreatment = await _isar.medicationIsars.get(recordId);
      if (savedTreatment == null) {
        throw DatabaseException('Failed to retrieve saved treatment', 'Treatment not found after saving');
      }

      _logger.info('Successfully added treatment: ${savedTreatment.businessId}');

      return savedTreatment;
    } catch (e) {
      _logger.severe('Error adding treatment', e);
      if (e is ValidationException) rethrow;
      throw DatabaseException('Failed to add treatment', e.toString());
    }
  }

  /// Deletes a health record by ID
  Future<void> deleteHealthRecord(String recordId) async {
    try {
      if (recordId.isEmpty) {
        throw ValidationException('Record ID is required');
      }

      // Check if the record exists
      final record = await _isar.healthRecordIsars
          .filter()
          .recordIdEqualTo(recordId)
          .findFirst();

      if (record == null) {
        throw RecordNotFoundException(
            'Health record not found with ID: $recordId');
      }

      // Delete the record
      await _isar.writeTxn(() async {
        await _isar.healthRecordIsars.delete(record.id);
      });

      _logger.info('Successfully deleted health record: $recordId');
    } catch (e) {
      _logger.severe('Error deleting health record: $recordId', e);
      if (e is ValidationException || e is RecordNotFoundException) rethrow;
      throw DatabaseException('Failed to delete health record', e.toString());
    }
  }

  /// Validates a health record
  void _validateHealthRecord(HealthRecordIsar record) {
    if (record.cattleId?.isEmpty ?? true) {
      throw ValidationException('Cattle ID is required');
    }

    if (record.date == null) {
      throw ValidationException('Record date is required');
    }

    if (record.recordType?.isEmpty ?? true) {
      throw ValidationException('Record type is required');
    }
  }

  //=== MEDICATION RECORDS ===//

  /// Retrieves medications for a specific cattle
  Future<List<MedicationIsar>> getMedicationsForCattle(String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.medicationIsars
          .filter()
          .cattleIdEqualTo(cattleId)
          .findAll();
    } catch (e) {
      _logger.severe('Error retrieving medications for cattle $cattleId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException('Failed to retrieve medications', e.toString());
    }
  }

  /// Retrieves all medication records across all cattle
  Future<List<MedicationIsar>> getAllMedications() async {
    try {
      return await _isar.medicationIsars.where().findAll();
    } catch (e) {
      _logger.severe('Error retrieving all medications', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to retrieve all medications', e.toString());
    }
  }

  /// Adds or updates a medication record for a specific cattle
  Future<void> addOrUpdateMedication(
      String cattleId, MedicationIsar record) async {
    try {
      // Validate required fields
      _validateMedicationRecord(record);

      // Ensure cattleId is set on the record
      record.cattleId = cattleId;

      // Use auto-increment ID
      // No need to set record.id manually as Isar will auto-increment

      await _isar.writeTxn(() async {
        await _isar.medicationIsars.put(record);
      });

      _logger.info(
          'Successfully added/updated medication record for cattle $cattleId');
    } catch (e) {
      _logger.severe(
          'Error adding/updating medication record for cattle $cattleId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to add/update medication record', e.toString());
    }
  }

  /// Deletes a medication record for a specific cattle
  Future<void> deleteMedication(String recordId) async {
    try {
      if (recordId.isEmpty) {
        throw ValidationException('Record ID is required');
      }

      // Find the medication by record ID (businessId)
      final medication = await _isar.medicationIsars
          .filter()
          .businessIdEqualTo(recordId)
          .findFirst();

      if (medication == null) {
        throw RecordNotFoundException(
            'Medication record not found with ID: $recordId');
      }

      await _isar.writeTxn(() async {
        await _isar.medicationIsars.delete(medication.id);
      });

      _logger.info('Successfully deleted medication record: $recordId');
    } catch (e) {
      _logger.severe('Error deleting medication record $recordId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to delete medication record', e.toString());
    }
  }

  /// Validates a medication record
  void _validateMedicationRecord(MedicationIsar record) {
    if (record.startDate == null) {
      throw ValidationException('Medication record must have a date');
    }

    if (record.name == null || record.name!.trim().isEmpty) {
      throw ValidationException(
          'Medication record must have a medication name');
    }

    if (record.dosage == null || record.dosage!.trim().isEmpty) {
      throw ValidationException('Medication record must have a dosage');
    }
  }

  //=== TREATMENT RECORDS ===//

  /// Retrieves treatments for a specific cattle
  Future<List<TreatmentIsar>> getTreatmentsForCattle(String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.treatmentIsars
          .filter()
          .cattleIdEqualTo(cattleId)
          .findAll();
    } catch (e) {
      _logger.severe('Error retrieving treatments for cattle $cattleId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException('Failed to retrieve treatments', e.toString());
    }
  }

  /// Retrieves all treatment records across all cattle
  Future<List<TreatmentIsar>> getAllTreatments() async {
    try {
      return await _isar.treatmentIsars.where().findAll();
    } catch (e) {
      _logger.severe('Error retrieving all treatments', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to retrieve all treatments', e.toString());
    }
  }

  /// Adds or updates a treatment record for a specific cattle
  Future<void> addOrUpdateTreatment(
      String cattleId, TreatmentIsar record) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      // Validate the record
      _validateTreatmentRecord(record);

      // Set cattleId
      record.cattleId = cattleId;

      await _isar.writeTxn(() async {
        await _isar.treatmentIsars.put(record);
      });

      _logger.info(
          'Successfully added/updated treatment record for cattle $cattleId');
    } catch (e) {
      _logger.severe(
          'Error adding/updating treatment record for cattle $cattleId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to add/update treatment record', e.toString());
    }
  }

  /// Deletes a treatment record
  Future<void> deleteTreatment(String recordId) async {
    try {
      if (recordId.isEmpty) {
        throw ValidationException('Record ID is required');
      }

      // Find the treatment by businessId
      final treatment = await _isar.treatmentIsars
          .filter()
          .businessIdEqualTo(recordId)
          .findFirst();

      if (treatment == null) {
        throw RecordNotFoundException(
            'Treatment record not found with ID: $recordId');
      }

      await _isar.writeTxn(() async {
        await _isar.treatmentIsars.delete(treatment.id);
      });

      _logger.info('Successfully deleted treatment record: $recordId');
    } catch (e) {
      _logger.severe('Error deleting treatment record: $recordId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to delete treatment record', e.toString());
    }
  }

  /// Validates a treatment record
  void _validateTreatmentRecord(TreatmentIsar record) {
    if (record.date == null) {
      throw ValidationException('Treatment date is required');
    }

    if (record.treatment == null || record.treatment!.trim().isEmpty) {
      throw ValidationException('Treatment description is required');
    }
  }

  //=== VACCINATION RECORDS ===//

  /// Retrieves vaccinations for a specific cattle
  Future<List<VaccinationIsar>> getVaccinationsForCattle(
      String cattleId) async {
    try {
      if (cattleId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.vaccinationIsars
          .filter()
          .cattleIdEqualTo(cattleId)
          .findAll();
    } catch (e) {
      _logger.severe('Error retrieving vaccinations for cattle $cattleId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException('Failed to retrieve vaccinations', e.toString());
    }
  }

  /// Retrieves all vaccination records across all cattle
  Future<List<VaccinationIsar>> getAllVaccinations() async {
    try {
      return await _isar.vaccinationIsars.where().findAll();
    } catch (e) {
      _logger.severe('Error retrieving all vaccinations', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to retrieve all vaccinations', e.toString());
    }
  }

  /// Adds or updates a vaccination record for a specific cattle
  /// Returns the saved vaccination with its ID
  Future<VaccinationIsar> addOrUpdateVaccination(
      String cattleId, VaccinationIsar record) async {
    try {
      // Validate required fields
      _validateVaccinationRecord(record);

      // Ensure cattleId is set on the record
      record.cattleId = cattleId;

      late int recordId;
      await _isar.writeTxn(() async {
        recordId = await _isar.vaccinationIsars.put(record);
      });

      // Fetch the saved record to ensure we have the complete object with ID
      final savedVaccination = await _isar.vaccinationIsars.get(recordId);
      if (savedVaccination == null) {
        throw DatabaseException('Failed to retrieve saved vaccination', 'Vaccination not found after saving');
      }

      _logger.info(
          'Successfully added/updated vaccination record for cattle $cattleId');

      return savedVaccination;
    } catch (e) {
      _logger.severe(
          'Error adding/updating vaccination record for cattle $cattleId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to add/update vaccination record', e.toString());
    }
  }

  /// Deletes a vaccination record
  Future<void> deleteVaccination(String recordId) async {
    try {
      if (recordId.isEmpty) {
        throw ValidationException('Record ID is required');
      }

      // Find the vaccination by recordId
      final vaccination = await _isar.vaccinationIsars
          .filter()
          .recordIdEqualTo(recordId)
          .findFirst();

      if (vaccination == null) {
        throw RecordNotFoundException(
            'Vaccination record not found with ID: $recordId');
      }

      await _isar.writeTxn(() async {
        await _isar.vaccinationIsars.delete(vaccination.id);
      });

      _logger.info('Successfully deleted vaccination record: $recordId');
    } catch (e) {
      _logger.severe('Error deleting vaccination record: $recordId', e);
      if (e is DatabaseException) rethrow;
      throw DatabaseException(
          'Failed to delete vaccination record', e.toString());
    }
  }

  /// Validates a vaccination record
  void _validateVaccinationRecord(VaccinationIsar record) {
    if (record.date == null) {
      throw ValidationException('Vaccination date is required');
    }

    if (record.vaccineName == null || record.vaccineName!.trim().isEmpty) {
      throw ValidationException('Vaccine name is required');
    }
  }
}
