import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../controllers/health_controller.dart';
import '../../widgets/index.dart';
import '../../../constants/app_tabs.dart';

class HealthAnalyticsTab extends StatelessWidget {
  final HealthController controller;

  const HealthAnalyticsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Cards
          _buildSummaryCards(context),
          const SizedBox(height: 24),
          
          // Record Type Distribution Chart
          _buildRecordTypeChart(context),
          const SizedBox(height: 24),
          
          // Status Distribution Chart
          _buildStatusChart(context),
          const SizedBox(height: 24),
          
          // Quick Stats
          _buildQuickStats(context),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            context,
            'Total Records',
            controller.totalHealthRecords.toString(),
            Icons.medical_services,
            UniversalEmptyStateTheme.health,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            context,
            'Active',
            controller.activeRecords.toString(),
            Icons.pending,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            context,
            'Completed',
            controller.completedRecords.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: color, size: 24),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordTypeChart(BuildContext context) {
    final typeData = controller.recordsByType;
    
    if (typeData.isEmpty || controller.totalHealthRecords == 0) {
      return UniversalEmptyState.health(
        title: 'No Data Available',
        message: 'No record type data available',
      );
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Record Type Distribution',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: _buildTypePieChartSections(typeData),
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildLegend(typeData, _getTypeColors()),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChart(BuildContext context) {
    final statusData = controller.recordsByStatus;
    
    if (statusData.isEmpty || controller.totalHealthRecords == 0) {
      return UniversalEmptyState.health(
        title: 'No Data Available',
        message: 'No status data available',
      );
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Status Distribution',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: PieChart(
                PieChartData(
                  sections: _buildStatusPieChartSections(statusData),
                  centerSpaceRadius: 40,
                  sectionsSpace: 2,
                ),
              ),
            ),
            const SizedBox(height: 16),
            _buildLegend(statusData, _getStatusColors()),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Statistics',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildStatRow('Total Health Records', controller.totalHealthRecords.toString()),
            _buildStatRow('Treatment Records', controller.treatmentRecords.toString()),
            _buildStatRow('Vaccination Records', controller.vaccinationRecords.toString()),
            _buildStatRow('Active Records', controller.activeRecords.toString()),
            _buildStatRow('Completed Records', controller.completedRecords.toString()),
          ],
        ),
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(
            value,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  List<PieChartSectionData> _buildTypePieChartSections(Map<String, int> data) {
    final colors = _getTypeColors();
    return data.entries.map((entry) {
      final percentage = (entry.value / controller.totalHealthRecords) * 100;
      return PieChartSectionData(
        color: colors[entry.key] ?? Colors.grey,
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  List<PieChartSectionData> _buildStatusPieChartSections(Map<String, int> data) {
    final colors = _getStatusColors();
    return data.entries.map((entry) {
      final percentage = (entry.value / controller.totalHealthRecords) * 100;
      return PieChartSectionData(
        color: colors[entry.key] ?? Colors.grey,
        value: entry.value.toDouble(),
        title: '${percentage.toStringAsFixed(1)}%',
        radius: 60,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();
  }

  Widget _buildLegend(Map<String, int> data, Map<String, Color> colors) {
    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: data.entries.map((entry) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: colors[entry.key] ?? Colors.grey,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text('${entry.key} (${entry.value})'),
          ],
        );
      }).toList(),
    );
  }

  Map<String, Color> _getTypeColors() {
    return {
      'Treatment': Colors.orange,
      'Vaccination': Colors.green,
      'Checkup': Colors.blue,
      'Surgery': Colors.red,
      'Unknown': Colors.grey,
    };
  }

  Map<String, Color> _getStatusColors() {
    return {
      'Active': Colors.orange,
      'Completed': Colors.green,
      'Pending': Colors.blue,
      'Cancelled': Colors.red,
      'Unknown': Colors.grey,
    };
  }
}
