import 'package:flutter/material.dart';
import '../controllers/health_controller.dart';
import '../../widgets/index.dart';

class HealthInsightsTab extends StatelessWidget {
  final HealthController controller;

  const HealthInsightsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Health Management Insights',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'AI-powered recommendations and insights for your cattle health management',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // Insights Cards
          ..._buildInsightCards(context),
        ],
      ),
    );
  }

  List<Widget> _buildInsightCards(BuildContext context) {
    final insights = _generateInsights();
    
    if (insights.isEmpty) {
      return [
        UniversalEmptyState.health(
          title: 'No Insights Available',
          message: 'Add more health records to get personalized insights',
        ),
      ];
    }

    return insights.map((insight) => _buildInsightCard(context, insight)).toList();
  }

  Widget _buildInsightCard(BuildContext context, HealthInsight insight) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  insight.icon,
                  color: insight.color,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    insight.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: insight.priority.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: insight.priority.color.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    insight.priority.label,
                    style: TextStyle(
                      color: insight.priority.color,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              insight.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (insight.recommendations.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Recommendations:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...insight.recommendations.map((rec) => Padding(
                padding: const EdgeInsets.only(left: 16, bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
                    Expanded(child: Text(rec)),
                  ],
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  List<HealthInsight> _generateInsights() {
    final insights = <HealthInsight>[];
    
    // Health Records Overview
    if (controller.totalHealthRecords == 0) {
      insights.add(HealthInsight(
        title: 'Start Health Record Tracking',
        description: 'You haven\'t added any health records yet. Start tracking your cattle\'s health to ensure optimal care and early disease detection.',
        icon: Icons.medical_services,
        color: UniversalEmptyStateTheme.health,
        priority: InsightPriority.high,
        recommendations: [
          'Record regular health checkups',
          'Track vaccination schedules',
          'Document any treatments or medications',
          'Monitor for signs of illness or injury',
        ],
      ));
    } else if (controller.totalHealthRecords < 10) {
      insights.add(HealthInsight(
        title: 'Building Health History',
        description: 'You have ${controller.totalHealthRecords} health records. Continue building comprehensive health histories for better care management.',
        icon: Icons.trending_up,
        color: Colors.blue,
        priority: InsightPriority.medium,
        recommendations: [
          'Maintain regular recording schedule',
          'Include detailed observations',
          'Track treatment outcomes',
        ],
      ));
    }

    // Active Records Analysis
    final activeInsight = _analyzeActiveRecords();
    if (activeInsight != null) {
      insights.add(activeInsight);
    }

    // Vaccination Schedule Insights
    final vaccinationInsight = _analyzeVaccinations();
    if (vaccinationInsight != null) {
      insights.add(vaccinationInsight);
    }

    // Treatment Analysis
    final treatmentInsight = _analyzeTreatments();
    if (treatmentInsight != null) {
      insights.add(treatmentInsight);
    }

    // Best Practices
    insights.add(HealthInsight(
      title: 'Health Management Best Practices',
      description: 'Follow these best practices to maintain optimal cattle health and ensure regulatory compliance.',
      icon: Icons.health_and_safety,
      color: Colors.green,
      priority: InsightPriority.medium,
      recommendations: [
        'Schedule regular veterinary checkups',
        'Maintain up-to-date vaccination records',
        'Monitor cattle daily for health changes',
        'Keep detailed treatment documentation',
        'Follow withdrawal periods for medications',
        'Implement biosecurity measures',
      ],
    ));

    return insights;
  }

  HealthInsight? _analyzeActiveRecords() {
    if (controller.totalHealthRecords == 0) return null;

    final activeCount = controller.activeRecords;
    final activePercentage = (activeCount / controller.totalHealthRecords) * 100;

    if (activeCount > 5) {
      return HealthInsight(
        title: 'High Number of Active Records',
        description: 'You have $activeCount active health records (${activePercentage.toStringAsFixed(1)}%). Monitor these cases closely.',
        icon: Icons.warning,
        color: Colors.orange,
        priority: InsightPriority.high,
        recommendations: [
          'Review active cases regularly',
          'Ensure timely follow-ups',
          'Monitor treatment progress',
          'Update record status when appropriate',
        ],
      );
    } else if (activeCount == 0 && controller.totalHealthRecords > 0) {
      return HealthInsight(
        title: 'No Active Health Cases',
        description: 'Great! You have no active health cases. This indicates good herd health management.',
        icon: Icons.check_circle,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Continue preventive care practices',
          'Maintain regular health monitoring',
          'Keep vaccination schedules current',
        ],
      );
    }

    return null;
  }

  HealthInsight? _analyzeVaccinations() {
    final vaccinationCount = controller.vaccinationRecords;
    final totalRecords = controller.totalHealthRecords;
    
    if (totalRecords == 0) return null;

    final vaccinationPercentage = (vaccinationCount / totalRecords) * 100;

    if (vaccinationPercentage < 30) {
      return HealthInsight(
        title: 'Low Vaccination Coverage',
        description: 'Only ${vaccinationPercentage.toStringAsFixed(1)}% of your health records are vaccinations. Consider reviewing your vaccination program.',
        icon: Icons.vaccines,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Review vaccination schedules with veterinarian',
          'Ensure core vaccines are up to date',
          'Consider regional disease risks',
          'Maintain vaccination records',
        ],
      );
    }

    return null;
  }

  HealthInsight? _analyzeTreatments() {
    final treatmentCount = controller.treatmentRecords;
    final totalRecords = controller.totalHealthRecords;
    
    if (totalRecords == 0) return null;

    final treatmentPercentage = (treatmentCount / totalRecords) * 100;

    if (treatmentPercentage > 60) {
      return HealthInsight(
        title: 'High Treatment Frequency',
        description: '${treatmentPercentage.toStringAsFixed(1)}% of your health records are treatments. This may indicate underlying health issues.',
        icon: Icons.healing,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Review common health issues in your herd',
          'Evaluate preventive care measures',
          'Consider environmental factors',
          'Consult with veterinarian about patterns',
        ],
      );
    }

    return null;
  }
}

class HealthInsight {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final InsightPriority priority;
  final List<String> recommendations;

  HealthInsight({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.priority,
    this.recommendations = const [],
  });
}

enum InsightPriority {
  low(Colors.green, 'Low'),
  medium(Colors.orange, 'Medium'),
  high(Colors.red, 'High');

  const InsightPriority(this.color, this.label);
  final Color color;
  final String label;
}
