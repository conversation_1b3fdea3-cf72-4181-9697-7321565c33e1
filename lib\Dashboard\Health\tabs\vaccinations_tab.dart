import 'package:flutter/material.dart';
import '../../../services/database/database_helper.dart';
import '../dialogs/vaccination_form_dialog.dart';
import '../../Cattle/models/cattle_isar.dart';
import 'package:intl/intl.dart';
import '../models/vaccination_record_isar.dart';
import '../controllers/health_controller.dart';
import '../../widgets/index.dart';
import '../../../utils/message_utils.dart';

class VaccinationsTab extends StatelessWidget {
  final HealthController controller;

  const VaccinationsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final vaccinationRecords = controller.getRecordsByType('vaccination');

    if (vaccinationRecords.isEmpty) {
      return UniversalEmptyState.health(
        title: 'No Vaccination Records',
        message: 'Vaccination records will appear here when added',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: vaccinationRecords.length,
      itemBuilder: (context, index) {
        final record = vaccinationRecords[index];
        final cattle = controller.getCattle(record.cattleBusinessId);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: const CircleAvatar(
              backgroundColor: Colors.green,
              child: Icon(
                Icons.vaccines,
                color: Colors.white,
              ),
            ),
            title: Text(cattle?.name ?? 'Unknown Cattle'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Vaccination: ${record.recordType ?? 'Unknown'}'),
                Text('Date: ${_formatDate(record.date)}'),
                if (record.status != null)
                  Text('Status: ${record.status}'),
              ],
            ),
            trailing: Icon(
              _getStatusIcon(record.status),
              color: _getStatusColor(record.status),
            ),
          ),
        );
      },
    );
  }

  IconData _getStatusIcon(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Icons.pending;
      case 'completed':
        return Icons.check_circle;
      case 'cancelled':
        return Icons.cancel;
      default:
        return Icons.help_outline;
    }
  }

  Color _getStatusColor(String? status) {
    switch (status?.toLowerCase()) {
      case 'active':
        return Colors.orange;
      case 'completed':
        return Colors.green;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}

class VaccinationsScreen extends StatefulWidget {
  const VaccinationsScreen({Key? key}) : super(key: key);

  @override
  State<VaccinationsScreen> createState() => _VaccinationsScreenState();
}

class _VaccinationsScreenState extends State<VaccinationsScreen> {
  late final DatabaseHelper _dbHelper;

  List<VaccinationIsar> _vaccinations = [];
  Map<String, CattleIsar> _cattleMap = {};
  bool _isLoading = true;
  String _searchQuery = '';
  String _selectedAnimalType = 'All';
  List<dynamic> _animalTypes = [];
  final TextEditingController _searchController = TextEditingController();
  String _selectedDateRange = 'All Time';
  String _selectedCattleId = 'All';
  List<String> _tagIds = [];

  final List<String> _dateRangeOptions = [
    'Today',
    '7 Days',
    '30 Days',
    '90 Days',
    'All Time',
  ];

  @override
  void initState() {
    super.initState();
    _dbHelper = DatabaseHelper.instance;
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      // Load all cattle
      final allCattle = await _dbHelper.cattleHandler.getAllCattle();

      // Get all vaccinations
      final allVaccinations = await _dbHelper.healthHandler.getAllVaccinations();

      // Create a map of cattle
      final Map<String, CattleIsar> cattleMap = {};

      for (final vaccination in allVaccinations) {
        final cattleId = vaccination.cattleId;
        if (cattleId != null) {
          final cattle = allCattle.where((c) => c.tagId?.toLowerCase() == cattleId.toLowerCase()).firstOrNull;
          if (cattle != null) {
            cattleMap[cattleId] = cattle;
          }
        }
      }

      if (mounted) {
        setState(() {
          _cattleMap = cattleMap;
          _vaccinations = allVaccinations;
          _animalTypes = []; // Simplified for now
          _tagIds = cattleMap.keys.toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        if (context.mounted) {
          HealthMessageUtils.showError(context, 'Error loading vaccinations: $e');
        }
      }
    }
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _searchController.clear();
      _selectedAnimalType = 'All';
      _selectedCattleId = 'All';
      _selectedDateRange = 'All Time';
    });
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Search Row
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by cattle name or tag ID',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          // Filter Row
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            child: Row(
              children: [
                // Animal Type Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Types'),
                        ),
                        ..._animalTypes.map((type) => PopupMenuItem(
                              value: type.name,
                              child: Text(type.name),
                            )),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedAnimalType = value;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedAnimalType,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Cattle Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Cattle'),
                        ),
                        ..._tagIds.map((tagId) {
                          final cattle = _cattleMap[tagId];
                          return PopupMenuItem(
                            value: tagId,
                            child: Text(cattle?.name ?? tagId),
                          );
                        }),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedCattleId = value;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedCattleId == 'All'
                                    ? 'All Cattle'
                                    : _cattleMap[_selectedCattleId]?.name ??
                                        _selectedCattleId,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Date Range Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => _dateRangeOptions
                          .map((range) => PopupMenuItem(
                                value: range,
                                child: Text(range),
                              ))
                          .toList(),
                      onSelected: (value) {
                        setState(() {
                          _selectedDateRange = value;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedDateRange,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Active Filters
          if (_selectedAnimalType != 'All' ||
              _selectedCattleId != 'All' ||
              _selectedDateRange != 'All Time' ||
              _searchQuery.isNotEmpty)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
              child: Row(
                children: [
                  const Text(
                    'Active Filters:',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 8),
                  if (_selectedDateRange != 'All Time')
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_selectedDateRange),
                        onDeleted: () =>
                            setState(() => _selectedDateRange = 'All Time'),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  if (_selectedAnimalType != 'All')
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_selectedAnimalType),
                        onDeleted: () =>
                            setState(() => _selectedAnimalType = 'All'),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  if (_selectedCattleId != 'All')
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_cattleMap[_selectedCattleId]?.name ??
                            _selectedCattleId),
                        onDeleted: () =>
                            setState(() => _selectedCattleId = 'All'),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  TextButton(
                    onPressed: _clearFilters,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      minimumSize: const Size(0, 24),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: const Text(
                      'Clear All',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          const Divider(height: 1),
        ],
      ),
    );
  }

  List<VaccinationIsar> _getFilteredVaccinations() {
    List<VaccinationIsar> filteredVaccinations = List.from(_vaccinations);

    if (_searchQuery.isNotEmpty) {
      final lowercaseQuery = _searchQuery.toLowerCase();
      filteredVaccinations = filteredVaccinations.where((vaccination) {
        final cattle = _cattleMap[vaccination.cattleId ?? ''];
        final vaccineName = vaccination.vaccineName?.toLowerCase() ?? '';
        final manufacturer = vaccination.manufacturer?.toLowerCase() ?? '';
        final batchNumber = vaccination.batchNumber?.toLowerCase() ?? '';
        final cattleName = cattle?.name?.toLowerCase() ?? '';

        return vaccineName.contains(lowercaseQuery) ||
            manufacturer.contains(lowercaseQuery) ||
            batchNumber.contains(lowercaseQuery) ||
            cattleName.contains(lowercaseQuery);
      }).toList();
    }

    // Sort vaccinations by date (newest first)
    filteredVaccinations.sort((a, b) {
      if (a.date == null && b.date == null) return 0;
      if (a.date == null) return 1;
      if (b.date == null) return -1;
      return b.date!.compareTo(a.date!);
    });

    if (_selectedDateRange != 'All Time') {
      filteredVaccinations = filteredVaccinations.where((vaccination) {
        if (vaccination.date == null) return false;

        final vaccinationDate = vaccination.date!;
        switch (_selectedDateRange) {
          case 'Today':
            final today = DateTime.now();
            return vaccinationDate.year == today.year &&
                vaccinationDate.month == today.month &&
                vaccinationDate.day == today.day;
          case '7 Days':
            final sevenDaysAgo = DateTime.now().subtract(const Duration(days: 7));
            return vaccinationDate.isAfter(sevenDaysAgo);
          case '30 Days':
            final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
            return vaccinationDate.isAfter(thirtyDaysAgo);
          case '90 Days':
            final ninetyDaysAgo = DateTime.now().subtract(const Duration(days: 90));
            return vaccinationDate.isAfter(ninetyDaysAgo);
          default:
            return true;
        }
      }).toList();
    }

    if (_selectedCattleId != 'All') {
      filteredVaccinations = filteredVaccinations.where((vaccination) {
        if (vaccination.cattleId == null) return false;
        return vaccination.cattleId == _selectedCattleId;
      }).toList();
    }

    if (_selectedAnimalType != 'All') {
      filteredVaccinations = filteredVaccinations.where((vaccination) {
        final cattle = _cattleMap[vaccination.cattleId ?? ''];
        if (cattle == null) return false;

        final animalTypeId = cattle.animalTypeId;
        if (animalTypeId == null) return false;

        final animalType = _animalTypes.firstWhere(
          (type) => type.id == animalTypeId,
          orElse: () => null,
        );
        return animalType?.name == _selectedAnimalType;
      }).toList();
    }

    return filteredVaccinations;
  }

  @override
  Widget build(BuildContext context) {
    final filteredVaccinations = _getFilteredVaccinations();

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Vaccinations',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: const Color(0xFF2E7D32),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showDialog<void>(
            context: context,
            builder: (context) {
              return VaccinationFormDialog(
                cattleId: '',
                cattle: _cattleMap.values.toList(),
                onSave: (vaccination) async {
                  await _dbHelper.healthHandler.addOrUpdateVaccination(
                    vaccination.cattleId ?? '',
                    vaccination
                  );
                  _loadData();
                  return true;
                },
              );
            },
          );
        },
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
      body: Column(
              children: [
                _buildSearchAndFilterSection(),
                Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : filteredVaccinations.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.vaccines,
                                size: 64,
                              color: Colors.grey[500],
                              ),
                              const SizedBox(height: 16),
                              Text(
                              'No Vaccination Records Found',
                                style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                color: Colors.grey[700],
                              ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                              'Add a new vaccination record',
                                style: TextStyle(
                                fontSize: 16,
                                  color: Colors.grey[500],
                                ),
                              ),
                              const SizedBox(height: 24),
                              ElevatedButton.icon(
                                onPressed: () {
                                showDialog<void>(
                                    context: context,
                                    builder: (context) {
                                      return VaccinationFormDialog(
                                        cattleId: '',
                                        cattle: _cattleMap.values.toList(),
                                        onSave: (vaccination) async {
                                          await _dbHelper.healthHandler.addOrUpdateVaccination(
                                            vaccination.cattleId ?? '',
                                            vaccination
                                          );
                                          _loadData();
                                          return true;
                                        },
                                      );
                                    },
                                );
                                },
                                icon: const Icon(Icons.add),
                                label: const Text('Add Vaccination'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: const Color(0xFF2E7D32),
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        )
                      : RefreshIndicator(
                          onRefresh: _loadData,
                          child: ListView.builder(
                            padding: const EdgeInsets.only(top: 8, bottom: 88),
                            itemCount: filteredVaccinations.length,
                            itemBuilder: (context, index) {
                              final vaccination = filteredVaccinations[index];
                            return _buildVaccinationCard(vaccination);
                            },
                          ),
                        ),
                ),
              ],
            ),
    );
  }

  Widget _buildVaccinationCard(VaccinationIsar vaccination) {
    final cattle = _cattleMap[vaccination.cattleId ?? ''];
    if (cattle == null) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: InkWell(
        onTap: () {
          // Show details dialog or navigate to detail page
          _showVaccinationDetails(vaccination);
        },
        child: Padding(
          padding: const EdgeInsets.all(16),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
          children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                    cattle.name ?? 'Unknown Cattle',
                                style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.delete_outline),
                    onPressed: () => _deleteVaccination(vaccination),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Tag ID: ${cattle.tagId ?? 'N/A'}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
                  ),
                  const SizedBox(height: 8),
              Text(
                'Vaccine: ${vaccination.vaccineName ?? 'N/A'}',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 4),
              Text(
                'Manufacturer: ${vaccination.manufacturer ?? 'N/A'}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Batch: ${vaccination.batchNumber ?? 'N/A'}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Date: ${_formatDate(vaccination.date)}',
                style: TextStyle(
                  color: Colors.grey[600],
                  fontSize: 14,
                ),
              ),
              if (vaccination.nextDueDate != null) ...[
                const SizedBox(height: 4),
                Text(
                  'Next Due: ${_formatDate(vaccination.nextDueDate)}',
                  style: TextStyle(
                    color: _getStatusColor(vaccination.status),
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                    ),
                  ],
                ],
              ),
            ),
      ),
    );
  }

  void _showVaccinationDetails(VaccinationIsar vaccination) {
    final cattle = _cattleMap[vaccination.cattleId ?? ''];
    if (cattle == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Vaccination Details'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Cattle', cattle.name ?? 'Unknown'),
              _buildDetailRow('Tag ID', cattle.tagId ?? 'Unknown'),
              _buildDetailRow('Vaccine', vaccination.vaccineName ?? 'N/A'),
              _buildDetailRow('Date', _formatDate(vaccination.date)),
              _buildDetailRow('Manufacturer', vaccination.manufacturer ?? 'N/A'),
              _buildDetailRow('Batch Number', vaccination.batchNumber ?? 'N/A'),
              _buildDetailRow('Status', vaccination.status ?? 'N/A'),
              _buildDetailRow('Cost', vaccination.cost != null ? '\$${vaccination.cost!.toStringAsFixed(2)}' : 'N/A'),
              _buildDetailRow('Next Due Date', _formatDate(vaccination.nextDueDate)),
              _buildDetailRow('Notes', vaccination.notes ?? 'N/A'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _editVaccination(vaccination);
            },
            child: const Text('Edit'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteVaccination(vaccination);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  void _editVaccination(VaccinationIsar vaccination) {
    final cattle = _cattleMap[vaccination.cattleId ?? ''];
    if (cattle == null) return;

    showDialog(
      context: context,
      builder: (context) {
        return VaccinationFormDialog(
          cattleId: vaccination.cattleId ?? '',
          cattle: _cattleMap.values.toList(),
          vaccination: vaccination,
          onSave: (updatedVaccination) async {
            await _dbHelper.healthHandler.addOrUpdateVaccination(
              updatedVaccination.cattleId ?? '',
              updatedVaccination
            );
            _loadData();
            return true;
          },
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
      children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  Future<void> _deleteVaccination(VaccinationIsar vaccination) async {
    final id = vaccination.recordId;
    final cattleId = vaccination.cattleId;

    if (id == null || cattleId == null) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error: Vaccination ID or Cattle ID is missing'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirm Deletion'),
        content: const Text(
            'Are you sure you want to delete this vaccination record? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _dbHelper.healthHandler.deleteVaccination(id);
                  await _loadData(); // Reload data after deletion
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                      content: Text('Vaccination record deleted successfully'),
                      backgroundColor: Colors.green,
                    ),
                    );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Error deleting vaccination record: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'N/A';
    return DateFormat.yMMMd().format(date.toLocal());
  }

  Color _getStatusColor(String? status) {
    if (status == null) return Colors.grey;

    final statusLower = status.toLowerCase();
    if (statusLower.contains('completed')) {
      return Colors.green;
    } else if (statusLower.contains('due')) {
      return Colors.orange;
    } else if (statusLower.contains('overdue')) {
      return Colors.red;
    } else if (statusLower.contains('scheduled')) {
      return Colors.blue;
    }
    return Colors.purple;
  }
}
