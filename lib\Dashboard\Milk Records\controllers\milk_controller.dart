import 'package:flutter/foundation.dart';
import 'package:get_it/get_it.dart';
import 'package:uuid/uuid.dart';
import '../models/milk_record_isar.dart';
import '../models/milk_sale_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../services/milk_handler.dart';
import '../services/milk_sales_service.dart';
import '../../Cattle/services/cattle_handler.dart';


/// Controller state enum
enum ControllerState { initial, loading, loaded, error, empty }

class MilkController extends ChangeNotifier {
  final MilkHandler _milkHandler = GetIt.instance<MilkHandler>();
  final MilkSalesService _milkSalesService = MilkSalesService();
  final CattleHandler _cattleHandler = GetIt.instance<CattleHandler>();

  // State management
  ControllerState _state = ControllerState.loading;
  String? _errorMessage;

  // Data
  List<MilkRecordIsar> _milkRecords = [];
  List<MilkSaleIsar> _milkSales = [];
  List<CattleIsar> _cattle = [];

  // Business ID - Generate unique ID for this controller instance
  final String businessId = const Uuid().v4();

  // Getters
  ControllerState get state => _state;
  String? get errorMessage => _errorMessage;
  List<MilkRecordIsar> get milkRecords => List.unmodifiable(_milkRecords);
  List<MilkSaleIsar> get milkSales => List.unmodifiable(_milkSales);
  List<CattleIsar> get cattle => List.unmodifiable(_cattle);
  
  // Analytics data
  int get totalMilkRecords => _milkRecords.length;
  int get totalMilkSales => _milkSales.length;
  double get totalMilkProduced => _milkRecords.fold(0.0, (sum, record) => sum + (record.quantity ?? 0.0));
  double get totalMilkSold => _milkSales.fold(0.0, (sum, sale) => sum + sale.quantity);
  double get totalRevenue => _milkSales.fold(0.0, (sum, sale) => sum + (sale.totalAmount ?? sale.total));
  int get femaleCattle => _cattle.where((c) => c.gender?.toLowerCase() == 'female').length;
  
  double get averageDailyProduction {
    if (_milkRecords.isEmpty) return 0.0;
    
    // Group records by date and sum quantities
    final Map<String, double> dailyProduction = {};
    for (final record in _milkRecords) {
      if (record.date != null && record.quantity != null) {
        final dateKey = record.date!.toIso8601String().split('T')[0];
        dailyProduction[dateKey] = (dailyProduction[dateKey] ?? 0.0) + record.quantity!;
      }
    }
    
    if (dailyProduction.isEmpty) return 0.0;
    final totalProduction = dailyProduction.values.fold(0.0, (sum, qty) => sum + qty);
    return totalProduction / dailyProduction.length;
  }

  Map<String, double> get productionByMonth {
    final Map<String, double> monthlyProduction = {};
    for (final record in _milkRecords) {
      if (record.date != null && record.quantity != null) {
        final monthKey = '${record.date!.year}-${record.date!.month.toString().padLeft(2, '0')}';
        monthlyProduction[monthKey] = (monthlyProduction[monthKey] ?? 0.0) + record.quantity!;
      }
    }
    return monthlyProduction;
  }

  Map<String, double> get salesByMonth {
    final Map<String, double> monthlySales = {};
    for (final sale in _milkSales) {
      final monthKey = '${sale.date.year}-${sale.date.month.toString().padLeft(2, '0')}';
      monthlySales[monthKey] = (monthlySales[monthKey] ?? 0.0) + (sale.totalAmount ?? sale.total);
    }
    return monthlySales;
  }

  // Initialize and load data
  Future<void> loadData() async {
    try {
      _setState(ControllerState.loading);

      await Future.wait([
        _loadMilkRecords(),
        _loadMilkSales(),
        _loadCattle(),
      ]);

      _setState(ControllerState.loaded);
    } catch (e, stackTrace) {
      debugPrint('Error loading milk data: $e\n$stackTrace');
      _setError('Failed to load milk data: ${e.toString()}');
    }
  }

  Future<void> refresh() async {
    await loadData();
  }

  Future<void> _loadMilkRecords() async {
    try {
      final milkData = await _milkHandler.getAllMilkRecords();
      _milkRecords = milkData;
    } catch (e) {
      debugPrint('Error loading milk records: $e');
      throw Exception('Failed to load milk records');
    }
  }

  Future<void> _loadMilkSales() async {
    try {
      final salesData = await _milkSalesService.getAllMilkSales();
      _milkSales = salesData;
    } catch (e) {
      debugPrint('Error loading milk sales: $e');
      throw Exception('Failed to load milk sales');
    }
  }

  Future<void> _loadCattle() async {
    try {
      final cattleData = await _cattleHandler.getAllCattle();
      _cattle = cattleData;
    } catch (e) {
      debugPrint('Error loading cattle: $e');
      throw Exception('Failed to load cattle');
    }
  }

  // Add new milk record
  Future<void> addMilkRecord(MilkRecordIsar record) async {
    try {
      await _milkHandler.addMilkRecord(record);
      _milkRecords.add(record);
      _milkRecords.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding milk record: $e');
      throw Exception('Failed to add milk record');
    }
  }

  // Add new milk sale
  Future<void> addMilkSale(MilkSaleIsar sale) async {
    try {
      await _milkSalesService.addMilkSale(sale);
      _milkSales.add(sale);
      _milkSales.sort((a, b) => (b.date).compareTo(a.date));
      notifyListeners();
    } catch (e) {
      debugPrint('Error adding milk sale: $e');
      throw Exception('Failed to add milk sale');
    }
  }

  // Update milk record
  void updateMilkRecord(MilkRecordIsar updatedRecord) {
    final index = _milkRecords.indexWhere((r) => r.businessId == updatedRecord.businessId);
    if (index >= 0) {
      _milkRecords[index] = updatedRecord;
      _milkRecords.sort((a, b) => (b.date ?? DateTime.now()).compareTo(a.date ?? DateTime.now()));
      notifyListeners();
    }
  }

  // Update milk sale
  void updateMilkSale(MilkSaleIsar updatedSale) {
    final index = _milkSales.indexWhere((s) => s.businessId == updatedSale.businessId);
    if (index >= 0) {
      _milkSales[index] = updatedSale;
      _milkSales.sort((a, b) => b.date.compareTo(a.date));
      notifyListeners();
    }
  }

  // Helper methods
  CattleIsar? getCattle(String? cattleBusinessId) {
    if (cattleBusinessId == null) return null;
    try {
      return _cattle.firstWhere(
        (cattle) => cattle.businessId == cattleBusinessId,
      );
    } catch (e) {
      return null;
    }
  }

  String getCattleName(String? cattleBusinessId) {
    final cattle = getCattle(cattleBusinessId);
    return cattle?.name ?? 'Unknown Cattle';
  }

  List<CattleIsar> get femaleCattleList {
    return _cattle.where((c) => c.gender?.toLowerCase() == 'female').toList();
  }

  // Filter methods
  List<MilkRecordIsar> getMilkRecordsByCattle(String cattleBusinessId) {
    return _milkRecords.where((r) => r.cattleBusinessId == cattleBusinessId).toList();
  }

  List<MilkRecordIsar> getMilkRecordsByDateRange(DateTime start, DateTime end) {
    return _milkRecords.where((r) {
      if (r.date == null) return false;
      return r.date!.isAfter(start.subtract(const Duration(days: 1))) && 
             r.date!.isBefore(end.add(const Duration(days: 1)));
    }).toList();
  }

  // State management helpers
  void _setState(ControllerState newState) {
    _state = newState;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _state = ControllerState.error;
    _errorMessage = error;
    notifyListeners();
  }
}
