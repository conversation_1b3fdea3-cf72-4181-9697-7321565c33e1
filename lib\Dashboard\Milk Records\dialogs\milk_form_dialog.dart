import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../services/milk_handler.dart';
import '../../Cattle/services/cattle_handler.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../models/milk_record_isar.dart';
import '../../../utils/message_utils.dart';
import 'package:logging/logging.dart';

class MilkFormDialog extends StatefulWidget {
  final MilkRecordIsar? record;
  final String? cattleTagId;

  const MilkFormDialog({
    Key? key,
    this.record,
    this.cattleTagId,
  }) : super(key: key);

  @override
  MilkFormDialogState createState() => MilkFormDialogState();
}

class MilkFormDialogState extends State<MilkFormDialog> {
  static const _inputDecorationConstraints = BoxConstraints(maxHeight: 56);
  static const _inputContentPadding =
      EdgeInsets.symmetric(horizontal: 12, vertical: 8);
  static const _animationDuration = Duration(milliseconds: 200);

  final _formKey = GlobalKey<FormState>();
  late final MilkHandler _milkHandler = MilkHandler.instance;
  late final CattleHandler _cattleHandler = CattleHandler.instance;
  late final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;
  final _scrollController = ScrollController();
  final _logger = Logger('MilkFormDialog');

  final TextEditingController _morningYieldController = TextEditingController();
  final TextEditingController _eveningYieldController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();

  DateTime _selectedDate = DateTime.now();
  CattleIsar? _selectedCattle;
  List<CattleIsar> _cattleList = [];
  Map<String, AnimalTypeIsar> _animalTypes = {};
  bool _isLoading = true;
  final bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _loadData();
    if (widget.record != null) {
      _selectedDate = widget.record!.date ?? DateTime.now();
      _morningYieldController.text = widget.record!.morningAmount?.toString() ?? "0.0";
      _eveningYieldController.text = widget.record!.eveningAmount?.toString() ?? "0.0";
      _notesController.text = widget.record!.notes ?? '';
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _morningYieldController.dispose();
    _eveningYieldController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      final cattleData = await _cattleHandler.getAllCattle();
      final typesData = await _farmSetupHandler.getAllAnimalTypes();

      // Use cattleData directly as it's already the correct type
      final cattle = cattleData;
      final types = typesData;

      // Create a list to store female cattle
      List<CattleIsar> femaleCattle = [];

      // Filter to show only female cattle
      for (var c in cattle) {
        if (c.gender != null && c.gender!.toLowerCase() == 'female') {
          // Check if status is explicitly set to lactating
          if (c.status?.toLowerCase() == 'lactating') {
            femaleCattle.add(c);
          }
        }
      }

      if (mounted) {
        setState(() {
          _cattleList = femaleCattle;
          _animalTypes = {for (var type in types) type.businessId ?? '': type};

          if (_cattleList.isEmpty) {
            _selectedCattle = null;
            _isLoading = false;
            return;
          }

          if (widget.cattleTagId != null) {
            _selectedCattle = _cattleList.firstWhere(
              (c) => c.tagId == widget.cattleTagId,
              orElse: () => _cattleList.first,
            );
          } else if (widget.record != null) {
            _selectedCattle = _cattleList.firstWhere(
              (c) => c.tagId == widget.record!.cattleTagId,
              orElse: () => _cattleList.first,
            );
          } else {
            _selectedCattle = _cattleList.first;
          }

          _isLoading = false;
        });
      }
    } catch (e, stackTrace) {
      _logger.severe('Error loading data', e, stackTrace);
      if (mounted) {
        setState(() => _isLoading = false);
        MilkMessageUtils.showError(context, 'Error loading data: $e');
      }
    }
  }

  Future<void> _saveMilkRecord() async {
    if (_formKey.currentState?.validate() != true) return;
    if (_selectedCattle == null) return;

    try {
      final morning = double.tryParse(_morningYieldController.text) ?? 0.0;
      final evening = double.tryParse(_eveningYieldController.text) ?? 0.0;
      final total = morning + evening;

      // Create a new milk record
      final record = MilkRecordIsar()
        ..cattleBusinessId = _selectedCattle!.businessId
        ..cattleTagId = _selectedCattle!.tagId
        ..date = _selectedDate
        ..morningAmount = morning
        ..eveningAmount = evening
        ..totalAmount = total
        ..notes = _notesController.text;

      // If editing, preserve the original ID
      if (widget.record != null) {
        record.id = widget.record!.id;
        record.businessId = widget.record!.businessId;
      }

      // Save the record and get the updated record with ID
      final savedRecord = await _milkHandler.updateMilkRecord(record);

      if (mounted) {
        // Return the saved record instead of just true
        Navigator.of(context).pop(savedRecord);
      }
    } catch (e, stackTrace) {
      _logger.severe('Error saving milk record', e, stackTrace);
      if (mounted) {
        MilkMessageUtils.showError(context, 'Error saving milk record: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: AnimatedContainer(
        duration: _animationDuration,
        curve: Curves.easeInOut,
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        decoration: BoxDecoration(
          color: Theme.of(context).dialogTheme.backgroundColor ?? Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: _isLoading
            ? const SizedBox(
                height: 200,
                child: Center(child: CircularProgressIndicator()),
              )
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Header
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.blue.withAlpha(26),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(16),
                        topRight: Radius.circular(16),
                      ),
                    ),
                    child: Row(
                      children: [
                        CircleAvatar(
                          radius: 20,
                          backgroundColor: Colors.blue.withAlpha(50),
                          child: const Icon(
                            Icons.water_drop,
                            color: Colors.blue,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          widget.record == null
                              ? 'Add Milk Record'
                              : 'Edit Milk Record',
                          style:
                              Theme.of(context).textTheme.titleLarge?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                        ),
                      ],
                    ),
                  ),
                  // Form Content
                  Flexible(
                    child: SingleChildScrollView(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            // Cattle Selection
                            DropdownButtonFormField<CattleIsar>(
                              decoration: InputDecoration(
                                labelText: 'Select Cattle',
                                border: const OutlineInputBorder(),
                                constraints: _inputDecorationConstraints,
                                contentPadding: _inputContentPadding,
                                prefixIcon: Icon(
                                  Icons.pets,
                                  color: Colors.brown.shade400,
                                ),
                              ),
                              value: _selectedCattle,
                              items: _cattleList.map((cattle) {
                                final animalType =
                                    _animalTypes[cattle.animalTypeId];
                                return DropdownMenuItem<CattleIsar>(
                                  value: cattle,
                                  child: Text(
                                      '${cattle.name} (${animalType?.name ?? "Unknown"}: ${cattle.tagId})'),
                                );
                              }).toList(),
                              onChanged: (CattleIsar? value) {
                                setState(() {
                                  _selectedCattle = value;
                                });
                              },
                              validator: (value) {
                                if (value == null) {
                                  return 'Please select a cattle';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            // Date Selection
                            InkWell(
                              onTap: () async {
                                final picked = await showDatePicker(
                                  context: context,
                                  initialDate: _selectedDate,
                                  firstDate: DateTime(2020),
                                  lastDate: DateTime.now(),
                                );
                                if (picked != null) {
                                  setState(() {
                                    _selectedDate = picked;
                                  });
                                }
                              },
                              child: InputDecorator(
                                decoration: const InputDecoration(
                                  labelText: 'Date',
                                  border: OutlineInputBorder(),
                                  prefixIcon: Icon(
                                    Icons.calendar_today,
                                    color: Colors.blue,
                                  ),
                                  constraints: _inputDecorationConstraints,
                                  contentPadding: _inputContentPadding,
                                ),
                                child: Text(
                                  DateFormat('MMMM dd, yyyy')
                                      .format(_selectedDate),
                                  style: const TextStyle(fontSize: 16),
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),
                            // Morning Yield
                            TextFormField(
                              controller: _morningYieldController,
                              decoration: const InputDecoration(
                                labelText: 'Morning Yield (L)',
                                border: OutlineInputBorder(),
                                constraints: _inputDecorationConstraints,
                                contentPadding: _inputContentPadding,
                                prefixIcon:
                                    Icon(Icons.wb_sunny, color: Colors.orange),
                              ),
                              keyboardType:
                                  const TextInputType.numberWithOptions(
                                      decimal: true),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return null;
                                }
                                if (double.tryParse(value) == null) {
                                  return 'Please enter a valid number';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            // Evening Yield
                            TextFormField(
                              controller: _eveningYieldController,
                              decoration: const InputDecoration(
                                labelText: 'Evening Yield (L)',
                                border: OutlineInputBorder(),
                                constraints: _inputDecorationConstraints,
                                contentPadding: _inputContentPadding,
                                prefixIcon: Icon(Icons.nights_stay,
                                    color: Colors.indigo),
                              ),
                              keyboardType:
                                  const TextInputType.numberWithOptions(
                                      decimal: true),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return null;
                                }
                                if (double.tryParse(value) == null) {
                                  return 'Please enter a valid number';
                                }
                                return null;
                              },
                            ),
                            const SizedBox(height: 16),
                            // Notes
                            TextFormField(
                              controller: _notesController,
                              decoration: const InputDecoration(
                                labelText: 'Notes',
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(
                                    horizontal: 12, vertical: 12),
                                prefixIcon:
                                    Icon(Icons.note, color: Colors.grey),
                              ),
                              maxLines: 3,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  // Action Buttons
                  Container(
                    padding: const EdgeInsets.all(16),
                    child: Row(
                      children: [
                        Expanded(
                          child: TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: const Text('Cancel'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isSaving ? null : _saveMilkRecord,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFF2E7D32),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: Text(
                              _isSaving
                                  ? 'Saving...'
                                  : (widget.record == null ? 'Add' : 'Update'),
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
      ),
    );
  }
}
