import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../models/milk_sale_isar.dart';
import '../services/milk_sales_service.dart';
import 'package:keyboard_dismisser/keyboard_dismisser.dart';
import '../services/milk_service.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';

class MilkSaleEntryDialog extends StatefulWidget {
  final DateTime selectedDate;
  final double availableMilk;

  const MilkSaleEntryDialog({
    super.key,
    required this.selectedDate,
    required this.availableMilk,
  });

  @override
  State<MilkSaleEntryDialog> createState() => _MilkSaleEntryDialogState();
}

class _MilkSaleEntryDialogState extends State<MilkSaleEntryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _milkSalesService = MilkSalesService();
  final _milkService = MilkService();
  final _farmSetupHandler = FarmSetupHandler.instance;

  final _quantityController = TextEditingController();
  final _rateController = TextEditingController();
  final _calfUsageController = TextEditingController();
  final _homeUsageController = TextEditingController();
  final _notesController = TextEditingController();
  bool _isPaid = true;
  double _totalAmount = 0;
  double _remainingMilk = 0;
  DateTime _selectedDate = DateTime.now();
  double _availableMilk = 0;

  // Currency settings
  String _currencySymbol = '₹';
  bool _symbolBeforeAmount = true;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate;
    _availableMilk = widget.availableMilk;
    _loadCurrencySettings();
    _remainingMilk = _availableMilk;
    _rateController.text = '60.0'; // Default rate
    _updateTotalAmount();

    // Add listeners
    _quantityController.addListener(_updateRemainingMilk);
    _calfUsageController.addListener(_updateRemainingMilk);
    _homeUsageController.addListener(_updateRemainingMilk);
    _rateController.addListener(_updateTotalAmount);
    _quantityController.addListener(_updateTotalAmount);
  }

  Future<void> _loadCurrencySettings() async {
    try {
      final currencySettings = await _farmSetupHandler.getCurrencySettings();
      if (mounted) {
        setState(() {
          _currencySymbol = currencySettings.currencySymbol;
          _symbolBeforeAmount = currencySettings.symbolBeforeAmount;
        });
      }
    } catch (e) {
      // Use default values if loading fails
      if (mounted) {
        setState(() {
          _currencySymbol = '\$';
          _symbolBeforeAmount = true;
        });
      }
    }
  }

  Future<void> _loadAvailableMilk(DateTime date) async {
    try {
      // Get milk records for the selected date
      final records = await _milkService.getMilkRecordsForDate(date);

      // Calculate total production
      double totalProduction = records.fold<double>(
          0.0,
          (sum, record) =>
              sum + (record.morningAmount ?? 0) + (record.eveningAmount ?? 0));

      // Get sales for the selected date

      // Calculate available milk (total - all usage)

      if (mounted) {
        setState(() {
          _availableMilk = totalProduction; // Update the total production
          _updateRemainingMilk();
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading milk data: $e')),
        );
      }
    }
  }

  void _updateTotalAmount() {
    final quantity = double.tryParse(_quantityController.text) ?? 0;
    final rate = double.tryParse(_rateController.text) ?? 0;
    setState(() {
      _totalAmount = quantity * rate;
    });
  }

  void _updateRemainingMilk() {
    final calfUsage = double.tryParse(_calfUsageController.text) ?? 0;
    final homeUsage = double.tryParse(_homeUsageController.text) ?? 0;
    final soldQuantity = double.tryParse(_quantityController.text) ?? 0;

    final availableForSale = _availableMilk - calfUsage - homeUsage;
    setState(() {
      _remainingMilk = availableForSale - soldQuantity;
    });
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Color(0xFF2E7D32),
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      setState(() {
        _selectedDate = picked;
      });
      await _loadAvailableMilk(_selectedDate);
    }
  }

  Future<void> _saveMilkSale() async {
    if (!_formKey.currentState!.validate()) return;

    final sale = MilkSaleIsar()
      ..date = _selectedDate
      ..buyer = "Default" // Changed from buyerName to buyer
      ..quantity = double.parse(_quantityController.text) // Changed from quantitySold to quantity
      ..price = double.parse(_rateController.text) // Changed from ratePerLiter to price
      ..total = _totalAmount
      ..paymentStatus = _isPaid ? 'Paid' : 'Pending' // Store payment status as string
      ..notes = _notesController.text.trim();

    await _milkSalesService.addMilkSale(sale);
    if (mounted) {
      Navigator.of(context).pop(true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.8,
          maxWidth: MediaQuery.of(context).size.width * 0.9,
        ),
        child: KeyboardDismisser(
          gestures: const [
            GestureType.onTap,
            GestureType.onPanUpdateDownDirection
          ],
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Form(
              key: _formKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Milk Sale Entry',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: const Color(0xFF2E7D32),
                            ),
                      ),
                      IconButton(
                        icon: const Icon(Icons.close, size: 20),
                        onPressed: () => Navigator.of(context).pop(),
                        padding: EdgeInsets.zero,
                        constraints: const BoxConstraints(),
                      ),
                    ],
                  ),
                  Divider(color: Colors.grey.shade300),
                  const SizedBox(height: 12),

                  // Date Picker
                  InkWell(
                    onTap: () => _selectDate(context),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 12, vertical: 10),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Date: ${DateFormat('dd MMM yyyy').format(_selectedDate)}',
                            style: const TextStyle(fontSize: 14),
                          ),
                          const Icon(Icons.calendar_today,
                              size: 18, color: Color(0xFF2E7D32)),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Production info and usage inputs
                  Row(
                    children: [
                      Expanded(
                        child: _infoCard(
                          'Total Production',
                          '${_availableMilk.toStringAsFixed(1)} L',
                          Colors.green,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _infoCard(
                          'Available',
                          '${_remainingMilk.toStringAsFixed(1)} L',
                          _remainingMilk < 0 ? Colors.red : Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Usage inputs
                  Row(
                    children: [
                      Expanded(
                        child: _buildCompactInput(
                          label: 'Calves Usage (L)',
                          controller: _calfUsageController,
                          hint: '0.0',
                          validator: _validateUsage,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildCompactInput(
                          label: 'Home Usage (L)',
                          controller: _homeUsageController,
                          hint: '0.0',
                          validator: _validateUsage,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Sale details
                  Row(
                    children: [
                      Expanded(
                        child: _buildCompactInput(
                          label: 'Quantity (L)',
                          controller: _quantityController,
                          hint: '0.0',
                          validator: _validateQuantity,
                          onActionPressed: _autoFillQuantity,
                          actionIcon: Icons.auto_fix_high,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildCompactInput(
                          label: 'Rate ($_currencySymbol)',
                          controller: _rateController,
                          hint: '60.0',
                          prefix: '$_currencySymbol ',
                          validator: _validateRate,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),

                  // Total amount
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: Colors.green.shade50,
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(color: Colors.green.shade100),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        const Text('Total Amount:'),
                        Text(
                          _formatCurrency(_totalAmount),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF2E7D32),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 12),

                  // Payment status
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const Text('Payment:'),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ToggleButtons(
                          isSelected: [_isPaid, !_isPaid],
                          onPressed: (index) =>
                              setState(() => _isPaid = index == 0),
                          borderRadius: BorderRadius.circular(8),
                          selectedColor: Colors.white,
                          fillColor: Colors.blue.shade600,
                          constraints: const BoxConstraints(
                            minHeight: 40,
                            minWidth: 80,
                          ),
                          renderBorder: false,
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(Icons.check_circle,
                                      size: 16,
                                      color:
                                          _isPaid ? Colors.white : Colors.grey),
                                  const SizedBox(width: 4),
                                  Text('Paid',
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: _isPaid
                                            ? Colors.white
                                            : Colors.grey.shade600,
                                      )),
                                ],
                              ),
                            ),
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 8),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Icon(Icons.pending,
                                      size: 16,
                                      color: !_isPaid
                                          ? Colors.white
                                          : Colors.grey),
                                  const SizedBox(width: 4),
                                  Text('Pending',
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: !_isPaid
                                            ? Colors.white
                                            : Colors.grey.shade600,
                                      )),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),

                  // Notes (optional)
                  TextFormField(
                    controller: _notesController,
                    decoration: InputDecoration(
                      labelText: 'Notes (Optional)',
                      hintText: 'Enter any additional information...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                      contentPadding: const EdgeInsets.all(8),
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),

                  // Submit button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _saveMilkSale,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2E7D32),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text('SAVE'),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _infoCard(String label, String value, MaterialColor color) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.shade50,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color.shade100),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: TextStyle(fontSize: 12, color: color.shade700)),
          const SizedBox(height: 2),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color.shade800,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactInput({
    required String label,
    required TextEditingController controller,
    required String hint,
    String? prefix,
    required String? Function(String?) validator,
    VoidCallback? onActionPressed,
    IconData? actionIcon,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: const TextStyle(fontSize: 12)),
        const SizedBox(height: 4),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: controller,
                decoration: InputDecoration(
                  hintText: hint,
                  prefixText: prefix,
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(4)),
                  isDense: true,
                ),
                keyboardType:
                    const TextInputType.numberWithOptions(decimal: true),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                validator: validator,
              ),
            ),
            if (onActionPressed != null && actionIcon != null) ...[
              const SizedBox(width: 4),
              InkWell(
                onTap: onActionPressed,
                child: Container(
                  height: 40,
                  width: 36,
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Icon(
                    actionIcon,
                    size: 18,
                    color: Colors.blue.shade700,
                  ),
                ),
              ),
            ],
          ],
        ),
      ],
    );
  }

  String? _validateUsage(String? value) {
    if (value == null || value.isEmpty) return null;
    final usage = double.tryParse(value);
    if (usage == null || usage < 0 || usage > _availableMilk) {
      return 'Invalid';
    }
    return null;
  }

  String? _validateQuantity(String? value) {
    if (value == null || value.isEmpty) {
      return 'Required';
    }
    final quantity = double.tryParse(value);
    final calfUsage = double.tryParse(_calfUsageController.text) ?? 0;
    final homeUsage = double.tryParse(_homeUsageController.text) ?? 0;
    final availableForSale = _availableMilk - calfUsage - homeUsage;

    if (quantity == null || quantity <= 0) {
      return 'Invalid';
    }
    if (quantity > availableForSale) {
      return 'Exceeds available';
    }
    return null;
  }

  String? _validateRate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Required';
    }
    final rate = double.tryParse(value);
    if (rate == null || rate <= 0) {
      return 'Invalid';
    }
    return null;
  }

  // Format currency based on user settings
  String _formatCurrency(double amount) {
    return _symbolBeforeAmount
        ? '$_currencySymbol ${amount.toStringAsFixed(2)}'
        : '${amount.toStringAsFixed(2)} $_currencySymbol';
  }

  // Auto-fill available milk quantity
  void _autoFillQuantity() {
    final calfUsage = double.tryParse(_calfUsageController.text) ?? 0;
    final homeUsage = double.tryParse(_homeUsageController.text) ?? 0;
    final availableForSale = _availableMilk - calfUsage - homeUsage;

    setState(() {
      _quantityController.text =
          availableForSale > 0 ? availableForSale.toStringAsFixed(1) : '0.0';
    });
  }

  @override
  void dispose() {
    _quantityController.dispose();
    _rateController.dispose();
    _calfUsageController.dispose();
    _homeUsageController.dispose();
    _notesController.dispose();
    super.dispose();
  }
}
