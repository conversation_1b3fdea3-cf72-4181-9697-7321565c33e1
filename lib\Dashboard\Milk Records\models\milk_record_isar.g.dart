// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'milk_record_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetMilkRecordIsarCollection on Isar {
  IsarCollection<MilkRecordIsar> get milkRecordIsars => this.collection();
}

const MilkRecordIsarSchema = CollectionSchema(
  name: r'MilkRecordIsar',
  id: -9026644130462430515,
  properties: {
    r'afternoon': PropertySchema(
      id: 0,
      name: r'afternoon',
      type: IsarType.double,
    ),
    r'afternoonAmount': PropertySchema(
      id: 1,
      name: r'afternoonAmount',
      type: IsarType.double,
    ),
    r'businessId': PropertySchema(
      id: 2,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'cattleBusinessId': PropertySchema(
      id: 3,
      name: r'cattleBusinessId',
      type: IsarType.string,
    ),
    r'cattleId': PropertySchema(
      id: 4,
      name: r'cattleId',
      type: IsarType.string,
    ),
    r'cattleTagId': PropertySchema(
      id: 5,
      name: r'cattleTagId',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 6,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'date': PropertySchema(
      id: 7,
      name: r'date',
      type: IsarType.dateTime,
    ),
    r'evening': PropertySchema(
      id: 8,
      name: r'evening',
      type: IsarType.double,
    ),
    r'eveningAmount': PropertySchema(
      id: 9,
      name: r'eveningAmount',
      type: IsarType.double,
    ),
    r'eveningYield': PropertySchema(
      id: 10,
      name: r'eveningYield',
      type: IsarType.double,
    ),
    r'fatContent': PropertySchema(
      id: 11,
      name: r'fatContent',
      type: IsarType.double,
    ),
    r'morning': PropertySchema(
      id: 12,
      name: r'morning',
      type: IsarType.double,
    ),
    r'morningAmount': PropertySchema(
      id: 13,
      name: r'morningAmount',
      type: IsarType.double,
    ),
    r'morningYield': PropertySchema(
      id: 14,
      name: r'morningYield',
      type: IsarType.double,
    ),
    r'notes': PropertySchema(
      id: 15,
      name: r'notes',
      type: IsarType.string,
    ),
    r'pricePerLiter': PropertySchema(
      id: 16,
      name: r'pricePerLiter',
      type: IsarType.double,
    ),
    r'recordId': PropertySchema(
      id: 17,
      name: r'recordId',
      type: IsarType.string,
    ),
    r'recordedAt': PropertySchema(
      id: 18,
      name: r'recordedAt',
      type: IsarType.dateTime,
    ),
    r'tagId': PropertySchema(
      id: 19,
      name: r'tagId',
      type: IsarType.string,
    ),
    r'totalAmount': PropertySchema(
      id: 20,
      name: r'totalAmount',
      type: IsarType.double,
    ),
    r'totalYield': PropertySchema(
      id: 21,
      name: r'totalYield',
      type: IsarType.double,
    ),
    r'updatedAt': PropertySchema(
      id: 22,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _milkRecordIsarEstimateSize,
  serialize: _milkRecordIsarSerialize,
  deserialize: _milkRecordIsarDeserialize,
  deserializeProp: _milkRecordIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'cattleBusinessId': IndexSchema(
      id: -1530790847330223488,
      name: r'cattleBusinessId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'cattleBusinessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'cattleTagId': IndexSchema(
      id: -2283963072638323009,
      name: r'cattleTagId',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'cattleTagId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'date': IndexSchema(
      id: -7552997827385218417,
      name: r'date',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'date',
          type: IndexType.value,
          caseSensitive: false,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _milkRecordIsarGetId,
  getLinks: _milkRecordIsarGetLinks,
  attach: _milkRecordIsarAttach,
  version: '3.1.0+1',
);

int _milkRecordIsarEstimateSize(
  MilkRecordIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleBusinessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.cattleTagId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.notes;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.recordId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.tagId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _milkRecordIsarSerialize(
  MilkRecordIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDouble(offsets[0], object.afternoon);
  writer.writeDouble(offsets[1], object.afternoonAmount);
  writer.writeString(offsets[2], object.businessId);
  writer.writeString(offsets[3], object.cattleBusinessId);
  writer.writeString(offsets[4], object.cattleId);
  writer.writeString(offsets[5], object.cattleTagId);
  writer.writeDateTime(offsets[6], object.createdAt);
  writer.writeDateTime(offsets[7], object.date);
  writer.writeDouble(offsets[8], object.evening);
  writer.writeDouble(offsets[9], object.eveningAmount);
  writer.writeDouble(offsets[10], object.eveningYield);
  writer.writeDouble(offsets[11], object.fatContent);
  writer.writeDouble(offsets[12], object.morning);
  writer.writeDouble(offsets[13], object.morningAmount);
  writer.writeDouble(offsets[14], object.morningYield);
  writer.writeString(offsets[15], object.notes);
  writer.writeDouble(offsets[16], object.pricePerLiter);
  writer.writeString(offsets[17], object.recordId);
  writer.writeDateTime(offsets[18], object.recordedAt);
  writer.writeString(offsets[19], object.tagId);
  writer.writeDouble(offsets[20], object.totalAmount);
  writer.writeDouble(offsets[21], object.totalYield);
  writer.writeDateTime(offsets[22], object.updatedAt);
}

MilkRecordIsar _milkRecordIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = MilkRecordIsar();
  object.afternoon = reader.readDoubleOrNull(offsets[0]);
  object.afternoonAmount = reader.readDoubleOrNull(offsets[1]);
  object.businessId = reader.readStringOrNull(offsets[2]);
  object.cattleBusinessId = reader.readStringOrNull(offsets[3]);
  object.cattleId = reader.readStringOrNull(offsets[4]);
  object.cattleTagId = reader.readStringOrNull(offsets[5]);
  object.createdAt = reader.readDateTimeOrNull(offsets[6]);
  object.date = reader.readDateTimeOrNull(offsets[7]);
  object.evening = reader.readDoubleOrNull(offsets[8]);
  object.eveningAmount = reader.readDoubleOrNull(offsets[9]);
  object.eveningYield = reader.readDoubleOrNull(offsets[10]);
  object.fatContent = reader.readDoubleOrNull(offsets[11]);
  object.id = id;
  object.morning = reader.readDoubleOrNull(offsets[12]);
  object.morningAmount = reader.readDoubleOrNull(offsets[13]);
  object.morningYield = reader.readDoubleOrNull(offsets[14]);
  object.notes = reader.readStringOrNull(offsets[15]);
  object.pricePerLiter = reader.readDoubleOrNull(offsets[16]);
  object.recordId = reader.readStringOrNull(offsets[17]);
  object.recordedAt = reader.readDateTimeOrNull(offsets[18]);
  object.tagId = reader.readStringOrNull(offsets[19]);
  object.totalAmount = reader.readDoubleOrNull(offsets[20]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[22]);
  return object;
}

P _milkRecordIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readDoubleOrNull(offset)) as P;
    case 1:
      return (reader.readDoubleOrNull(offset)) as P;
    case 2:
      return (reader.readStringOrNull(offset)) as P;
    case 3:
      return (reader.readStringOrNull(offset)) as P;
    case 4:
      return (reader.readStringOrNull(offset)) as P;
    case 5:
      return (reader.readStringOrNull(offset)) as P;
    case 6:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 7:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 8:
      return (reader.readDoubleOrNull(offset)) as P;
    case 9:
      return (reader.readDoubleOrNull(offset)) as P;
    case 10:
      return (reader.readDoubleOrNull(offset)) as P;
    case 11:
      return (reader.readDoubleOrNull(offset)) as P;
    case 12:
      return (reader.readDoubleOrNull(offset)) as P;
    case 13:
      return (reader.readDoubleOrNull(offset)) as P;
    case 14:
      return (reader.readDoubleOrNull(offset)) as P;
    case 15:
      return (reader.readStringOrNull(offset)) as P;
    case 16:
      return (reader.readDoubleOrNull(offset)) as P;
    case 17:
      return (reader.readStringOrNull(offset)) as P;
    case 18:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 19:
      return (reader.readStringOrNull(offset)) as P;
    case 20:
      return (reader.readDoubleOrNull(offset)) as P;
    case 21:
      return (reader.readDouble(offset)) as P;
    case 22:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _milkRecordIsarGetId(MilkRecordIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _milkRecordIsarGetLinks(MilkRecordIsar object) {
  return [];
}

void _milkRecordIsarAttach(
    IsarCollection<dynamic> col, Id id, MilkRecordIsar object) {
  object.id = id;
}

extension MilkRecordIsarByIndex on IsarCollection<MilkRecordIsar> {
  Future<MilkRecordIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  MilkRecordIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<MilkRecordIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<MilkRecordIsar?> getAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(MilkRecordIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(MilkRecordIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<MilkRecordIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<MilkRecordIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension MilkRecordIsarQueryWhereSort
    on QueryBuilder<MilkRecordIsar, MilkRecordIsar, QWhere> {
  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhere> anyDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        const IndexWhereClause.any(indexName: r'date'),
      );
    });
  }
}

extension MilkRecordIsarQueryWhere
    on QueryBuilder<MilkRecordIsar, MilkRecordIsar, QWhereClause> {
  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause> idEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause> idNotEqualTo(
      Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause> idGreaterThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause> idLessThan(
      Id id,
      {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      cattleBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleBusinessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      cattleBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cattleBusinessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      cattleBusinessIdEqualTo(String? cattleBusinessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleBusinessId',
        value: [cattleBusinessId],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      cattleBusinessIdNotEqualTo(String? cattleBusinessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleBusinessId',
              lower: [],
              upper: [cattleBusinessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleBusinessId',
              lower: [cattleBusinessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleBusinessId',
              lower: [cattleBusinessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleBusinessId',
              lower: [],
              upper: [cattleBusinessId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      cattleTagIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleTagId',
        value: [null],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      cattleTagIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'cattleTagId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      cattleTagIdEqualTo(String? cattleTagId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'cattleTagId',
        value: [cattleTagId],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      cattleTagIdNotEqualTo(String? cattleTagId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [],
              upper: [cattleTagId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [cattleTagId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [cattleTagId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'cattleTagId',
              lower: [],
              upper: [cattleTagId],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause> dateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'date',
        value: [null],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      dateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'date',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause> dateEqualTo(
      DateTime? date) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'date',
        value: [date],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      dateNotEqualTo(DateTime? date) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'date',
              lower: [],
              upper: [date],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'date',
              lower: [date],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'date',
              lower: [date],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'date',
              lower: [],
              upper: [date],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause>
      dateGreaterThan(
    DateTime? date, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'date',
        lower: [date],
        includeLower: include,
        upper: [],
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause> dateLessThan(
    DateTime? date, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'date',
        lower: [],
        upper: [date],
        includeUpper: include,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterWhereClause> dateBetween(
    DateTime? lowerDate,
    DateTime? upperDate, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'date',
        lower: [lowerDate],
        includeLower: includeLower,
        upper: [upperDate],
        includeUpper: includeUpper,
      ));
    });
  }
}

extension MilkRecordIsarQueryFilter
    on QueryBuilder<MilkRecordIsar, MilkRecordIsar, QFilterCondition> {
  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      afternoonIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'afternoon',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      afternoonIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'afternoon',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      afternoonEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'afternoon',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      afternoonGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'afternoon',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      afternoonLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'afternoon',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      afternoonBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'afternoon',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      afternoonAmountIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'afternoonAmount',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      afternoonAmountIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'afternoonAmount',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      afternoonAmountEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'afternoonAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      afternoonAmountGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'afternoonAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      afternoonAmountLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'afternoonAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      afternoonAmountBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'afternoonAmount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cattleBusinessId',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cattleBusinessId',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleBusinessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleBusinessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleBusinessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleBusinessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cattleBusinessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleBusinessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleBusinessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleBusinessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cattleBusinessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleBusinessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cattleBusinessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleBusinessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cattleBusinessId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cattleId',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cattleId',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cattleId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cattleId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cattleId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cattleId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cattleId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cattleId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cattleId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cattleId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleTagIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cattleTagId',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleTagIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cattleTagId',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleTagIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleTagIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleTagIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleTagIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cattleTagId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleTagIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleTagIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleTagIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'cattleTagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleTagIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'cattleTagId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleTagIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cattleTagId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      cattleTagIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'cattleTagId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      dateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      dateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'date',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      dateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      dateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      dateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'date',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      dateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'date',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'evening',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'evening',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'evening',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'evening',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'evening',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'evening',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningAmountIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'eveningAmount',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningAmountIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'eveningAmount',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningAmountEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'eveningAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningAmountGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'eveningAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningAmountLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'eveningAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningAmountBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'eveningAmount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningYieldIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'eveningYield',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningYieldIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'eveningYield',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningYieldEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'eveningYield',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningYieldGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'eveningYield',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningYieldLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'eveningYield',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      eveningYieldBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'eveningYield',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      fatContentIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'fatContent',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      fatContentIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'fatContent',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      fatContentEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'fatContent',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      fatContentGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'fatContent',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      fatContentLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'fatContent',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      fatContentBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'fatContent',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition> idEqualTo(
      Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'morning',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'morning',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'morning',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'morning',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'morning',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'morning',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningAmountIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'morningAmount',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningAmountIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'morningAmount',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningAmountEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'morningAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningAmountGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'morningAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningAmountLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'morningAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningAmountBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'morningAmount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningYieldIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'morningYield',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningYieldIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'morningYield',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningYieldEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'morningYield',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningYieldGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'morningYield',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningYieldLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'morningYield',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      morningYieldBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'morningYield',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      notesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      notesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'notes',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      notesEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      notesGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      notesLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      notesBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'notes',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      notesStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      notesEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      notesContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'notes',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      notesMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'notes',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      notesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      notesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'notes',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      pricePerLiterIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'pricePerLiter',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      pricePerLiterIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'pricePerLiter',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      pricePerLiterEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'pricePerLiter',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      pricePerLiterGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'pricePerLiter',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      pricePerLiterLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'pricePerLiter',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      pricePerLiterBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'pricePerLiter',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'recordId',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'recordId',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'recordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'recordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'recordId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'recordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'recordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'recordId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'recordId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recordId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'recordId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'recordedAt',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'recordedAt',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'recordedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'recordedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'recordedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      recordedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'recordedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      tagIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'tagId',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      tagIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'tagId',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      tagIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'tagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      tagIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'tagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      tagIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'tagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      tagIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'tagId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      tagIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'tagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      tagIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'tagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      tagIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'tagId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      tagIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'tagId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      tagIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'tagId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      tagIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'tagId',
        value: '',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      totalAmountIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalAmount',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      totalAmountIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalAmount',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      totalAmountEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      totalAmountGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      totalAmountLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      totalAmountBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalAmount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      totalYieldEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalYield',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      totalYieldGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalYield',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      totalYieldLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalYield',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      totalYieldBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalYield',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterFilterCondition>
      updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension MilkRecordIsarQueryObject
    on QueryBuilder<MilkRecordIsar, MilkRecordIsar, QFilterCondition> {}

extension MilkRecordIsarQueryLinks
    on QueryBuilder<MilkRecordIsar, MilkRecordIsar, QFilterCondition> {}

extension MilkRecordIsarQuerySortBy
    on QueryBuilder<MilkRecordIsar, MilkRecordIsar, QSortBy> {
  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByAfternoon() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'afternoon', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByAfternoonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'afternoon', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByAfternoonAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'afternoonAmount', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByAfternoonAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'afternoonAmount', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByCattleBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByCattleBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByCattleId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleId', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByCattleIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleId', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByCattleTagId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByCattleTagIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByEvening() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'evening', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByEveningDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'evening', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByEveningAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eveningAmount', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByEveningAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eveningAmount', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByEveningYield() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eveningYield', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByEveningYieldDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eveningYield', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByFatContent() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fatContent', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByFatContentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fatContent', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByMorning() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'morning', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByMorningDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'morning', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByMorningAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'morningAmount', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByMorningAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'morningAmount', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByMorningYield() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'morningYield', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByMorningYieldDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'morningYield', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByPricePerLiter() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pricePerLiter', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByPricePerLiterDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pricePerLiter', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByRecordId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordId', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByRecordIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordId', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByRecordedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordedAt', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByRecordedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordedAt', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByTagId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'tagId', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByTagIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'tagId', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByTotalAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalAmount', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByTotalAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalAmount', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByTotalYield() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalYield', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByTotalYieldDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalYield', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension MilkRecordIsarQuerySortThenBy
    on QueryBuilder<MilkRecordIsar, MilkRecordIsar, QSortThenBy> {
  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByAfternoon() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'afternoon', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByAfternoonDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'afternoon', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByAfternoonAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'afternoonAmount', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByAfternoonAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'afternoonAmount', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByCattleBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByCattleBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleBusinessId', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByCattleId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleId', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByCattleIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleId', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByCattleTagId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByCattleTagIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cattleTagId', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'date', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByEvening() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'evening', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByEveningDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'evening', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByEveningAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eveningAmount', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByEveningAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eveningAmount', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByEveningYield() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eveningYield', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByEveningYieldDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'eveningYield', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByFatContent() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fatContent', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByFatContentDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'fatContent', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByMorning() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'morning', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByMorningDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'morning', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByMorningAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'morningAmount', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByMorningAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'morningAmount', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByMorningYield() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'morningYield', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByMorningYieldDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'morningYield', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByNotes() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByNotesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'notes', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByPricePerLiter() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pricePerLiter', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByPricePerLiterDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pricePerLiter', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByRecordId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordId', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByRecordIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordId', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByRecordedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordedAt', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByRecordedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'recordedAt', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByTagId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'tagId', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByTagIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'tagId', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByTotalAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalAmount', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByTotalAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalAmount', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByTotalYield() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalYield', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByTotalYieldDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalYield', Sort.desc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy> thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension MilkRecordIsarQueryWhereDistinct
    on QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct> {
  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByAfternoon() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'afternoon');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByAfternoonAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'afternoonAmount');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct> distinctByBusinessId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByCattleBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cattleBusinessId',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct> distinctByCattleId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cattleId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct> distinctByCattleTagId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cattleTagId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct> distinctByDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'date');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct> distinctByEvening() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'evening');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByEveningAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'eveningAmount');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByEveningYield() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'eveningYield');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByFatContent() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'fatContent');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct> distinctByMorning() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'morning');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByMorningAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'morningAmount');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByMorningYield() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'morningYield');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct> distinctByNotes(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'notes', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByPricePerLiter() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'pricePerLiter');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct> distinctByRecordId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'recordId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByRecordedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'recordedAt');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct> distinctByTagId(
      {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'tagId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByTotalAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalAmount');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByTotalYield() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalYield');
    });
  }

  QueryBuilder<MilkRecordIsar, MilkRecordIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension MilkRecordIsarQueryProperty
    on QueryBuilder<MilkRecordIsar, MilkRecordIsar, QQueryProperty> {
  QueryBuilder<MilkRecordIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<MilkRecordIsar, double?, QQueryOperations> afternoonProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'afternoon');
    });
  }

  QueryBuilder<MilkRecordIsar, double?, QQueryOperations>
      afternoonAmountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'afternoonAmount');
    });
  }

  QueryBuilder<MilkRecordIsar, String?, QQueryOperations> businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<MilkRecordIsar, String?, QQueryOperations>
      cattleBusinessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cattleBusinessId');
    });
  }

  QueryBuilder<MilkRecordIsar, String?, QQueryOperations> cattleIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cattleId');
    });
  }

  QueryBuilder<MilkRecordIsar, String?, QQueryOperations>
      cattleTagIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cattleTagId');
    });
  }

  QueryBuilder<MilkRecordIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<MilkRecordIsar, DateTime?, QQueryOperations> dateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'date');
    });
  }

  QueryBuilder<MilkRecordIsar, double?, QQueryOperations> eveningProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'evening');
    });
  }

  QueryBuilder<MilkRecordIsar, double?, QQueryOperations>
      eveningAmountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'eveningAmount');
    });
  }

  QueryBuilder<MilkRecordIsar, double?, QQueryOperations>
      eveningYieldProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'eveningYield');
    });
  }

  QueryBuilder<MilkRecordIsar, double?, QQueryOperations> fatContentProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'fatContent');
    });
  }

  QueryBuilder<MilkRecordIsar, double?, QQueryOperations> morningProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'morning');
    });
  }

  QueryBuilder<MilkRecordIsar, double?, QQueryOperations>
      morningAmountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'morningAmount');
    });
  }

  QueryBuilder<MilkRecordIsar, double?, QQueryOperations>
      morningYieldProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'morningYield');
    });
  }

  QueryBuilder<MilkRecordIsar, String?, QQueryOperations> notesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'notes');
    });
  }

  QueryBuilder<MilkRecordIsar, double?, QQueryOperations>
      pricePerLiterProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'pricePerLiter');
    });
  }

  QueryBuilder<MilkRecordIsar, String?, QQueryOperations> recordIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'recordId');
    });
  }

  QueryBuilder<MilkRecordIsar, DateTime?, QQueryOperations>
      recordedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'recordedAt');
    });
  }

  QueryBuilder<MilkRecordIsar, String?, QQueryOperations> tagIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'tagId');
    });
  }

  QueryBuilder<MilkRecordIsar, double?, QQueryOperations>
      totalAmountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalAmount');
    });
  }

  QueryBuilder<MilkRecordIsar, double, QQueryOperations> totalYieldProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalYield');
    });
  }

  QueryBuilder<MilkRecordIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
