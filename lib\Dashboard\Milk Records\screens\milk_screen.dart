import 'package:flutter/material.dart';

import '../../../constants/app_tabs.dart';
import '../../../constants/app_colors.dart';
import '../controllers/milk_controller.dart';
import '../tabs/milk_records_tab.dart';
import '../tabs/milk_sales_tab.dart';
import '../tabs/milk_analytics_tab.dart';
import '../tabs/milk_insights_tab.dart';
import '../../widgets/index.dart'; // Import Universal Components

class MilkScreen extends StatefulWidget {
  const MilkScreen({super.key});

  @override
  State<MilkScreen> createState() => _MilkScreenState();
}

class _MilkScreenState extends State<MilkScreen>
    with TickerProviderStateMixin, UniversalScreenState, UniversalDataRefresh {
  late TabController _tabController;
  late MilkController _milkController;

  final List<TabItem> _tabs = [
    TabItem(icon: Icons.analytics, label: 'Analytics', color: AppColors.milkHeader, screen: Container()),
    TabItem(icon: Icons.list_alt, label: 'Records', color: AppColors.milkHeader, screen: Container()),
    TabItem(icon: Icons.point_of_sale, label: 'Sales', color: AppColors.milkHeader, screen: Container()),
    TabItem(icon: Icons.lightbulb, label: 'Insights', color: AppColors.milkHeader, screen: Container()),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _milkController = MilkController();
    _milkController.loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _milkController.dispose();
    super.dispose();
  }

  ScreenState _mapControllerStateToScreenState(ControllerState controllerState) {
    switch (controllerState) {
      case ControllerState.initial:
        return ScreenState.initial;
      case ControllerState.loading:
        return ScreenState.loading;
      case ControllerState.loaded:
        return ScreenState.loaded;
      case ControllerState.error:
        return ScreenState.error;
      case ControllerState.empty:
        return ScreenState.empty;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Milk Management'),
        backgroundColor: UniversalEmptyStateTheme.milk,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => performRefresh(() => _milkController.refresh()),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar with multicolor support
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
            useMulticolor: true,
            indicatorColor: UniversalEmptyStateTheme.milk,
          ),
          // TabBarView
          Expanded(
            child: ListenableBuilder(
              listenable: _milkController,
              builder: (context, child) {
                final controllerState = _milkController.state;

                return UniversalStateBuilder(
                  state: _mapControllerStateToScreenState(controllerState),
                  errorMessage: _milkController.errorMessage,
                  onRetry: () => performRefresh(() => _milkController.refresh()),
                  moduleColor: UniversalEmptyStateTheme.milk,
                  loadingWidget: UniversalLoadingIndicator.milk(),
                  errorWidget: UniversalErrorIndicator.milk(
                    message: _milkController.errorMessage ?? 'Failed to load milk data',
                    onRetry: () => performRefresh(() => _milkController.refresh()),
                  ),
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      MilkAnalyticsTab(controller: _milkController),
                      MilkRecordsTab(controller: _milkController),
                      MilkSalesTab(controller: _milkController),
                      MilkInsightsTab(controller: _milkController),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

}
