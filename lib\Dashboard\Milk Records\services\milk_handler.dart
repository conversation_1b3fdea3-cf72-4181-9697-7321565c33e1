import 'package:logging/logging.dart';
import 'package:isar/isar.dart';
import 'package:get_it/get_it.dart';

import '../models/milk_record_isar.dart';
import '../../../services/database/isar_service.dart';
import '../../../services/database/exceptions/database_exceptions.dart';

/// Consolidated handler for all Milk Records module database operations
class MilkHandler {
  static final Logger _logger = Logger('MilkHandler');
  final IsarService _isarService;

  // Singleton instance
  static final MilkHandler _instance = MilkHandler._internal();
  static MilkHandler get instance => _instance;

  // Private constructor
  MilkHandler._internal() : _isarService = GetIt.instance<IsarService>();

  // Getter for Isar instance
  Isar get _isar => _isarService.isar;

  //=== MILK RECORDS ===//

  /// Get all milk records
  Future<List<MilkRecordIsar>> getAllMilkRecords() async {
    try {
      return await _isar.milkRecordIsars
          .where()
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting all milk records: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle
  Future<List<MilkRecordIsar>> getMilkRecordsForCattle(String cattleBusinessId) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.milkRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleBusinessId: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Add a new milk record
  /// Returns the saved record with its ID
  Future<MilkRecordIsar> addMilkRecord(MilkRecordIsar record) async {
    try {
      late int recordId;
      await _isar.writeTxn(() async {
        recordId = await _isar.milkRecordIsars.put(record);
      });

      // Fetch the saved record to ensure we have the complete object with ID
      final savedRecord = await _isar.milkRecordIsars.get(recordId);
      if (savedRecord == null) {
        throw DatabaseException('Failed to retrieve saved record', 'Record not found after saving');
      }

      _logger.info('Added new milk record: ${savedRecord.businessId}, ID: $recordId');
      return savedRecord;
    } catch (e) {
      _logger.severe('Error adding milk record: $e');
      throw DatabaseException('Failed to add milk record', e.toString());
    }
  }

  /// Update an existing milk record
  /// Returns the saved record with its ID
  Future<MilkRecordIsar> updateMilkRecord(MilkRecordIsar record) async {
    try {
      late int recordId;
      await _isar.writeTxn(() async {
        recordId = await _isar.milkRecordIsars.put(record);
      });

      // Fetch the saved record to ensure we have the complete object with ID
      final savedRecord = await _isar.milkRecordIsars.get(recordId);
      if (savedRecord == null) {
        throw DatabaseException('Failed to retrieve saved record', 'Record not found after saving');
      }

      _logger.info('Updated milk record: ${savedRecord.businessId}, ID: $recordId');
      return savedRecord;
    } catch (e) {
      _logger.severe('Error updating milk record: $e');
      throw DatabaseException('Failed to update milk record', e.toString());
    }
  }

  /// Delete a milk record
  Future<void> deleteMilkRecord(Id id) async {
    try {
      await _isar.writeTxn(() async {
        await _isar.milkRecordIsars.delete(id);
      });
      _logger.info('Deleted milk record: $id');
    } catch (e) {
      _logger.severe('Error deleting milk record: $e');
      throw DatabaseException('Failed to delete milk record', e.toString());
    }
  }

  /// Get milk records for a date range
  Future<List<MilkRecordIsar>> getMilkRecordsForDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _isar.milkRecordIsars
          .filter()
          .dateBetween(startDate, endDate)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting milk records for date range: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle in a date range
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleInDateRange(
    String cattleBusinessId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      return await _isar.milkRecordIsars
          .filter()
          .cattleBusinessIdEqualTo(cattleBusinessId)
          .dateBetween(startDate, endDate)
          .sortByDateDesc()
          .findAll();
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleBusinessId in date range: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific date
  Future<List<MilkRecordIsar>> getMilkRecordsForDate(DateTime date) async {
    try {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));
      return await getMilkRecordsForDateRange(startOfDay, endOfDay);
    } catch (e) {
      _logger.severe('Error getting milk records for date: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle on a specific date
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleOnDate(
    String cattleBusinessId,
    DateTime date,
  ) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));
      return await getMilkRecordsForCattleInDateRange(cattleBusinessId, startOfDay, endOfDay);
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleBusinessId on date: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle in the last N days
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleInLastNDays(
    String cattleBusinessId,
    int days,
  ) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: days));
      return await getMilkRecordsForCattleInDateRange(cattleBusinessId, startDate, endDate);
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleBusinessId in last $days days: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle in the last N months
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleInLastNMonths(
    String cattleBusinessId,
    int months,
  ) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      final endDate = DateTime.now();
      final startDate = DateTime(endDate.year, endDate.month - months, endDate.day);
      return await getMilkRecordsForCattleInDateRange(cattleBusinessId, startDate, endDate);
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleBusinessId in last $months months: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle in the last N years
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleInLastNYears(
    String cattleBusinessId,
    int years,
  ) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      final endDate = DateTime.now();
      final startDate = DateTime(endDate.year - years, endDate.month, endDate.day);
      return await getMilkRecordsForCattleInDateRange(cattleBusinessId, startDate, endDate);
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleBusinessId in last $years years: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }

  /// Get milk records for a specific cattle in the last N weeks
  Future<List<MilkRecordIsar>> getMilkRecordsForCattleInLastNWeeks(
    String cattleBusinessId,
    int weeks,
  ) async {
    try {
      if (cattleBusinessId.isEmpty) {
        throw ValidationException('Cattle ID is required');
      }

      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: weeks * 7));
      return await getMilkRecordsForCattleInDateRange(cattleBusinessId, startDate, endDate);
    } catch (e) {
      _logger.severe('Error getting milk records for cattle $cattleBusinessId in last $weeks weeks: $e');
      throw DatabaseException('Failed to retrieve milk records', e.toString());
    }
  }
}