import 'package:flutter/material.dart';
import '../controllers/milk_controller.dart';
import '../../widgets/index.dart';

class MilkInsightsTab extends StatelessWidget {
  final MilkController controller;

  const MilkInsightsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Milk Production Insights',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'AI-powered recommendations and insights for your milk production management',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 24),

          // Insights Cards
          ..._buildInsightCards(context),
        ],
      ),
    );
  }

  List<Widget> _buildInsightCards(BuildContext context) {
    final insights = _generateInsights();
    
    if (insights.isEmpty) {
      return [
        UniversalEmptyState.milk(
          title: 'No Insights Available',
          message: 'Add more milk records to get personalized insights',
        ),
      ];
    }

    return insights.map((insight) => _buildInsightCard(context, insight)).toList();
  }

  Widget _buildInsightCard(BuildContext context, MilkInsight insight) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  insight.icon,
                  color: insight.color,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    insight.title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: insight.priority.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: insight.priority.color.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    insight.priority.label,
                    style: TextStyle(
                      color: insight.priority.color,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              insight.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            if (insight.recommendations.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                'Recommendations:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              ...insight.recommendations.map((rec) => Padding(
                padding: const EdgeInsets.only(left: 16, bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ', style: TextStyle(fontWeight: FontWeight.bold)),
                    Expanded(child: Text(rec)),
                  ],
                ),
              )),
            ],
          ],
        ),
      ),
    );
  }

  List<MilkInsight> _generateInsights() {
    final insights = <MilkInsight>[];
    
    // Milk Production Overview
    if (controller.totalMilkRecords == 0) {
      insights.add(MilkInsight(
        title: 'Start Milk Production Tracking',
        description: 'You haven\'t recorded any milk production yet. Start tracking daily milk production to monitor herd performance and optimize dairy operations.',
        icon: Icons.water_drop,
        color: UniversalEmptyStateTheme.milk,
        priority: InsightPriority.high,
        recommendations: [
          'Record daily milk production for each cow',
          'Track morning and evening milking sessions',
          'Monitor milk quality parameters',
          'Note any unusual observations',
        ],
      ));
    } else if (controller.totalMilkRecords < 30) {
      insights.add(MilkInsight(
        title: 'Building Production History',
        description: 'You have ${controller.totalMilkRecords} milk records. Continue consistent recording to establish production patterns and trends.',
        icon: Icons.trending_up,
        color: Colors.blue,
        priority: InsightPriority.medium,
        recommendations: [
          'Maintain daily recording schedule',
          'Track production variations',
          'Monitor seasonal patterns',
        ],
      ));
    }

    // Production Performance Analysis
    final productionInsight = _analyzeProduction();
    if (productionInsight != null) {
      insights.add(productionInsight);
    }

    // Female Cattle Analysis
    final femaleCattleInsight = _analyzeFemaleHerd();
    if (femaleCattleInsight != null) {
      insights.add(femaleCattleInsight);
    }

    // Sales Analysis
    final salesInsight = _analyzeSales();
    if (salesInsight != null) {
      insights.add(salesInsight);
    }

    // Best Practices
    insights.add(MilkInsight(
      title: 'Milk Production Best Practices',
      description: 'Follow these best practices to optimize milk production and ensure quality dairy operations.',
      icon: Icons.star,
      color: Colors.green,
      priority: InsightPriority.medium,
      recommendations: [
        'Maintain consistent milking schedules',
        'Ensure proper cow nutrition and hydration',
        'Monitor udder health regularly',
        'Keep detailed production records',
        'Implement proper milk storage and cooling',
        'Regular equipment cleaning and maintenance',
        'Track feed quality and quantity',
        'Monitor environmental conditions',
      ],
    ));

    return insights;
  }

  MilkInsight? _analyzeProduction() {
    if (controller.totalMilkRecords == 0) return null;

    final avgDaily = controller.averageDailyProduction;


    if (avgDaily < 10) {
      return MilkInsight(
        title: 'Low Daily Production',
        description: 'Your average daily production is ${avgDaily.toStringAsFixed(1)}L. This may indicate opportunities for improvement.',
        icon: Icons.trending_down,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Review cow nutrition and feeding schedule',
          'Check water availability and quality',
          'Evaluate milking technique and equipment',
          'Monitor cow health and stress levels',
          'Consider breed-specific production expectations',
        ],
      );
    } else if (avgDaily > 25) {
      return MilkInsight(
        title: 'Excellent Production Performance',
        description: 'Outstanding! Your average daily production is ${avgDaily.toStringAsFixed(1)}L. This indicates excellent herd management.',
        icon: Icons.celebration,
        color: Colors.green,
        priority: InsightPriority.low,
        recommendations: [
          'Continue current management practices',
          'Share successful methods with other farmers',
          'Consider expanding production capacity',
          'Monitor for sustainability of high production',
        ],
      );
    }

    return null;
  }

  MilkInsight? _analyzeFemaleHerd() {
    final totalCattle = controller.cattle.length;
    final femaleCattle = controller.femaleCattle;
    
    if (totalCattle == 0) return null;

    if (femaleCattle == 0) {
      return MilkInsight(
        title: 'No Female Cattle for Milk Production',
        description: 'Your herd has no female cattle. Female cattle are essential for milk production.',
        icon: Icons.female,
        color: Colors.red,
        priority: InsightPriority.high,
        recommendations: [
          'Acquire dairy cows or heifers',
          'Plan herd composition for milk production',
          'Consider breed selection for dairy purposes',
        ],
      );
    } else if (controller.totalMilkRecords > 0) {
      final recordsPerCow = controller.totalMilkRecords / femaleCattle;
      if (recordsPerCow < 7) {
        return MilkInsight(
          title: 'Inconsistent Recording',
          description: 'You have ${recordsPerCow.toStringAsFixed(1)} records per female cattle. Consider more consistent daily recording.',
          icon: Icons.schedule,
          color: Colors.orange,
          priority: InsightPriority.medium,
          recommendations: [
            'Establish daily recording routine',
            'Track all producing cows consistently',
            'Use reminders or scheduling tools',
            'Train staff on recording procedures',
          ],
        );
      }
    }

    return null;
  }

  MilkInsight? _analyzeSales() {
    if (controller.totalMilkRecords == 0) return null;

    final totalProduced = controller.totalMilkProduced;
    final totalSold = controller.totalMilkSold;

    if (controller.totalMilkSales == 0 && totalProduced > 0) {
      return MilkInsight(
        title: 'No Sales Records',
        description: 'You have milk production records but no sales records. Track sales to monitor revenue and market performance.',
        icon: Icons.point_of_sale,
        color: Colors.orange,
        priority: InsightPriority.medium,
        recommendations: [
          'Record all milk sales transactions',
          'Track prices and market trends',
          'Monitor customer relationships',
          'Calculate profit margins',
        ],
      );
    }

    if (totalSold > 0 && totalProduced > 0) {
      final salesRatio = (totalSold / totalProduced) * 100;
      
      if (salesRatio > 100) {
        return MilkInsight(
          title: 'Sales Exceed Production Records',
          description: 'Your recorded sales (${totalSold.toStringAsFixed(1)}L) exceed production (${totalProduced.toStringAsFixed(1)}L). Check record accuracy.',
          icon: Icons.warning,
          color: Colors.red,
          priority: InsightPriority.high,
          recommendations: [
            'Review production and sales records for accuracy',
            'Ensure all production is being recorded',
            'Check for data entry errors',
            'Verify measurement units consistency',
          ],
        );
      } else if (salesRatio < 50) {
        return MilkInsight(
          title: 'Low Sales Ratio',
          description: 'You\'re selling only ${salesRatio.toStringAsFixed(1)}% of your production. Consider market expansion opportunities.',
          icon: Icons.trending_down,
          color: Colors.orange,
          priority: InsightPriority.medium,
          recommendations: [
            'Explore new market opportunities',
            'Improve milk storage and preservation',
            'Consider value-added products',
            'Review pricing strategies',
          ],
        );
      }
    }

    return null;
  }
}

class MilkInsight {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final InsightPriority priority;
  final List<String> recommendations;

  MilkInsight({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.priority,
    this.recommendations = const [],
  });
}

enum InsightPriority {
  low(Colors.green, 'Low'),
  medium(Colors.orange, 'Medium'),
  high(Colors.red, 'High');

  const InsightPriority(this.color, this.label);
  final Color color;
  final String label;
}
