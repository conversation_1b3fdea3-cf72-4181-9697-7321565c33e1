import 'package:flutter/material.dart';
import '../models/milk_record_isar.dart';
import '../services/milk_service.dart';
import '../services/milk_handler.dart';
import '../dialogs/milk_form_dialog.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Cattle/details/cattle_detail_screen.dart';
import '../../Cattle/services/cattle_handler.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../controllers/milk_controller.dart';
import '../../../utils/message_utils.dart';
import '../../widgets/empty_state.dart';
import '../../widgets/index.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'package:logging/logging.dart';

class MilkRecordsTab extends StatelessWidget {
  final MilkController controller;

  const MilkRecordsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final records = controller.milkRecords;

    if (records.isEmpty) {
      return UniversalEmptyState.milk(
        title: 'No Milk Records',
        message: 'Add your first milk record to start tracking production',
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: records.length,
      itemBuilder: (context, index) {
        final record = records[index];
        final cattle = controller.getCattle(record.cattleBusinessId);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: UniversalEmptyStateTheme.milk,
              child: const Icon(
                Icons.water_drop,
                color: Colors.white,
              ),
            ),
            title: Text(cattle?.name ?? 'Unknown Cattle'),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Date: ${_formatDate(record.date)}'),
                Text('Quantity: ${record.quantity?.toStringAsFixed(1) ?? '0.0'}L'),
                if (record.session != null)
                  Text('Session: ${record.session}'),
              ],
            ),
            trailing: Text(
              '${record.quantity?.toStringAsFixed(1) ?? '0.0'}L',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        );
      },
    );
  }

  String _formatDate(DateTime? date) {
    if (date == null) return 'Unknown';
    return DateFormat('MMM dd, yyyy').format(date);
  }
}

// Wrapper class to navigate directly to milk tab and records tab
class CattleDetailScreenWithMilkTab extends StatelessWidget {
  final CattleIsar cattle;
  final BreedCategoryIsar breed;
  final AnimalTypeIsar animalType;
  final Function(CattleIsar) onCattleUpdated;

  const CattleDetailScreenWithMilkTab({
    Key? key,
    required this.cattle,
    required this.breed,
    required this.animalType,
    required this.onCattleUpdated,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return CattleDetailScreen(
      existingCattle: cattle,
      businessId: cattle.businessId ?? '',
      onCattleUpdated: onCattleUpdated,
    );
  }
}

class MilkRecordsScreen extends StatefulWidget {
  const MilkRecordsScreen({Key? key}) : super(key: key);

  @override
  State<MilkRecordsScreen> createState() => _MilkRecordsScreenState();
}

class _MilkRecordsScreenState extends State<MilkRecordsScreen> {
  final MilkService _milkService = MilkService();
  final MilkHandler _milkHandler = MilkHandler.instance;
  final CattleHandler _cattleHandler = CattleHandler.instance;
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;
  List<MilkRecordIsar> _records = [];
  Map<String, CattleIsar> _cattleMap = {};
  bool _isLoading = true;
  Timer? _refreshTimer;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');
  String _searchQuery = '';
  String _selectedAnimalType = 'All';
  String _selectedTagId = 'All';
  String _selectedDateRange = 'All Time';
  List<AnimalTypeIsar> _animalTypes = [];
  List<String> _tagIds = [];
  final _logger = Logger('MilkRecordsScreen');
  final TextEditingController _searchController = TextEditingController();

  final List<String> _dateRangeOptions = [
    'Today',
    '7 Days',
    '30 Days',
    '90 Days',
    'All Time',
  ];

  DateTimeRange? get _effectiveDateRange {
    final now = DateTime.now();
    switch (_selectedDateRange) {
      case 'Today':
        return DateTimeRange(
          start: DateTime(now.year, now.month, now.day),
          end: now,
        );
      case '7 Days':
        return DateTimeRange(
          start: now.subtract(const Duration(days: 7)),
          end: now,
        );
      case '30 Days':
        return DateTimeRange(
          start: now.subtract(const Duration(days: 30)),
          end: now,
        );
      case '90 Days':
        return DateTimeRange(
          start: now.subtract(const Duration(days: 90)),
          end: now,
        );
      case 'All Time':
      default:
        return null;
    }
  }

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _initializeData() async {
    try {
      setState(() => _isLoading = true);

      // Load all required data using Isar handlers directly
      final animalTypes = await _farmSetupHandler.getAllAnimalTypes();
      final allCattle = await _cattleHandler.getAllCattle();

      // Create maps for efficient lookups
      final cattleMap = {
        for (var cattle in allCattle) cattle.tagId ?? '': cattle
      };

      // Get unique tag IDs for female cattle only
      final tagIds = allCattle
          .where((c) => c.gender?.toLowerCase() == 'female')
          .map((c) => c.tagId ?? '')
          .where((id) => id.isNotEmpty)
          .toList();
      tagIds.sort();

      // Load milk records using MilkHandler directly
      final records = await _milkHandler.getAllMilkRecords();

      // Sort records by date (with null check)
      records.sort((a, b) {
        if (a.date == null) return 1;
        if (b.date == null) return -1;
        return b.date!.compareTo(a.date!);
      });

      // Verify data integrity
      for (var record in records) {
        final cattle = cattleMap[record.cattleTagId];
        if (cattle == null) {
          _logger.warning('No cattle found for tag ID: ${record.cattleTagId}');
        }
      }

      if (mounted) {
        setState(() {
          _cattleMap = cattleMap;
          _animalTypes = animalTypes;
          _tagIds = tagIds;
          _records = records;
          _isLoading = false;
        });
      }

      _setupPeriodicRefresh();
    } catch (e, stackTrace) {
      _logger.severe('Error loading milk records data', e, stackTrace);
      if (mounted) {
        setState(() => _isLoading = false);
        MilkMessageUtils.showError(context, 'Error loading milk records: $e');
      }
    }
  }

  void _setupPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(minutes: 5), (_) {
      _refreshData();
    });
  }

  Future<void> _refreshData() async {
    if (!mounted) return;
    try {
      await _initializeData();
    } catch (e) {
      _logger.severe('Error refreshing milk records data', e);
      if (mounted) {
        MilkMessageUtils.showError(context, 'Error refreshing data: $e');
      }
    }
  }

  List<MilkRecordIsar> get _filteredRecords {
    return _records.where((record) {
      // Check if record matches the filter criteria
      final cattle = _cattleMap[record.cattleTagId];

      // Filter by animal type
      if (_selectedAnimalType != 'All' && cattle != null) {
        final animalTypeId = cattle.animalTypeId;
        if (animalTypeId == null || animalTypeId != _selectedAnimalType) {
          return false;
        }
      }

      // Filter by tag ID
      if (_selectedTagId != 'All' && record.cattleTagId != _selectedTagId) {
        return false;
      }

      // Filter by date range
      final range = _effectiveDateRange;
      if (range != null && record.date != null) {
        if (!record.date!.isAfter(range.start) ||
            !record.date!.isBefore(range.end.add(const Duration(days: 1)))) {
          return false;
        }
      }

      // Filter by search query
      if (_searchQuery.isNotEmpty) {
        final tagId = record.cattleTagId ?? '';
        final cattleName = cattle?.name?.toLowerCase() ?? '';
        final recordMorning = record.morningAmount?.toString() ?? '';
        final recordEvening = record.eveningAmount?.toString() ?? '';
        final recordDate = record.date != null
            ? DateFormat('yyyy-MM-dd').format(record.date!)
            : '';
        final recordNotes = record.notes?.toLowerCase() ?? '';

        final searchLower = _searchQuery.toLowerCase();

        // Check if any field contains the search query
        return tagId.contains(searchLower) ||
            cattleName.contains(searchLower) ||
            recordMorning.contains(searchLower) ||
            recordEvening.contains(searchLower) ||
            recordDate.contains(searchLower) ||
            recordNotes.contains(searchLower);
      }

      return true;
    }).toList();
  }

  Future<void> _showAddRecordDialog([MilkRecordIsar? record]) async {
    final result = await showDialog<MilkRecordIsar>(
      context: context,
      builder: (context) => MilkFormDialog(
        record: record,
        cattleTagId: record?.cattleTagId,
      ),
    );

    if (result != null) {
      try {
        if (record != null) {
          await _milkService.updateMilkRecord(result);
        } else {
          await _milkService.addMilkRecord(result);
        }
        await _refreshData();
      } catch (e) {
        if (mounted) {
          MilkMessageUtils.showError(context,
              'Error ${record != null ? 'updating' : 'adding'} record: $e');
        }
      }
    }
  }

  Future<void> _deleteMilkRecord(MilkRecordIsar record) async {
    try {
      await _milkService.deleteMilkRecord(record.id);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Milk record deleted successfully')),
        );
      }
      await _refreshData();
    } catch (e) {
      _logger.severe('Error deleting milk record', e);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error deleting milk record: $e')),
        );
      }
    }
  }

  void _confirmDeleteRecord(MilkRecordIsar record) {
    final cattle = _cattleMap[record.cattleTagId];
    final cattleName = cattle?.name ?? 'Unknown Cattle';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Record'),
        content: Text(
            'Are you sure you want to delete this milk record for $cattleName from ${DateFormat('MMMM dd, yyyy').format(record.date ?? DateTime.now())}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _deleteMilkRecord(record);
            },
            child: const Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _clearFilters() {
    setState(() {
      _searchQuery = '';
      _searchController.clear();
      _selectedAnimalType = 'All';
      _selectedTagId = 'All';
      _selectedDateRange = 'All Time';
    });
  }

  Widget _buildSearchAndFilterSection() {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Search Row
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search by cattle name or tag ID',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
          // Filter Row
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            child: Row(
              children: [
                // Animal Type Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Types'),
                        ),
                        ..._animalTypes.map((type) => PopupMenuItem(
                              value: type.name ?? 'Unknown',
                              child: Text(type.name ?? 'Unknown'),
                            )),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedAnimalType = value;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedAnimalType,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Tag ID Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'All',
                          child: Text('All Tags'),
                        ),
                        ..._tagIds.map((tagId) => PopupMenuItem(
                              value: tagId,
                              child: Text(tagId),
                            )),
                      ],
                      onSelected: (value) {
                        setState(() {
                          _selectedTagId = value;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedTagId,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                // Date Range Filter
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: PopupMenuButton<String>(
                      position: PopupMenuPosition.under,
                      itemBuilder: (context) => _dateRangeOptions
                          .map((range) => PopupMenuItem(
                                value: range,
                                child: Text(range),
                              ))
                          .toList(),
                      onSelected: (value) {
                        setState(() {
                          _selectedDateRange = value;
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: Text(
                                _selectedDateRange,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            const Icon(Icons.arrow_drop_down),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Active Filters
          if (_selectedAnimalType != 'All' ||
              _selectedTagId != 'All' ||
              _selectedDateRange != 'All Time' ||
              _searchQuery.isNotEmpty)
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
              child: Row(
                children: [
                  const Text(
                    'Active Filters:',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(width: 8),
                  if (_selectedDateRange != 'All Time')
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_selectedDateRange),
                        onDeleted: () =>
                            setState(() => _selectedDateRange = 'All Time'),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  if (_selectedAnimalType != 'All')
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_selectedAnimalType),
                        onDeleted: () =>
                            setState(() => _selectedAnimalType = 'All'),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  if (_selectedTagId != 'All')
                    Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: Chip(
                        label: Text(_selectedTagId),
                        onDeleted: () => setState(() => _selectedTagId = 'All'),
                        labelStyle: const TextStyle(fontSize: 12),
                        padding: EdgeInsets.zero,
                        materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      ),
                    ),
                  TextButton(
                    onPressed: _clearFilters,
                    style: TextButton.styleFrom(
                      padding: const EdgeInsets.symmetric(horizontal: 8),
                      minimumSize: const Size(0, 24),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                    child: const Text(
                      'Clear All',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          const Divider(height: 1),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Milk Records'),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                _buildSearchAndFilterSection(),
                Expanded(
                  child: _filteredRecords.isEmpty
                      ? MilkEmptyState(
                          icon: _records.isEmpty
                              ? Icons.water_drop_outlined
                              : Icons.filter_alt_off_outlined,
                          message: _records.isEmpty
                              ? 'No Milk Records'
                              : 'No Records Found',
                          subtitle: _records.isEmpty
                              ? 'Start by adding your first milk record'
                              : 'No records match your current filters',
                          color: const Color(0xFF1976D2), // Blue for records
                          action: _records.isEmpty
                              ? ElevatedButton.icon(
                                  onPressed: _showAddRecordDialog,
                                  icon: const Icon(Icons.add),
                                  label: const Text('Add First Record'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF1976D2),
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 12),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                )
                              : ElevatedButton.icon(
                                  onPressed: _clearFilters,
                                  icon: const Icon(Icons.clear_all),
                                  label: const Text('Clear Filters'),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: const Color(0xFF1976D2),
                                    foregroundColor: Colors.white,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 12),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.only(top: 8, bottom: 88),
                          itemCount: _filteredRecords.length,
                          itemBuilder: (context, index) {
                            final record = _filteredRecords[index];
                            return _buildRecordCard(record);
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddRecordDialog,
        backgroundColor: const Color(0xFF2E7D32),
        child: const Icon(Icons.add),
      ),
    );
  }

  void _openEditDialog(MilkRecordIsar record) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => MilkFormDialog(
        record: record,
        cattleTagId: record.cattleTagId,
      ),
    );

    if (result == true && mounted) {
      _refreshData();
    }
  }

  Widget _buildMilkQuantityItem(
    IconData icon,
    String label,
    String value,
    MaterialColor color, {
    bool isTotal = false,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
        decoration: isTotal
            ? BoxDecoration(
                color: color.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: color.shade200),
              )
            : null,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, size: 14, color: color.shade700),
                const SizedBox(width: 4),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    color: color.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
                color: isTotal ? color.shade700 : Colors.black87,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRecordCard(MilkRecordIsar record) {
    final cattle = _cattleMap[record.cattleTagId];
    final cattleName = cattle?.name ?? 'Unknown Cattle';

    return GestureDetector(
      onTap: () => _navigateToCattleMilkTab(record.cattleTagId),
      child: Card(
        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        elevation: 2.0,
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CircleAvatar(
                        backgroundColor: Colors.blue.shade50,
                        radius: 24,
                        child: Icon(
                          Icons.water_drop,
                          color: Colors.blue.shade700,
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              DateFormat('MMMM dd, yyyy')
                                  .format(record.date ?? DateTime.now()),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '$cattleName (${record.cattleTagId})',
                              style: const TextStyle(
                                fontSize: 15,
                                color: Colors.black87,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildMilkQuantityItem(
                        Icons.wb_sunny_outlined,
                        'Morning',
                        '${record.morningAmount?.toStringAsFixed(1) ?? 'N/A'} L',
                        Colors.blue, // Use blue instead of orange
                      ),
                      _buildMilkQuantityItem(
                        Icons.nightlight_outlined,
                        'Evening',
                        '${record.eveningAmount?.toStringAsFixed(1) ?? 'N/A'} L',
                        Colors.indigo,
                      ),
                      _buildMilkQuantityItem(
                        Icons.local_drink_outlined,
                        'Total',
                        '${record.totalYield.toStringAsFixed(1)} L',
                        Colors.green,
                        isTotal: true,
                      ),
                    ],
                  ),
                  if (record.notes != null && record.notes!.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    const Divider(),
                    const SizedBox(height: 4),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Icon(Icons.note, size: 16, color: Colors.grey),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            record.notes!,
                            style: const TextStyle(
                              fontSize: 13,
                              color: Colors.grey,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),
            Positioned(
              top: 8,
              right: 8,
              child: Row(
                children: [
                  IconButton(
                    icon: const Icon(Icons.edit, size: 20),
                    onPressed: () => _openEditDialog(record),
                    color: Colors.blue,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    tooltip: 'Edit Record',
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.delete, size: 20),
                    onPressed: () => _confirmDeleteRecord(record),
                    color: Colors.red,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    tooltip: 'Delete Record',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _navigateToCattleMilkTab(String? tagId) async {
    if (tagId == null) return;

    final cattle = _cattleMap[tagId];
    if (cattle == null) {
      if (mounted) {
        MilkMessageUtils.showError(context, 'Cattle not found');
      }
      return;
    }

    try {
      // Get breed and animal type information
      final breedData = await _farmSetupHandler.getAllBreedCategories();
      final animalTypeData = await _farmSetupHandler.getAllAnimalTypes();

      // Find matching breed and animal type for this cattle
      final breed = breedData.firstWhere(
        (b) => b.businessId == cattle.breedId,
        orElse: () => breedData.first,
      );

      final animalType = animalTypeData.firstWhere(
        (t) => t.businessId == cattle.animalTypeId,
        orElse: () => animalTypeData.first,
      );

      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(
            builder: (context) => CattleDetailScreenWithMilkTab(
              cattle: cattle,
              breed: breed,
              animalType: animalType,
              onCattleUpdated: (updatedCattle) async {
                await _cattleHandler.updateCattle(updatedCattle);
                _refreshData();
              },
            ),
          ),
        );
      }
    } catch (e) {
      _logger.severe('Error navigating to cattle details', e);
      if (mounted) {
        MilkMessageUtils.showError(context, 'Error: $e');
      }
    }
  }
}
