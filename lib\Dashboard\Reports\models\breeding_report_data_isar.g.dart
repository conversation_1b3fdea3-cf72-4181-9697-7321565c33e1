// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'breeding_report_data_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetBreedingReportDataIsarCollection on Isar {
  IsarCollection<BreedingReportDataIsar> get breedingReportDataIsars =>
      this.collection();
}

const BreedingReportDataIsarSchema = CollectionSchema(
  name: r'BreedingReportDataIsar',
  id: 6049452949784331568,
  properties: {
    r'averageBreedingPerCow': PropertySchema(
      id: 0,
      name: r'averageBreedingPerCow',
      type: IsarType.double,
    ),
    r'averageDaysToPregConfirm': PropertySchema(
      id: 1,
      name: r'averageDaysToPregConfirm',
      type: IsarType.double,
    ),
    r'breedingCounts': PropertySchema(
      id: 2,
      name: r'breedingCounts',
      type: IsarType.longList,
    ),
    r'breedingDates': PropertySchema(
      id: 3,
      name: r'breedingDates',
      type: IsarType.dateTimeList,
    ),
    r'bullColors': PropertySchema(
      id: 4,
      name: r'bullColors',
      type: IsarType.longList,
    ),
    r'bullCounts': PropertySchema(
      id: 5,
      name: r'bullCounts',
      type: IsarType.longList,
    ),
    r'bullNames': PropertySchema(
      id: 6,
      name: r'bullNames',
      type: IsarType.stringList,
    ),
    r'businessId': PropertySchema(
      id: 7,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'createdAt': PropertySchema(
      id: 8,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'endDate': PropertySchema(
      id: 9,
      name: r'endDate',
      type: IsarType.dateTime,
    ),
    r'failedBreedings': PropertySchema(
      id: 10,
      name: r'failedBreedings',
      type: IsarType.long,
    ),
    r'filterCriteria': PropertySchema(
      id: 11,
      name: r'filterCriteria',
      type: IsarType.string,
    ),
    r'generatedAt': PropertySchema(
      id: 12,
      name: r'generatedAt',
      type: IsarType.dateTime,
    ),
    r'methodColors': PropertySchema(
      id: 13,
      name: r'methodColors',
      type: IsarType.longList,
    ),
    r'methodCounts': PropertySchema(
      id: 14,
      name: r'methodCounts',
      type: IsarType.longList,
    ),
    r'methodNames': PropertySchema(
      id: 15,
      name: r'methodNames',
      type: IsarType.stringList,
    ),
    r'pendingBreedings': PropertySchema(
      id: 16,
      name: r'pendingBreedings',
      type: IsarType.long,
    ),
    r'reportType': PropertySchema(
      id: 17,
      name: r'reportType',
      type: IsarType.string,
    ),
    r'startDate': PropertySchema(
      id: 18,
      name: r'startDate',
      type: IsarType.dateTime,
    ),
    r'successRate': PropertySchema(
      id: 19,
      name: r'successRate',
      type: IsarType.double,
    ),
    r'successfulBreedings': PropertySchema(
      id: 20,
      name: r'successfulBreedings',
      type: IsarType.long,
    ),
    r'title': PropertySchema(
      id: 21,
      name: r'title',
      type: IsarType.string,
    ),
    r'totalBreedings': PropertySchema(
      id: 22,
      name: r'totalBreedings',
      type: IsarType.long,
    ),
    r'updatedAt': PropertySchema(
      id: 23,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _breedingReportDataIsarEstimateSize,
  serialize: _breedingReportDataIsarSerialize,
  deserialize: _breedingReportDataIsarDeserialize,
  deserializeProp: _breedingReportDataIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'reportType': IndexSchema(
      id: 3559997651334899995,
      name: r'reportType',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'reportType',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _breedingReportDataIsarGetId,
  getLinks: _breedingReportDataIsarGetLinks,
  attach: _breedingReportDataIsarAttach,
  version: '3.1.0+1',
);

int _breedingReportDataIsarEstimateSize(
  BreedingReportDataIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.breedingCounts;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.breedingDates;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.bullColors;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.bullCounts;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final list = object.bullNames;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.filterCriteria;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.methodColors;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.methodCounts;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final list = object.methodNames;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.reportType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.title;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _breedingReportDataIsarSerialize(
  BreedingReportDataIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDouble(offsets[0], object.averageBreedingPerCow);
  writer.writeDouble(offsets[1], object.averageDaysToPregConfirm);
  writer.writeLongList(offsets[2], object.breedingCounts);
  writer.writeDateTimeList(offsets[3], object.breedingDates);
  writer.writeLongList(offsets[4], object.bullColors);
  writer.writeLongList(offsets[5], object.bullCounts);
  writer.writeStringList(offsets[6], object.bullNames);
  writer.writeString(offsets[7], object.businessId);
  writer.writeDateTime(offsets[8], object.createdAt);
  writer.writeDateTime(offsets[9], object.endDate);
  writer.writeLong(offsets[10], object.failedBreedings);
  writer.writeString(offsets[11], object.filterCriteria);
  writer.writeDateTime(offsets[12], object.generatedAt);
  writer.writeLongList(offsets[13], object.methodColors);
  writer.writeLongList(offsets[14], object.methodCounts);
  writer.writeStringList(offsets[15], object.methodNames);
  writer.writeLong(offsets[16], object.pendingBreedings);
  writer.writeString(offsets[17], object.reportType);
  writer.writeDateTime(offsets[18], object.startDate);
  writer.writeDouble(offsets[19], object.successRate);
  writer.writeLong(offsets[20], object.successfulBreedings);
  writer.writeString(offsets[21], object.title);
  writer.writeLong(offsets[22], object.totalBreedings);
  writer.writeDateTime(offsets[23], object.updatedAt);
}

BreedingReportDataIsar _breedingReportDataIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = BreedingReportDataIsar(
    endDate: reader.readDateTimeOrNull(offsets[9]),
    startDate: reader.readDateTimeOrNull(offsets[18]),
  );
  object.averageBreedingPerCow = reader.readDoubleOrNull(offsets[0]);
  object.averageDaysToPregConfirm = reader.readDoubleOrNull(offsets[1]);
  object.breedingCounts = reader.readLongList(offsets[2]);
  object.breedingDates = reader.readDateTimeList(offsets[3]);
  object.bullColors = reader.readLongList(offsets[4]);
  object.bullCounts = reader.readLongList(offsets[5]);
  object.bullNames = reader.readStringList(offsets[6]);
  object.businessId = reader.readStringOrNull(offsets[7]);
  object.createdAt = reader.readDateTimeOrNull(offsets[8]);
  object.failedBreedings = reader.readLongOrNull(offsets[10]);
  object.filterCriteria = reader.readStringOrNull(offsets[11]);
  object.generatedAt = reader.readDateTimeOrNull(offsets[12]);
  object.id = id;
  object.methodColors = reader.readLongList(offsets[13]);
  object.methodCounts = reader.readLongList(offsets[14]);
  object.methodNames = reader.readStringList(offsets[15]);
  object.pendingBreedings = reader.readLongOrNull(offsets[16]);
  object.reportType = reader.readStringOrNull(offsets[17]);
  object.successRate = reader.readDoubleOrNull(offsets[19]);
  object.successfulBreedings = reader.readLongOrNull(offsets[20]);
  object.title = reader.readStringOrNull(offsets[21]);
  object.totalBreedings = reader.readLongOrNull(offsets[22]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[23]);
  return object;
}

P _breedingReportDataIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readDoubleOrNull(offset)) as P;
    case 1:
      return (reader.readDoubleOrNull(offset)) as P;
    case 2:
      return (reader.readLongList(offset)) as P;
    case 3:
      return (reader.readDateTimeList(offset)) as P;
    case 4:
      return (reader.readLongList(offset)) as P;
    case 5:
      return (reader.readLongList(offset)) as P;
    case 6:
      return (reader.readStringList(offset)) as P;
    case 7:
      return (reader.readStringOrNull(offset)) as P;
    case 8:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 9:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 10:
      return (reader.readLongOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 13:
      return (reader.readLongList(offset)) as P;
    case 14:
      return (reader.readLongList(offset)) as P;
    case 15:
      return (reader.readStringList(offset)) as P;
    case 16:
      return (reader.readLongOrNull(offset)) as P;
    case 17:
      return (reader.readStringOrNull(offset)) as P;
    case 18:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 19:
      return (reader.readDoubleOrNull(offset)) as P;
    case 20:
      return (reader.readLongOrNull(offset)) as P;
    case 21:
      return (reader.readStringOrNull(offset)) as P;
    case 22:
      return (reader.readLongOrNull(offset)) as P;
    case 23:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _breedingReportDataIsarGetId(BreedingReportDataIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _breedingReportDataIsarGetLinks(
    BreedingReportDataIsar object) {
  return [];
}

void _breedingReportDataIsarAttach(
    IsarCollection<dynamic> col, Id id, BreedingReportDataIsar object) {
  object.id = id;
}

extension BreedingReportDataIsarByIndex
    on IsarCollection<BreedingReportDataIsar> {
  Future<BreedingReportDataIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  BreedingReportDataIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<BreedingReportDataIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<BreedingReportDataIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(BreedingReportDataIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(BreedingReportDataIsar object,
      {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<BreedingReportDataIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<BreedingReportDataIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension BreedingReportDataIsarQueryWhereSort
    on QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QWhere> {
  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterWhere>
      anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension BreedingReportDataIsarQueryWhere on QueryBuilder<
    BreedingReportDataIsar, BreedingReportDataIsar, QWhereClause> {
  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'reportType',
        value: [null],
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'reportType',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> reportTypeEqualTo(String? reportType) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'reportType',
        value: [reportType],
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> reportTypeNotEqualTo(String? reportType) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [],
              upper: [reportType],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [reportType],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [reportType],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [],
              upper: [reportType],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterWhereClause> businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension BreedingReportDataIsarQueryFilter on QueryBuilder<
    BreedingReportDataIsar, BreedingReportDataIsar, QFilterCondition> {
  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> averageBreedingPerCowIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'averageBreedingPerCow',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> averageBreedingPerCowIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'averageBreedingPerCow',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> averageBreedingPerCowEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'averageBreedingPerCow',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> averageBreedingPerCowGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'averageBreedingPerCow',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> averageBreedingPerCowLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'averageBreedingPerCow',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> averageBreedingPerCowBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'averageBreedingPerCow',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> averageDaysToPregConfirmIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'averageDaysToPregConfirm',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> averageDaysToPregConfirmIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'averageDaysToPregConfirm',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> averageDaysToPregConfirmEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'averageDaysToPregConfirm',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> averageDaysToPregConfirmGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'averageDaysToPregConfirm',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> averageDaysToPregConfirmLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'averageDaysToPregConfirm',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> averageDaysToPregConfirmBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'averageDaysToPregConfirm',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingCountsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'breedingCounts',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingCountsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'breedingCounts',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingCountsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'breedingCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingCountsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'breedingCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingCountsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'breedingCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingCountsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'breedingCounts',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingCountsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedingCounts',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingCountsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedingCounts',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingCountsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedingCounts',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingCountsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedingCounts',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingCountsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedingCounts',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingCountsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedingCounts',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingDatesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'breedingDates',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingDatesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'breedingDates',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingDatesElementEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'breedingDates',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingDatesElementGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'breedingDates',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingDatesElementLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'breedingDates',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingDatesElementBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'breedingDates',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingDatesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedingDates',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingDatesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedingDates',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingDatesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedingDates',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingDatesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedingDates',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingDatesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedingDates',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> breedingDatesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedingDates',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullColorsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'bullColors',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullColorsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'bullColors',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullColorsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'bullColors',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullColorsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'bullColors',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullColorsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'bullColors',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullColorsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'bullColors',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullColorsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullColors',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullColorsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullColors',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullColorsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullColors',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullColorsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullColors',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullColorsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullColors',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullColorsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullColors',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullCountsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'bullCounts',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullCountsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'bullCounts',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullCountsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'bullCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullCountsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'bullCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullCountsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'bullCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullCountsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'bullCounts',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullCountsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullCounts',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullCountsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullCounts',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullCountsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullCounts',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullCountsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullCounts',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullCountsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullCounts',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullCountsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullCounts',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'bullNames',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'bullNames',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'bullNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'bullNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'bullNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'bullNames',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'bullNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'bullNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
          QAfterFilterCondition>
      bullNamesElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'bullNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
          QAfterFilterCondition>
      bullNamesElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'bullNames',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'bullNames',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'bullNames',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullNames',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullNames',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullNames',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullNames',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullNames',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> bullNamesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'bullNames',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
          QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
          QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> endDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> endDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> endDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> endDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> endDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> endDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'endDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> failedBreedingsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'failedBreedings',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> failedBreedingsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'failedBreedings',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> failedBreedingsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'failedBreedings',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> failedBreedingsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'failedBreedings',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> failedBreedingsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'failedBreedings',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> failedBreedingsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'failedBreedings',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> filterCriteriaEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> filterCriteriaGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> filterCriteriaLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> filterCriteriaBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'filterCriteria',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> filterCriteriaStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> filterCriteriaEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
          QAfterFilterCondition>
      filterCriteriaContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
          QAfterFilterCondition>
      filterCriteriaMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'filterCriteria',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> generatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> generatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> generatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> generatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> generatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> generatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'generatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodColorsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'methodColors',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodColorsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'methodColors',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodColorsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'methodColors',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodColorsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'methodColors',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodColorsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'methodColors',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodColorsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'methodColors',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodColorsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodColors',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodColorsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodColors',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodColorsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodColors',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodColorsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodColors',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodColorsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodColors',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodColorsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodColors',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodCountsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'methodCounts',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodCountsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'methodCounts',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodCountsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'methodCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodCountsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'methodCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodCountsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'methodCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodCountsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'methodCounts',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodCountsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodCounts',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodCountsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodCounts',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodCountsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodCounts',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodCountsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodCounts',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodCountsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodCounts',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodCountsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodCounts',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'methodNames',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'methodNames',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'methodNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'methodNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'methodNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'methodNames',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'methodNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'methodNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
          QAfterFilterCondition>
      methodNamesElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'methodNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
          QAfterFilterCondition>
      methodNamesElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'methodNames',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'methodNames',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'methodNames',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodNames',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodNames',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodNames',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodNames',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodNames',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> methodNamesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'methodNames',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> pendingBreedingsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'pendingBreedings',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> pendingBreedingsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'pendingBreedings',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> pendingBreedingsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'pendingBreedings',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> pendingBreedingsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'pendingBreedings',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> pendingBreedingsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'pendingBreedings',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> pendingBreedingsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'pendingBreedings',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> reportTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> reportTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> reportTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> reportTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reportType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> reportTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> reportTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
          QAfterFilterCondition>
      reportTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
          QAfterFilterCondition>
      reportTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'reportType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> reportTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> reportTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> startDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> startDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> startDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> startDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> startDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> startDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'startDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> successRateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'successRate',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> successRateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'successRate',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> successRateEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'successRate',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> successRateGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'successRate',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> successRateLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'successRate',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> successRateBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'successRate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> successfulBreedingsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'successfulBreedings',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> successfulBreedingsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'successfulBreedings',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> successfulBreedingsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'successfulBreedings',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> successfulBreedingsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'successfulBreedings',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> successfulBreedingsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'successfulBreedings',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> successfulBreedingsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'successfulBreedings',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> titleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> titleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> titleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> titleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> titleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> titleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'title',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> titleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> titleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
          QAfterFilterCondition>
      titleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
          QAfterFilterCondition>
      titleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'title',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> titleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> titleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> totalBreedingsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalBreedings',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> totalBreedingsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalBreedings',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> totalBreedingsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalBreedings',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> totalBreedingsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalBreedings',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> totalBreedingsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalBreedings',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> totalBreedingsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalBreedings',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar,
      QAfterFilterCondition> updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension BreedingReportDataIsarQueryObject on QueryBuilder<
    BreedingReportDataIsar, BreedingReportDataIsar, QFilterCondition> {}

extension BreedingReportDataIsarQueryLinks on QueryBuilder<
    BreedingReportDataIsar, BreedingReportDataIsar, QFilterCondition> {}

extension BreedingReportDataIsarQuerySortBy
    on QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QSortBy> {
  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByAverageBreedingPerCow() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageBreedingPerCow', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByAverageBreedingPerCowDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageBreedingPerCow', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByAverageDaysToPregConfirm() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageDaysToPregConfirm', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByAverageDaysToPregConfirmDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageDaysToPregConfirm', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByFailedBreedings() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'failedBreedings', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByFailedBreedingsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'failedBreedings', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByPendingBreedings() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pendingBreedings', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByPendingBreedingsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pendingBreedings', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortBySuccessRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'successRate', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortBySuccessRateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'successRate', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortBySuccessfulBreedings() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'successfulBreedings', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortBySuccessfulBreedingsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'successfulBreedings', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByTotalBreedings() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalBreedings', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByTotalBreedingsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalBreedings', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension BreedingReportDataIsarQuerySortThenBy on QueryBuilder<
    BreedingReportDataIsar, BreedingReportDataIsar, QSortThenBy> {
  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByAverageBreedingPerCow() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageBreedingPerCow', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByAverageBreedingPerCowDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageBreedingPerCow', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByAverageDaysToPregConfirm() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageDaysToPregConfirm', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByAverageDaysToPregConfirmDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageDaysToPregConfirm', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByFailedBreedings() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'failedBreedings', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByFailedBreedingsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'failedBreedings', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByPendingBreedings() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pendingBreedings', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByPendingBreedingsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'pendingBreedings', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenBySuccessRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'successRate', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenBySuccessRateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'successRate', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenBySuccessfulBreedings() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'successfulBreedings', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenBySuccessfulBreedingsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'successfulBreedings', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByTotalBreedings() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalBreedings', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByTotalBreedingsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalBreedings', Sort.desc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension BreedingReportDataIsarQueryWhereDistinct
    on QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct> {
  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByAverageBreedingPerCow() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'averageBreedingPerCow');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByAverageDaysToPregConfirm() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'averageDaysToPregConfirm');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByBreedingCounts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'breedingCounts');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByBreedingDates() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'breedingDates');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByBullColors() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'bullColors');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByBullCounts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'bullCounts');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByBullNames() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'bullNames');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'endDate');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByFailedBreedings() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'failedBreedings');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByFilterCriteria({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'filterCriteria',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'generatedAt');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByMethodColors() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'methodColors');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByMethodCounts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'methodCounts');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByMethodNames() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'methodNames');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByPendingBreedings() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'pendingBreedings');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByReportType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reportType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'startDate');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctBySuccessRate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'successRate');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctBySuccessfulBreedings() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'successfulBreedings');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByTitle({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'title', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByTotalBreedings() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalBreedings');
    });
  }

  QueryBuilder<BreedingReportDataIsar, BreedingReportDataIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension BreedingReportDataIsarQueryProperty on QueryBuilder<
    BreedingReportDataIsar, BreedingReportDataIsar, QQueryProperty> {
  QueryBuilder<BreedingReportDataIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<BreedingReportDataIsar, double?, QQueryOperations>
      averageBreedingPerCowProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'averageBreedingPerCow');
    });
  }

  QueryBuilder<BreedingReportDataIsar, double?, QQueryOperations>
      averageDaysToPregConfirmProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'averageDaysToPregConfirm');
    });
  }

  QueryBuilder<BreedingReportDataIsar, List<int>?, QQueryOperations>
      breedingCountsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'breedingCounts');
    });
  }

  QueryBuilder<BreedingReportDataIsar, List<DateTime>?, QQueryOperations>
      breedingDatesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'breedingDates');
    });
  }

  QueryBuilder<BreedingReportDataIsar, List<int>?, QQueryOperations>
      bullColorsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'bullColors');
    });
  }

  QueryBuilder<BreedingReportDataIsar, List<int>?, QQueryOperations>
      bullCountsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'bullCounts');
    });
  }

  QueryBuilder<BreedingReportDataIsar, List<String>?, QQueryOperations>
      bullNamesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'bullNames');
    });
  }

  QueryBuilder<BreedingReportDataIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<BreedingReportDataIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<BreedingReportDataIsar, DateTime?, QQueryOperations>
      endDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'endDate');
    });
  }

  QueryBuilder<BreedingReportDataIsar, int?, QQueryOperations>
      failedBreedingsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'failedBreedings');
    });
  }

  QueryBuilder<BreedingReportDataIsar, String?, QQueryOperations>
      filterCriteriaProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'filterCriteria');
    });
  }

  QueryBuilder<BreedingReportDataIsar, DateTime?, QQueryOperations>
      generatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'generatedAt');
    });
  }

  QueryBuilder<BreedingReportDataIsar, List<int>?, QQueryOperations>
      methodColorsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'methodColors');
    });
  }

  QueryBuilder<BreedingReportDataIsar, List<int>?, QQueryOperations>
      methodCountsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'methodCounts');
    });
  }

  QueryBuilder<BreedingReportDataIsar, List<String>?, QQueryOperations>
      methodNamesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'methodNames');
    });
  }

  QueryBuilder<BreedingReportDataIsar, int?, QQueryOperations>
      pendingBreedingsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'pendingBreedings');
    });
  }

  QueryBuilder<BreedingReportDataIsar, String?, QQueryOperations>
      reportTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reportType');
    });
  }

  QueryBuilder<BreedingReportDataIsar, DateTime?, QQueryOperations>
      startDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'startDate');
    });
  }

  QueryBuilder<BreedingReportDataIsar, double?, QQueryOperations>
      successRateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'successRate');
    });
  }

  QueryBuilder<BreedingReportDataIsar, int?, QQueryOperations>
      successfulBreedingsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'successfulBreedings');
    });
  }

  QueryBuilder<BreedingReportDataIsar, String?, QQueryOperations>
      titleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'title');
    });
  }

  QueryBuilder<BreedingReportDataIsar, int?, QQueryOperations>
      totalBreedingsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalBreedings');
    });
  }

  QueryBuilder<BreedingReportDataIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
