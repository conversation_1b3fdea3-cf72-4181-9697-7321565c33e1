// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'cattle_report_data_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetCattleReportDataIsarCollection on Isar {
  IsarCollection<CattleReportDataIsar> get cattleReportDataIsars =>
      this.collection();
}

const CattleReportDataIsarSchema = CollectionSchema(
  name: r'CattleReportDataIsar',
  id: 373477532377473993,
  properties: {
    r'activeCattle': PropertySchema(
      id: 0,
      name: r'activeCattle',
      type: IsarType.long,
    ),
    r'averageAge': PropertySchema(
      id: 1,
      name: r'averageAge',
      type: IsarType.double,
    ),
    r'breedColors': PropertySchema(
      id: 2,
      name: r'breedColors',
      type: IsarType.longList,
    ),
    r'breedCounts': PropertySchema(
      id: 3,
      name: r'breedCounts',
      type: IsarType.longList,
    ),
    r'breedNames': PropertySchema(
      id: 4,
      name: r'breedNames',
      type: IsarType.stringList,
    ),
    r'bulls': PropertySchema(
      id: 5,
      name: r'bulls',
      type: IsarType.long,
    ),
    r'businessId': PropertySchema(
      id: 6,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'calves': PropertySchema(
      id: 7,
      name: r'calves',
      type: IsarType.long,
    ),
    r'cows': PropertySchema(
      id: 8,
      name: r'cows',
      type: IsarType.long,
    ),
    r'createdAt': PropertySchema(
      id: 9,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'endDate': PropertySchema(
      id: 10,
      name: r'endDate',
      type: IsarType.dateTime,
    ),
    r'filterCriteria': PropertySchema(
      id: 11,
      name: r'filterCriteria',
      type: IsarType.string,
    ),
    r'generatedAt': PropertySchema(
      id: 12,
      name: r'generatedAt',
      type: IsarType.dateTime,
    ),
    r'groupColors': PropertySchema(
      id: 13,
      name: r'groupColors',
      type: IsarType.longList,
    ),
    r'groupCounts': PropertySchema(
      id: 14,
      name: r'groupCounts',
      type: IsarType.longList,
    ),
    r'groupNames': PropertySchema(
      id: 15,
      name: r'groupNames',
      type: IsarType.stringList,
    ),
    r'heifers': PropertySchema(
      id: 16,
      name: r'heifers',
      type: IsarType.long,
    ),
    r'inactiveCattle': PropertySchema(
      id: 17,
      name: r'inactiveCattle',
      type: IsarType.long,
    ),
    r'reportType': PropertySchema(
      id: 18,
      name: r'reportType',
      type: IsarType.string,
    ),
    r'startDate': PropertySchema(
      id: 19,
      name: r'startDate',
      type: IsarType.dateTime,
    ),
    r'steers': PropertySchema(
      id: 20,
      name: r'steers',
      type: IsarType.long,
    ),
    r'title': PropertySchema(
      id: 21,
      name: r'title',
      type: IsarType.string,
    ),
    r'totalCattle': PropertySchema(
      id: 22,
      name: r'totalCattle',
      type: IsarType.long,
    ),
    r'totalWeight': PropertySchema(
      id: 23,
      name: r'totalWeight',
      type: IsarType.double,
    ),
    r'updatedAt': PropertySchema(
      id: 24,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _cattleReportDataIsarEstimateSize,
  serialize: _cattleReportDataIsarSerialize,
  deserialize: _cattleReportDataIsarDeserialize,
  deserializeProp: _cattleReportDataIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'reportType': IndexSchema(
      id: 3559997651334899995,
      name: r'reportType',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'reportType',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _cattleReportDataIsarGetId,
  getLinks: _cattleReportDataIsarGetLinks,
  attach: _cattleReportDataIsarAttach,
  version: '3.1.0+1',
);

int _cattleReportDataIsarEstimateSize(
  CattleReportDataIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.breedColors;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.breedCounts;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final list = object.breedNames;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.filterCriteria;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.groupColors;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.groupCounts;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final list = object.groupNames;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.reportType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.title;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  return bytesCount;
}

void _cattleReportDataIsarSerialize(
  CattleReportDataIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeLong(offsets[0], object.activeCattle);
  writer.writeDouble(offsets[1], object.averageAge);
  writer.writeLongList(offsets[2], object.breedColors);
  writer.writeLongList(offsets[3], object.breedCounts);
  writer.writeStringList(offsets[4], object.breedNames);
  writer.writeLong(offsets[5], object.bulls);
  writer.writeString(offsets[6], object.businessId);
  writer.writeLong(offsets[7], object.calves);
  writer.writeLong(offsets[8], object.cows);
  writer.writeDateTime(offsets[9], object.createdAt);
  writer.writeDateTime(offsets[10], object.endDate);
  writer.writeString(offsets[11], object.filterCriteria);
  writer.writeDateTime(offsets[12], object.generatedAt);
  writer.writeLongList(offsets[13], object.groupColors);
  writer.writeLongList(offsets[14], object.groupCounts);
  writer.writeStringList(offsets[15], object.groupNames);
  writer.writeLong(offsets[16], object.heifers);
  writer.writeLong(offsets[17], object.inactiveCattle);
  writer.writeString(offsets[18], object.reportType);
  writer.writeDateTime(offsets[19], object.startDate);
  writer.writeLong(offsets[20], object.steers);
  writer.writeString(offsets[21], object.title);
  writer.writeLong(offsets[22], object.totalCattle);
  writer.writeDouble(offsets[23], object.totalWeight);
  writer.writeDateTime(offsets[24], object.updatedAt);
}

CattleReportDataIsar _cattleReportDataIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = CattleReportDataIsar();
  object.activeCattle = reader.readLongOrNull(offsets[0]);
  object.averageAge = reader.readDoubleOrNull(offsets[1]);
  object.breedColors = reader.readLongList(offsets[2]);
  object.breedCounts = reader.readLongList(offsets[3]);
  object.breedNames = reader.readStringList(offsets[4]);
  object.bulls = reader.readLongOrNull(offsets[5]);
  object.businessId = reader.readStringOrNull(offsets[6]);
  object.calves = reader.readLongOrNull(offsets[7]);
  object.cows = reader.readLongOrNull(offsets[8]);
  object.createdAt = reader.readDateTimeOrNull(offsets[9]);
  object.endDate = reader.readDateTimeOrNull(offsets[10]);
  object.filterCriteria = reader.readStringOrNull(offsets[11]);
  object.generatedAt = reader.readDateTimeOrNull(offsets[12]);
  object.groupColors = reader.readLongList(offsets[13]);
  object.groupCounts = reader.readLongList(offsets[14]);
  object.groupNames = reader.readStringList(offsets[15]);
  object.heifers = reader.readLongOrNull(offsets[16]);
  object.id = id;
  object.inactiveCattle = reader.readLongOrNull(offsets[17]);
  object.reportType = reader.readStringOrNull(offsets[18]);
  object.startDate = reader.readDateTimeOrNull(offsets[19]);
  object.steers = reader.readLongOrNull(offsets[20]);
  object.title = reader.readStringOrNull(offsets[21]);
  object.totalCattle = reader.readLongOrNull(offsets[22]);
  object.totalWeight = reader.readDoubleOrNull(offsets[23]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[24]);
  return object;
}

P _cattleReportDataIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readLongOrNull(offset)) as P;
    case 1:
      return (reader.readDoubleOrNull(offset)) as P;
    case 2:
      return (reader.readLongList(offset)) as P;
    case 3:
      return (reader.readLongList(offset)) as P;
    case 4:
      return (reader.readStringList(offset)) as P;
    case 5:
      return (reader.readLongOrNull(offset)) as P;
    case 6:
      return (reader.readStringOrNull(offset)) as P;
    case 7:
      return (reader.readLongOrNull(offset)) as P;
    case 8:
      return (reader.readLongOrNull(offset)) as P;
    case 9:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 10:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 11:
      return (reader.readStringOrNull(offset)) as P;
    case 12:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 13:
      return (reader.readLongList(offset)) as P;
    case 14:
      return (reader.readLongList(offset)) as P;
    case 15:
      return (reader.readStringList(offset)) as P;
    case 16:
      return (reader.readLongOrNull(offset)) as P;
    case 17:
      return (reader.readLongOrNull(offset)) as P;
    case 18:
      return (reader.readStringOrNull(offset)) as P;
    case 19:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 20:
      return (reader.readLongOrNull(offset)) as P;
    case 21:
      return (reader.readStringOrNull(offset)) as P;
    case 22:
      return (reader.readLongOrNull(offset)) as P;
    case 23:
      return (reader.readDoubleOrNull(offset)) as P;
    case 24:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _cattleReportDataIsarGetId(CattleReportDataIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _cattleReportDataIsarGetLinks(
    CattleReportDataIsar object) {
  return [];
}

void _cattleReportDataIsarAttach(
    IsarCollection<dynamic> col, Id id, CattleReportDataIsar object) {
  object.id = id;
}

extension CattleReportDataIsarByIndex on IsarCollection<CattleReportDataIsar> {
  Future<CattleReportDataIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  CattleReportDataIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<CattleReportDataIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<CattleReportDataIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(CattleReportDataIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(CattleReportDataIsar object, {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(List<CattleReportDataIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<CattleReportDataIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension CattleReportDataIsarQueryWhereSort
    on QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QWhere> {
  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhere>
      anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension CattleReportDataIsarQueryWhere
    on QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QWhereClause> {
  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'reportType',
        value: [null],
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'reportType',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      reportTypeEqualTo(String? reportType) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'reportType',
        value: [reportType],
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      reportTypeNotEqualTo(String? reportType) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [],
              upper: [reportType],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [reportType],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [reportType],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [],
              upper: [reportType],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterWhereClause>
      businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension CattleReportDataIsarQueryFilter on QueryBuilder<CattleReportDataIsar,
    CattleReportDataIsar, QFilterCondition> {
  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> activeCattleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'activeCattle',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> activeCattleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'activeCattle',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> activeCattleEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'activeCattle',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> activeCattleGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'activeCattle',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> activeCattleLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'activeCattle',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> activeCattleBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'activeCattle',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> averageAgeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'averageAge',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> averageAgeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'averageAge',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> averageAgeEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'averageAge',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> averageAgeGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'averageAge',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> averageAgeLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'averageAge',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> averageAgeBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'averageAge',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedColorsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'breedColors',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedColorsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'breedColors',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedColorsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'breedColors',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedColorsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'breedColors',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedColorsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'breedColors',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedColorsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'breedColors',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedColorsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedColors',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedColorsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedColors',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedColorsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedColors',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedColorsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedColors',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedColorsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedColors',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedColorsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedColors',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedCountsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'breedCounts',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedCountsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'breedCounts',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedCountsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'breedCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedCountsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'breedCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedCountsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'breedCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedCountsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'breedCounts',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedCountsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedCounts',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedCountsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedCounts',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedCountsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedCounts',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedCountsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedCounts',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedCountsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedCounts',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedCountsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedCounts',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'breedNames',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'breedNames',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'breedNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'breedNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'breedNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'breedNames',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'breedNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'breedNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
          QAfterFilterCondition>
      breedNamesElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'breedNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
          QAfterFilterCondition>
      breedNamesElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'breedNames',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'breedNames',
        value: '',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'breedNames',
        value: '',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedNames',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedNames',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedNames',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedNames',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedNames',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> breedNamesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'breedNames',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> bullsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'bulls',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> bullsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'bulls',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> bullsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'bulls',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> bullsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'bulls',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> bullsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'bulls',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> bullsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'bulls',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
          QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
          QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> calvesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'calves',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> calvesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'calves',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> calvesEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'calves',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> calvesGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'calves',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> calvesLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'calves',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> calvesBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'calves',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> cowsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'cows',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> cowsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'cows',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> cowsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'cows',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> cowsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'cows',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> cowsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'cows',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> cowsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'cows',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> endDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> endDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> endDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> endDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> endDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> endDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'endDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> filterCriteriaEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> filterCriteriaGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> filterCriteriaLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> filterCriteriaBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'filterCriteria',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> filterCriteriaStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> filterCriteriaEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
          QAfterFilterCondition>
      filterCriteriaContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
          QAfterFilterCondition>
      filterCriteriaMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'filterCriteria',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> generatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> generatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> generatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> generatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> generatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> generatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'generatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupColorsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'groupColors',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupColorsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'groupColors',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupColorsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'groupColors',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupColorsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'groupColors',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupColorsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'groupColors',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupColorsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'groupColors',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupColorsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupColors',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupColorsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupColors',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupColorsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupColors',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupColorsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupColors',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupColorsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupColors',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupColorsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupColors',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupCountsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'groupCounts',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupCountsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'groupCounts',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupCountsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'groupCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupCountsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'groupCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupCountsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'groupCounts',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupCountsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'groupCounts',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupCountsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupCounts',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupCountsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupCounts',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupCountsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupCounts',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupCountsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupCounts',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupCountsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupCounts',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupCountsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupCounts',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'groupNames',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'groupNames',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'groupNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'groupNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'groupNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'groupNames',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'groupNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'groupNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
          QAfterFilterCondition>
      groupNamesElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'groupNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
          QAfterFilterCondition>
      groupNamesElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'groupNames',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'groupNames',
        value: '',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'groupNames',
        value: '',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupNames',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupNames',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupNames',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupNames',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupNames',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> groupNamesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'groupNames',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> heifersIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'heifers',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> heifersIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'heifers',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> heifersEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'heifers',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> heifersGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'heifers',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> heifersLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'heifers',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> heifersBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'heifers',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> inactiveCattleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'inactiveCattle',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> inactiveCattleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'inactiveCattle',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> inactiveCattleEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'inactiveCattle',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> inactiveCattleGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'inactiveCattle',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> inactiveCattleLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'inactiveCattle',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> inactiveCattleBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'inactiveCattle',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> reportTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> reportTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> reportTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> reportTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reportType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> reportTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> reportTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
          QAfterFilterCondition>
      reportTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
          QAfterFilterCondition>
      reportTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'reportType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> reportTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> reportTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> startDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> startDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> startDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> startDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> startDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> startDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'startDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> steersIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'steers',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> steersIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'steers',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> steersEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'steers',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> steersGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'steers',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> steersLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'steers',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> steersBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'steers',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> titleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> titleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> titleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> titleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> titleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> titleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'title',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> titleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> titleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
          QAfterFilterCondition>
      titleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
          QAfterFilterCondition>
      titleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'title',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> titleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> titleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> totalCattleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalCattle',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> totalCattleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalCattle',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> totalCattleEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalCattle',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> totalCattleGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalCattle',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> totalCattleLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalCattle',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> totalCattleBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalCattle',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> totalWeightIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalWeight',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> totalWeightIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalWeight',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> totalWeightEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> totalWeightGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> totalWeightLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalWeight',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> totalWeightBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalWeight',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar,
      QAfterFilterCondition> updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension CattleReportDataIsarQueryObject on QueryBuilder<CattleReportDataIsar,
    CattleReportDataIsar, QFilterCondition> {}

extension CattleReportDataIsarQueryLinks on QueryBuilder<CattleReportDataIsar,
    CattleReportDataIsar, QFilterCondition> {}

extension CattleReportDataIsarQuerySortBy
    on QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QSortBy> {
  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByActiveCattle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'activeCattle', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByActiveCattleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'activeCattle', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByAverageAge() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageAge', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByAverageAgeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageAge', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByBulls() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bulls', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByBullsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bulls', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByCalves() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calves', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByCalvesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calves', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByCows() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cows', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByCowsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cows', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByHeifers() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'heifers', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByHeifersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'heifers', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByInactiveCattle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inactiveCattle', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByInactiveCattleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inactiveCattle', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortBySteers() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'steers', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortBySteersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'steers', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByTotalCattle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalCattle', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByTotalCattleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalCattle', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByTotalWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWeight', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByTotalWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWeight', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension CattleReportDataIsarQuerySortThenBy
    on QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QSortThenBy> {
  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByActiveCattle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'activeCattle', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByActiveCattleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'activeCattle', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByAverageAge() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageAge', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByAverageAgeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageAge', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByBulls() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bulls', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByBullsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'bulls', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByCalves() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calves', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByCalvesDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'calves', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByCows() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cows', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByCowsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'cows', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByHeifers() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'heifers', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByHeifersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'heifers', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByInactiveCattle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inactiveCattle', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByInactiveCattleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'inactiveCattle', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenBySteers() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'steers', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenBySteersDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'steers', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByTotalCattle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalCattle', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByTotalCattleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalCattle', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByTotalWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWeight', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByTotalWeightDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalWeight', Sort.desc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QAfterSortBy>
      thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension CattleReportDataIsarQueryWhereDistinct
    on QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct> {
  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByActiveCattle() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'activeCattle');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByAverageAge() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'averageAge');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByBreedColors() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'breedColors');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByBreedCounts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'breedCounts');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByBreedNames() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'breedNames');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByBulls() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'bulls');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByCalves() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'calves');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByCows() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'cows');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'endDate');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByFilterCriteria({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'filterCriteria',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'generatedAt');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByGroupColors() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'groupColors');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByGroupCounts() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'groupCounts');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByGroupNames() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'groupNames');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByHeifers() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'heifers');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByInactiveCattle() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'inactiveCattle');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByReportType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reportType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'startDate');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctBySteers() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'steers');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByTitle({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'title', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByTotalCattle() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalCattle');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByTotalWeight() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalWeight');
    });
  }

  QueryBuilder<CattleReportDataIsar, CattleReportDataIsar, QDistinct>
      distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension CattleReportDataIsarQueryProperty on QueryBuilder<
    CattleReportDataIsar, CattleReportDataIsar, QQueryProperty> {
  QueryBuilder<CattleReportDataIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<CattleReportDataIsar, int?, QQueryOperations>
      activeCattleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'activeCattle');
    });
  }

  QueryBuilder<CattleReportDataIsar, double?, QQueryOperations>
      averageAgeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'averageAge');
    });
  }

  QueryBuilder<CattleReportDataIsar, List<int>?, QQueryOperations>
      breedColorsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'breedColors');
    });
  }

  QueryBuilder<CattleReportDataIsar, List<int>?, QQueryOperations>
      breedCountsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'breedCounts');
    });
  }

  QueryBuilder<CattleReportDataIsar, List<String>?, QQueryOperations>
      breedNamesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'breedNames');
    });
  }

  QueryBuilder<CattleReportDataIsar, int?, QQueryOperations> bullsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'bulls');
    });
  }

  QueryBuilder<CattleReportDataIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<CattleReportDataIsar, int?, QQueryOperations> calvesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'calves');
    });
  }

  QueryBuilder<CattleReportDataIsar, int?, QQueryOperations> cowsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'cows');
    });
  }

  QueryBuilder<CattleReportDataIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<CattleReportDataIsar, DateTime?, QQueryOperations>
      endDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'endDate');
    });
  }

  QueryBuilder<CattleReportDataIsar, String?, QQueryOperations>
      filterCriteriaProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'filterCriteria');
    });
  }

  QueryBuilder<CattleReportDataIsar, DateTime?, QQueryOperations>
      generatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'generatedAt');
    });
  }

  QueryBuilder<CattleReportDataIsar, List<int>?, QQueryOperations>
      groupColorsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'groupColors');
    });
  }

  QueryBuilder<CattleReportDataIsar, List<int>?, QQueryOperations>
      groupCountsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'groupCounts');
    });
  }

  QueryBuilder<CattleReportDataIsar, List<String>?, QQueryOperations>
      groupNamesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'groupNames');
    });
  }

  QueryBuilder<CattleReportDataIsar, int?, QQueryOperations> heifersProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'heifers');
    });
  }

  QueryBuilder<CattleReportDataIsar, int?, QQueryOperations>
      inactiveCattleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'inactiveCattle');
    });
  }

  QueryBuilder<CattleReportDataIsar, String?, QQueryOperations>
      reportTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reportType');
    });
  }

  QueryBuilder<CattleReportDataIsar, DateTime?, QQueryOperations>
      startDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'startDate');
    });
  }

  QueryBuilder<CattleReportDataIsar, int?, QQueryOperations> steersProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'steers');
    });
  }

  QueryBuilder<CattleReportDataIsar, String?, QQueryOperations>
      titleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'title');
    });
  }

  QueryBuilder<CattleReportDataIsar, int?, QQueryOperations>
      totalCattleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalCattle');
    });
  }

  QueryBuilder<CattleReportDataIsar, double?, QQueryOperations>
      totalWeightProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalWeight');
    });
  }

  QueryBuilder<CattleReportDataIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
