// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'transactions_report_data_isar.dart';

// **************************************************************************
// IsarCollectionGenerator
// **************************************************************************

// coverage:ignore-file
// ignore_for_file: duplicate_ignore, non_constant_identifier_names, constant_identifier_names, invalid_use_of_protected_member, unnecessary_cast, prefer_const_constructors, lines_longer_than_80_chars, require_trailing_commas, inference_failure_on_function_invocation, unnecessary_parenthesis, unnecessary_raw_strings, unnecessary_null_checks, join_return_with_assignment, prefer_final_locals, avoid_js_rounded_ints, avoid_positional_boolean_parameters, always_specify_types

extension GetTransactionsReportDataIsarCollection on Isar {
  IsarCollection<TransactionsReportDataIsar> get transactionsReportDataIsars =>
      this.collection();
}

const TransactionsReportDataIsarSchema = CollectionSchema(
  name: r'TransactionsReportDataIsar',
  id: -8558089292941268783,
  properties: {
    r'averageTransactionAmount': PropertySchema(
      id: 0,
      name: r'averageTransactionAmount',
      type: IsarType.double,
    ),
    r'businessId': PropertySchema(
      id: 1,
      name: r'businessId',
      type: IsarType.string,
    ),
    r'categoryColors': PropertySchema(
      id: 2,
      name: r'categoryColors',
      type: IsarType.longList,
    ),
    r'categoryNames': PropertySchema(
      id: 3,
      name: r'categoryNames',
      type: IsarType.stringList,
    ),
    r'createdAt': PropertySchema(
      id: 4,
      name: r'createdAt',
      type: IsarType.dateTime,
    ),
    r'endDate': PropertySchema(
      id: 5,
      name: r'endDate',
      type: IsarType.dateTime,
    ),
    r'expenseByCategory': PropertySchema(
      id: 6,
      name: r'expenseByCategory',
      type: IsarType.doubleList,
    ),
    r'expenseValues': PropertySchema(
      id: 7,
      name: r'expenseValues',
      type: IsarType.doubleList,
    ),
    r'filterCriteria': PropertySchema(
      id: 8,
      name: r'filterCriteria',
      type: IsarType.string,
    ),
    r'generatedAt': PropertySchema(
      id: 9,
      name: r'generatedAt',
      type: IsarType.dateTime,
    ),
    r'incomeByCategory': PropertySchema(
      id: 10,
      name: r'incomeByCategory',
      type: IsarType.doubleList,
    ),
    r'incomeValues': PropertySchema(
      id: 11,
      name: r'incomeValues',
      type: IsarType.doubleList,
    ),
    r'netBalance': PropertySchema(
      id: 12,
      name: r'netBalance',
      type: IsarType.double,
    ),
    r'reportType': PropertySchema(
      id: 13,
      name: r'reportType',
      type: IsarType.string,
    ),
    r'startDate': PropertySchema(
      id: 14,
      name: r'startDate',
      type: IsarType.dateTime,
    ),
    r'title': PropertySchema(
      id: 15,
      name: r'title',
      type: IsarType.string,
    ),
    r'totalExpense': PropertySchema(
      id: 16,
      name: r'totalExpense',
      type: IsarType.double,
    ),
    r'totalIncome': PropertySchema(
      id: 17,
      name: r'totalIncome',
      type: IsarType.double,
    ),
    r'totalTransactions': PropertySchema(
      id: 18,
      name: r'totalTransactions',
      type: IsarType.long,
    ),
    r'transactionDates': PropertySchema(
      id: 19,
      name: r'transactionDates',
      type: IsarType.dateTimeList,
    ),
    r'updatedAt': PropertySchema(
      id: 20,
      name: r'updatedAt',
      type: IsarType.dateTime,
    )
  },
  estimateSize: _transactionsReportDataIsarEstimateSize,
  serialize: _transactionsReportDataIsarSerialize,
  deserialize: _transactionsReportDataIsarDeserialize,
  deserializeProp: _transactionsReportDataIsarDeserializeProp,
  idName: r'id',
  indexes: {
    r'reportType': IndexSchema(
      id: 3559997651334899995,
      name: r'reportType',
      unique: false,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'reportType',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    ),
    r'businessId': IndexSchema(
      id: 2228048290814354584,
      name: r'businessId',
      unique: true,
      replace: false,
      properties: [
        IndexPropertySchema(
          name: r'businessId',
          type: IndexType.hash,
          caseSensitive: true,
        )
      ],
    )
  },
  links: {},
  embeddedSchemas: {},
  getId: _transactionsReportDataIsarGetId,
  getLinks: _transactionsReportDataIsarGetLinks,
  attach: _transactionsReportDataIsarAttach,
  version: '3.1.0+1',
);

int _transactionsReportDataIsarEstimateSize(
  TransactionsReportDataIsar object,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  var bytesCount = offsets.last;
  {
    final value = object.businessId;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.categoryColors;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final list = object.categoryNames;
    if (list != null) {
      bytesCount += 3 + list.length * 3;
      {
        for (var i = 0; i < list.length; i++) {
          final value = list[i];
          bytesCount += value.length * 3;
        }
      }
    }
  }
  {
    final value = object.expenseByCategory;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.expenseValues;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.filterCriteria;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.incomeByCategory;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.incomeValues;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  {
    final value = object.reportType;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.title;
    if (value != null) {
      bytesCount += 3 + value.length * 3;
    }
  }
  {
    final value = object.transactionDates;
    if (value != null) {
      bytesCount += 3 + value.length * 8;
    }
  }
  return bytesCount;
}

void _transactionsReportDataIsarSerialize(
  TransactionsReportDataIsar object,
  IsarWriter writer,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  writer.writeDouble(offsets[0], object.averageTransactionAmount);
  writer.writeString(offsets[1], object.businessId);
  writer.writeLongList(offsets[2], object.categoryColors);
  writer.writeStringList(offsets[3], object.categoryNames);
  writer.writeDateTime(offsets[4], object.createdAt);
  writer.writeDateTime(offsets[5], object.endDate);
  writer.writeDoubleList(offsets[6], object.expenseByCategory);
  writer.writeDoubleList(offsets[7], object.expenseValues);
  writer.writeString(offsets[8], object.filterCriteria);
  writer.writeDateTime(offsets[9], object.generatedAt);
  writer.writeDoubleList(offsets[10], object.incomeByCategory);
  writer.writeDoubleList(offsets[11], object.incomeValues);
  writer.writeDouble(offsets[12], object.netBalance);
  writer.writeString(offsets[13], object.reportType);
  writer.writeDateTime(offsets[14], object.startDate);
  writer.writeString(offsets[15], object.title);
  writer.writeDouble(offsets[16], object.totalExpense);
  writer.writeDouble(offsets[17], object.totalIncome);
  writer.writeLong(offsets[18], object.totalTransactions);
  writer.writeDateTimeList(offsets[19], object.transactionDates);
  writer.writeDateTime(offsets[20], object.updatedAt);
}

TransactionsReportDataIsar _transactionsReportDataIsarDeserialize(
  Id id,
  IsarReader reader,
  List<int> offsets,
  Map<Type, List<int>> allOffsets,
) {
  final object = TransactionsReportDataIsar();
  object.averageTransactionAmount = reader.readDoubleOrNull(offsets[0]);
  object.businessId = reader.readStringOrNull(offsets[1]);
  object.categoryColors = reader.readLongList(offsets[2]);
  object.categoryNames = reader.readStringList(offsets[3]);
  object.createdAt = reader.readDateTimeOrNull(offsets[4]);
  object.endDate = reader.readDateTimeOrNull(offsets[5]);
  object.expenseByCategory = reader.readDoubleList(offsets[6]);
  object.expenseValues = reader.readDoubleList(offsets[7]);
  object.filterCriteria = reader.readStringOrNull(offsets[8]);
  object.generatedAt = reader.readDateTimeOrNull(offsets[9]);
  object.id = id;
  object.incomeByCategory = reader.readDoubleList(offsets[10]);
  object.incomeValues = reader.readDoubleList(offsets[11]);
  object.netBalance = reader.readDoubleOrNull(offsets[12]);
  object.reportType = reader.readStringOrNull(offsets[13]);
  object.startDate = reader.readDateTimeOrNull(offsets[14]);
  object.title = reader.readStringOrNull(offsets[15]);
  object.totalExpense = reader.readDoubleOrNull(offsets[16]);
  object.totalIncome = reader.readDoubleOrNull(offsets[17]);
  object.totalTransactions = reader.readLongOrNull(offsets[18]);
  object.transactionDates = reader.readDateTimeList(offsets[19]);
  object.updatedAt = reader.readDateTimeOrNull(offsets[20]);
  return object;
}

P _transactionsReportDataIsarDeserializeProp<P>(
  IsarReader reader,
  int propertyId,
  int offset,
  Map<Type, List<int>> allOffsets,
) {
  switch (propertyId) {
    case 0:
      return (reader.readDoubleOrNull(offset)) as P;
    case 1:
      return (reader.readStringOrNull(offset)) as P;
    case 2:
      return (reader.readLongList(offset)) as P;
    case 3:
      return (reader.readStringList(offset)) as P;
    case 4:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 5:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 6:
      return (reader.readDoubleList(offset)) as P;
    case 7:
      return (reader.readDoubleList(offset)) as P;
    case 8:
      return (reader.readStringOrNull(offset)) as P;
    case 9:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 10:
      return (reader.readDoubleList(offset)) as P;
    case 11:
      return (reader.readDoubleList(offset)) as P;
    case 12:
      return (reader.readDoubleOrNull(offset)) as P;
    case 13:
      return (reader.readStringOrNull(offset)) as P;
    case 14:
      return (reader.readDateTimeOrNull(offset)) as P;
    case 15:
      return (reader.readStringOrNull(offset)) as P;
    case 16:
      return (reader.readDoubleOrNull(offset)) as P;
    case 17:
      return (reader.readDoubleOrNull(offset)) as P;
    case 18:
      return (reader.readLongOrNull(offset)) as P;
    case 19:
      return (reader.readDateTimeList(offset)) as P;
    case 20:
      return (reader.readDateTimeOrNull(offset)) as P;
    default:
      throw IsarError('Unknown property with id $propertyId');
  }
}

Id _transactionsReportDataIsarGetId(TransactionsReportDataIsar object) {
  return object.id;
}

List<IsarLinkBase<dynamic>> _transactionsReportDataIsarGetLinks(
    TransactionsReportDataIsar object) {
  return [];
}

void _transactionsReportDataIsarAttach(
    IsarCollection<dynamic> col, Id id, TransactionsReportDataIsar object) {
  object.id = id;
}

extension TransactionsReportDataIsarByIndex
    on IsarCollection<TransactionsReportDataIsar> {
  Future<TransactionsReportDataIsar?> getByBusinessId(String? businessId) {
    return getByIndex(r'businessId', [businessId]);
  }

  TransactionsReportDataIsar? getByBusinessIdSync(String? businessId) {
    return getByIndexSync(r'businessId', [businessId]);
  }

  Future<bool> deleteByBusinessId(String? businessId) {
    return deleteByIndex(r'businessId', [businessId]);
  }

  bool deleteByBusinessIdSync(String? businessId) {
    return deleteByIndexSync(r'businessId', [businessId]);
  }

  Future<List<TransactionsReportDataIsar?>> getAllByBusinessId(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndex(r'businessId', values);
  }

  List<TransactionsReportDataIsar?> getAllByBusinessIdSync(
      List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return getAllByIndexSync(r'businessId', values);
  }

  Future<int> deleteAllByBusinessId(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndex(r'businessId', values);
  }

  int deleteAllByBusinessIdSync(List<String?> businessIdValues) {
    final values = businessIdValues.map((e) => [e]).toList();
    return deleteAllByIndexSync(r'businessId', values);
  }

  Future<Id> putByBusinessId(TransactionsReportDataIsar object) {
    return putByIndex(r'businessId', object);
  }

  Id putByBusinessIdSync(TransactionsReportDataIsar object,
      {bool saveLinks = true}) {
    return putByIndexSync(r'businessId', object, saveLinks: saveLinks);
  }

  Future<List<Id>> putAllByBusinessId(
      List<TransactionsReportDataIsar> objects) {
    return putAllByIndex(r'businessId', objects);
  }

  List<Id> putAllByBusinessIdSync(List<TransactionsReportDataIsar> objects,
      {bool saveLinks = true}) {
    return putAllByIndexSync(r'businessId', objects, saveLinks: saveLinks);
  }
}

extension TransactionsReportDataIsarQueryWhereSort on QueryBuilder<
    TransactionsReportDataIsar, TransactionsReportDataIsar, QWhere> {
  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhere> anyId() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(const IdWhereClause.any());
    });
  }
}

extension TransactionsReportDataIsarQueryWhere on QueryBuilder<
    TransactionsReportDataIsar, TransactionsReportDataIsar, QWhereClause> {
  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> idEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: id,
        upper: id,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> idNotEqualTo(Id id) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            )
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            );
      } else {
        return query
            .addWhereClause(
              IdWhereClause.greaterThan(lower: id, includeLower: false),
            )
            .addWhereClause(
              IdWhereClause.lessThan(upper: id, includeUpper: false),
            );
      }
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> idGreaterThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.greaterThan(lower: id, includeLower: include),
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> idLessThan(Id id, {bool include = false}) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(
        IdWhereClause.lessThan(upper: id, includeUpper: include),
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> idBetween(
    Id lowerId,
    Id upperId, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IdWhereClause.between(
        lower: lowerId,
        includeLower: includeLower,
        upper: upperId,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'reportType',
        value: [null],
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'reportType',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> reportTypeEqualTo(String? reportType) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'reportType',
        value: [reportType],
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> reportTypeNotEqualTo(String? reportType) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [],
              upper: [reportType],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [reportType],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [reportType],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'reportType',
              lower: [],
              upper: [reportType],
              includeUpper: false,
            ));
      }
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [null],
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.between(
        indexName: r'businessId',
        lower: [null],
        includeLower: false,
        upper: [],
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> businessIdEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      return query.addWhereClause(IndexWhereClause.equalTo(
        indexName: r'businessId',
        value: [businessId],
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterWhereClause> businessIdNotEqualTo(String? businessId) {
    return QueryBuilder.apply(this, (query) {
      if (query.whereSort == Sort.asc) {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ));
      } else {
        return query
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [businessId],
              includeLower: false,
              upper: [],
            ))
            .addWhereClause(IndexWhereClause.between(
              indexName: r'businessId',
              lower: [],
              upper: [businessId],
              includeUpper: false,
            ));
      }
    });
  }
}

extension TransactionsReportDataIsarQueryFilter on QueryBuilder<
    TransactionsReportDataIsar, TransactionsReportDataIsar, QFilterCondition> {
  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> averageTransactionAmountIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'averageTransactionAmount',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> averageTransactionAmountIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'averageTransactionAmount',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> averageTransactionAmountEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'averageTransactionAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> averageTransactionAmountGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'averageTransactionAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> averageTransactionAmountLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'averageTransactionAmount',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> averageTransactionAmountBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'averageTransactionAmount',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> businessIdIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> businessIdIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'businessId',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> businessIdEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> businessIdGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> businessIdLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> businessIdBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'businessId',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> businessIdStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> businessIdEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
          QAfterFilterCondition>
      businessIdContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'businessId',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
          QAfterFilterCondition>
      businessIdMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'businessId',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> businessIdIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> businessIdIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'businessId',
        value: '',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryColorsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'categoryColors',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryColorsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'categoryColors',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryColorsElementEqualTo(int value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'categoryColors',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryColorsElementGreaterThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'categoryColors',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryColorsElementLessThan(
    int value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'categoryColors',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryColorsElementBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'categoryColors',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryColorsLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'categoryColors',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryColorsIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'categoryColors',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryColorsIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'categoryColors',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryColorsLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'categoryColors',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryColorsLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'categoryColors',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryColorsLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'categoryColors',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'categoryNames',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'categoryNames',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesElementEqualTo(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'categoryNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesElementGreaterThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'categoryNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesElementLessThan(
    String value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'categoryNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesElementBetween(
    String lower,
    String upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'categoryNames',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesElementStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'categoryNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesElementEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'categoryNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
          QAfterFilterCondition>
      categoryNamesElementContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'categoryNames',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
          QAfterFilterCondition>
      categoryNamesElementMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'categoryNames',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesElementIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'categoryNames',
        value: '',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesElementIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'categoryNames',
        value: '',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'categoryNames',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'categoryNames',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'categoryNames',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'categoryNames',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'categoryNames',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> categoryNamesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'categoryNames',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> createdAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> createdAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'createdAt',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> createdAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> createdAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> createdAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'createdAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> createdAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'createdAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> endDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> endDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'endDate',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> endDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> endDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> endDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'endDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> endDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'endDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseByCategoryIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'expenseByCategory',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseByCategoryIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'expenseByCategory',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseByCategoryElementEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'expenseByCategory',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseByCategoryElementGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'expenseByCategory',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseByCategoryElementLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'expenseByCategory',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseByCategoryElementBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'expenseByCategory',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseByCategoryLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'expenseByCategory',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseByCategoryIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'expenseByCategory',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseByCategoryIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'expenseByCategory',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseByCategoryLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'expenseByCategory',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseByCategoryLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'expenseByCategory',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseByCategoryLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'expenseByCategory',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseValuesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'expenseValues',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseValuesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'expenseValues',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseValuesElementEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'expenseValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseValuesElementGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'expenseValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseValuesElementLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'expenseValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseValuesElementBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'expenseValues',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseValuesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'expenseValues',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseValuesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'expenseValues',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseValuesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'expenseValues',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseValuesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'expenseValues',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseValuesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'expenseValues',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> expenseValuesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'expenseValues',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'filterCriteria',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> filterCriteriaEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> filterCriteriaGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> filterCriteriaLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> filterCriteriaBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'filterCriteria',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> filterCriteriaStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> filterCriteriaEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
          QAfterFilterCondition>
      filterCriteriaContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'filterCriteria',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
          QAfterFilterCondition>
      filterCriteriaMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'filterCriteria',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> filterCriteriaIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'filterCriteria',
        value: '',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> generatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> generatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'generatedAt',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> generatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> generatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> generatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'generatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> generatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'generatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> idEqualTo(Id value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> idGreaterThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> idLessThan(
    Id value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'id',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> idBetween(
    Id lower,
    Id upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'id',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeByCategoryIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'incomeByCategory',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeByCategoryIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'incomeByCategory',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeByCategoryElementEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'incomeByCategory',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeByCategoryElementGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'incomeByCategory',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeByCategoryElementLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'incomeByCategory',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeByCategoryElementBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'incomeByCategory',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeByCategoryLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'incomeByCategory',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeByCategoryIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'incomeByCategory',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeByCategoryIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'incomeByCategory',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeByCategoryLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'incomeByCategory',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeByCategoryLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'incomeByCategory',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeByCategoryLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'incomeByCategory',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeValuesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'incomeValues',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeValuesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'incomeValues',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeValuesElementEqualTo(
    double value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'incomeValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeValuesElementGreaterThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'incomeValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeValuesElementLessThan(
    double value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'incomeValues',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeValuesElementBetween(
    double lower,
    double upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'incomeValues',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeValuesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'incomeValues',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeValuesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'incomeValues',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeValuesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'incomeValues',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeValuesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'incomeValues',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeValuesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'incomeValues',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> incomeValuesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'incomeValues',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> netBalanceIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'netBalance',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> netBalanceIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'netBalance',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> netBalanceEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'netBalance',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> netBalanceGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'netBalance',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> netBalanceLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'netBalance',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> netBalanceBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'netBalance',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> reportTypeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> reportTypeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'reportType',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> reportTypeEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> reportTypeGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> reportTypeLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> reportTypeBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'reportType',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> reportTypeStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> reportTypeEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
          QAfterFilterCondition>
      reportTypeContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'reportType',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
          QAfterFilterCondition>
      reportTypeMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'reportType',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> reportTypeIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> reportTypeIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'reportType',
        value: '',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> startDateIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> startDateIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'startDate',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> startDateEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> startDateGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> startDateLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'startDate',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> startDateBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'startDate',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> titleIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> titleIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'title',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> titleEqualTo(
    String? value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> titleGreaterThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> titleLessThan(
    String? value, {
    bool include = false,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> titleBetween(
    String? lower,
    String? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'title',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> titleStartsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.startsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> titleEndsWith(
    String value, {
    bool caseSensitive = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.endsWith(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
          QAfterFilterCondition>
      titleContains(String value, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.contains(
        property: r'title',
        value: value,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
          QAfterFilterCondition>
      titleMatches(String pattern, {bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.matches(
        property: r'title',
        wildcard: pattern,
        caseSensitive: caseSensitive,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> titleIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> titleIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        property: r'title',
        value: '',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalExpenseIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalExpense',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalExpenseIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalExpense',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalExpenseEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalExpense',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalExpenseGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalExpense',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalExpenseLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalExpense',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalExpenseBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalExpense',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalIncomeIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalIncome',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalIncomeIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalIncome',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalIncomeEqualTo(
    double? value, {
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalIncome',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalIncomeGreaterThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalIncome',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalIncomeLessThan(
    double? value, {
    bool include = false,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalIncome',
        value: value,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalIncomeBetween(
    double? lower,
    double? upper, {
    bool includeLower = true,
    bool includeUpper = true,
    double epsilon = Query.epsilon,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalIncome',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
        epsilon: epsilon,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalTransactionsIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'totalTransactions',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalTransactionsIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'totalTransactions',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalTransactionsEqualTo(int? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'totalTransactions',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalTransactionsGreaterThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'totalTransactions',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalTransactionsLessThan(
    int? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'totalTransactions',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> totalTransactionsBetween(
    int? lower,
    int? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'totalTransactions',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> transactionDatesIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'transactionDates',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> transactionDatesIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'transactionDates',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> transactionDatesElementEqualTo(DateTime value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'transactionDates',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> transactionDatesElementGreaterThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'transactionDates',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> transactionDatesElementLessThan(
    DateTime value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'transactionDates',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> transactionDatesElementBetween(
    DateTime lower,
    DateTime upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'transactionDates',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> transactionDatesLengthEqualTo(int length) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'transactionDates',
        length,
        true,
        length,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> transactionDatesIsEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'transactionDates',
        0,
        true,
        0,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> transactionDatesIsNotEmpty() {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'transactionDates',
        0,
        false,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> transactionDatesLengthLessThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'transactionDates',
        0,
        true,
        length,
        include,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> transactionDatesLengthGreaterThan(
    int length, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'transactionDates',
        length,
        include,
        999999,
        true,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> transactionDatesLengthBetween(
    int lower,
    int upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.listLength(
        r'transactionDates',
        lower,
        includeLower,
        upper,
        includeUpper,
      );
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> updatedAtIsNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> updatedAtIsNotNull() {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(const FilterCondition.isNotNull(
        property: r'updatedAt',
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> updatedAtEqualTo(DateTime? value) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.equalTo(
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> updatedAtGreaterThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.greaterThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> updatedAtLessThan(
    DateTime? value, {
    bool include = false,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.lessThan(
        include: include,
        property: r'updatedAt',
        value: value,
      ));
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterFilterCondition> updatedAtBetween(
    DateTime? lower,
    DateTime? upper, {
    bool includeLower = true,
    bool includeUpper = true,
  }) {
    return QueryBuilder.apply(this, (query) {
      return query.addFilterCondition(FilterCondition.between(
        property: r'updatedAt',
        lower: lower,
        includeLower: includeLower,
        upper: upper,
        includeUpper: includeUpper,
      ));
    });
  }
}

extension TransactionsReportDataIsarQueryObject on QueryBuilder<
    TransactionsReportDataIsar, TransactionsReportDataIsar, QFilterCondition> {}

extension TransactionsReportDataIsarQueryLinks on QueryBuilder<
    TransactionsReportDataIsar, TransactionsReportDataIsar, QFilterCondition> {}

extension TransactionsReportDataIsarQuerySortBy on QueryBuilder<
    TransactionsReportDataIsar, TransactionsReportDataIsar, QSortBy> {
  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByAverageTransactionAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageTransactionAmount', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByAverageTransactionAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageTransactionAmount', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByNetBalance() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'netBalance', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByNetBalanceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'netBalance', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByTotalExpense() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalExpense', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByTotalExpenseDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalExpense', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByTotalIncome() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalIncome', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByTotalIncomeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalIncome', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByTotalTransactions() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalTransactions', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByTotalTransactionsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalTransactions', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> sortByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension TransactionsReportDataIsarQuerySortThenBy on QueryBuilder<
    TransactionsReportDataIsar, TransactionsReportDataIsar, QSortThenBy> {
  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByAverageTransactionAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageTransactionAmount', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByAverageTransactionAmountDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'averageTransactionAmount', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByBusinessId() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByBusinessIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'businessId', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByCreatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'createdAt', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByEndDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'endDate', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByFilterCriteria() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByFilterCriteriaDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'filterCriteria', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByGeneratedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'generatedAt', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenById() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByIdDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'id', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByNetBalance() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'netBalance', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByNetBalanceDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'netBalance', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByReportType() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByReportTypeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'reportType', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByStartDateDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'startDate', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByTitle() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByTitleDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'title', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByTotalExpense() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalExpense', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByTotalExpenseDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalExpense', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByTotalIncome() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalIncome', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByTotalIncomeDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalIncome', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByTotalTransactions() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalTransactions', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByTotalTransactionsDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'totalTransactions', Sort.desc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.asc);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QAfterSortBy> thenByUpdatedAtDesc() {
    return QueryBuilder.apply(this, (query) {
      return query.addSortBy(r'updatedAt', Sort.desc);
    });
  }
}

extension TransactionsReportDataIsarQueryWhereDistinct on QueryBuilder<
    TransactionsReportDataIsar, TransactionsReportDataIsar, QDistinct> {
  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByAverageTransactionAmount() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'averageTransactionAmount');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByBusinessId({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'businessId', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByCategoryColors() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'categoryColors');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByCategoryNames() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'categoryNames');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByCreatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'createdAt');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByEndDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'endDate');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByExpenseByCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'expenseByCategory');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByExpenseValues() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'expenseValues');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByFilterCriteria({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'filterCriteria',
          caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByGeneratedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'generatedAt');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByIncomeByCategory() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'incomeByCategory');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByIncomeValues() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'incomeValues');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByNetBalance() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'netBalance');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByReportType({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'reportType', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByStartDate() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'startDate');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByTitle({bool caseSensitive = true}) {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'title', caseSensitive: caseSensitive);
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByTotalExpense() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalExpense');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByTotalIncome() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalIncome');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByTotalTransactions() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'totalTransactions');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByTransactionDates() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'transactionDates');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, TransactionsReportDataIsar,
      QDistinct> distinctByUpdatedAt() {
    return QueryBuilder.apply(this, (query) {
      return query.addDistinctBy(r'updatedAt');
    });
  }
}

extension TransactionsReportDataIsarQueryProperty on QueryBuilder<
    TransactionsReportDataIsar, TransactionsReportDataIsar, QQueryProperty> {
  QueryBuilder<TransactionsReportDataIsar, int, QQueryOperations> idProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'id');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, double?, QQueryOperations>
      averageTransactionAmountProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'averageTransactionAmount');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, String?, QQueryOperations>
      businessIdProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'businessId');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, List<int>?, QQueryOperations>
      categoryColorsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'categoryColors');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, List<String>?, QQueryOperations>
      categoryNamesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'categoryNames');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, DateTime?, QQueryOperations>
      createdAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'createdAt');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, DateTime?, QQueryOperations>
      endDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'endDate');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, List<double>?, QQueryOperations>
      expenseByCategoryProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'expenseByCategory');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, List<double>?, QQueryOperations>
      expenseValuesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'expenseValues');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, String?, QQueryOperations>
      filterCriteriaProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'filterCriteria');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, DateTime?, QQueryOperations>
      generatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'generatedAt');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, List<double>?, QQueryOperations>
      incomeByCategoryProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'incomeByCategory');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, List<double>?, QQueryOperations>
      incomeValuesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'incomeValues');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, double?, QQueryOperations>
      netBalanceProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'netBalance');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, String?, QQueryOperations>
      reportTypeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'reportType');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, DateTime?, QQueryOperations>
      startDateProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'startDate');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, String?, QQueryOperations>
      titleProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'title');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, double?, QQueryOperations>
      totalExpenseProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalExpense');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, double?, QQueryOperations>
      totalIncomeProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalIncome');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, int?, QQueryOperations>
      totalTransactionsProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'totalTransactions');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, List<DateTime>?, QQueryOperations>
      transactionDatesProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'transactionDates');
    });
  }

  QueryBuilder<TransactionsReportDataIsar, DateTime?, QQueryOperations>
      updatedAtProperty() {
    return QueryBuilder.apply(this, (query) {
      return query.addPropertyName(r'updatedAt');
    });
  }
}
