import 'package:flutter/material.dart';
import '../../../routes/app_routes.dart';

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Reports'),
        backgroundColor: const Color(0xFF2E7D32),
        foregroundColor: Colors.white,
      ),
      body: GridView.count(
        padding: const EdgeInsets.all(16.0),
        crossAxisCount: 2,
        crossAxisSpacing: 16.0,
        mainAxisSpacing: 16.0,
        childAspectRatio: 1.0,
        children: [
          _buildReportCard(
            context,
            'Milk Reports',
            Icons.water_drop,
            AppRoutes.milkReport,
            const Color(0xFF1976D2),
          ),
          _buildReportCard(
            context,
            'Breeding Reports',
            Icons.pets,
            AppRoutes.breedingReport,
            const Color(0xFF2E7D32),
          ),
          _buildReportCard(
            context,
            'Events Reports',
            Icons.event_note,
            AppRoutes.eventsReport,
            const Color(0xFFD32F2F),
          ),
          _buildReportCard(
            context,
            'Transactions Reports',
            Icons.account_balance_wallet,
            AppRoutes.transactionsReport,
            const Color(0xFF7B1FA2),
          ),
          _buildReportCard(
            context,
            'Cattle Reports',
            Icons.view_list_rounded,
            AppRoutes.cattleReport,
            const Color(0xFF00796B),
          ),
          _buildReportCard(
            context,
            'Pregnancies Reports',
            Icons.pregnant_woman,
            AppRoutes.pregnanciesReport,
            const Color(0xFFE65100),
          ),
          _buildReportCard(
            context,
            'Weight Reports',
            Icons.monitor_weight_outlined,
            AppRoutes.weightReport,
            const Color.fromARGB(255, 35, 146, 197),
          ),
        ],
      ),
    );
  }

  Widget _buildReportCard(
    BuildContext context,
    String title,
    IconData icon,
    String route,
    Color color,
  ) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: () => Navigator.pushNamed(context, route),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 48,
              color: color,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
