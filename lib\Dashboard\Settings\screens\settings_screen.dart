import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../services/settings_handler.dart';
import 'terms_of_service_screen.dart';
import 'privacy_policy_screen.dart';
import '../../Profile/screens/profile_screen.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({Key? key}) : super(key: key);

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final SettingsHandler _settingsHandler = GetIt.instance<SettingsHandler>();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initSettings();
  }

  Future<void> _initSettings() async {
    if (!_settingsHandler.isInitialized) {
      await _settingsHandler.init();
    }
    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
      body: AnimatedBuilder(
        animation: _settingsHandler,
        builder: (context, child) {
          return ListView(
            children: [
              // Profile Section
              _buildSectionHeader('Profile'),
              ListTile(
                leading: const Icon(Icons.person),
                title: const Text('Profile Information'),
                subtitle: const Text('Manage your profile details'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ProfileScreen(),
                    ),
                  );
                },
              ),
              const Divider(),

              // Appearance Section
              _buildSectionHeader('Appearance'),
              SwitchListTile(
                title: const Text('Dark Mode'),
                subtitle: const Text('Toggle dark theme'),
                value: _settingsHandler.darkMode,
                onChanged: (value) async {
                  await _settingsHandler.setDarkMode(value);
                  setState(() {});
                },
              ),
              ListTile(
                title: const Text('Language'),
                subtitle: Text(_settingsHandler.language),
                trailing: const Icon(Icons.arrow_drop_down),
                onTap: () {
                  _showLanguageDialog();
                },
              ),
              const Divider(),

              // Notifications Section
              _buildSectionHeader('Notifications'),
              SwitchListTile(
                title: const Text('Push Notifications'),
                subtitle: const Text('Receive alerts and reminders'),
                value: _settingsHandler.notifications,
                onChanged: (value) async {
                  await _settingsHandler.setNotifications(value);
                  setState(() {});
                },
              ),
              const Divider(),

              // Data & Sync Section
              _buildSectionHeader('Data & Sync'),
              ListTile(
                title: const Text('Data Refresh Interval'),
                subtitle:
                    Text('${_settingsHandler.dataRefreshInterval} minutes'),
                trailing: const Icon(Icons.arrow_drop_down),
                onTap: () {
                  _showRefreshIntervalDialog();
                },
              ),
              ListTile(
                title: const Text('Clear Cache'),
                subtitle: const Text('Free up storage space'),
                trailing: const Icon(Icons.cleaning_services),
                onTap: () {
                  _showClearCacheDialog();
                },
              ),
              const Divider(),

              // About Section
              _buildSectionHeader('About'),
              ListTile(
                title: const Text('Terms of Service'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const TermsOfServiceScreen(),
                    ),
                  );
                },
              ),
              ListTile(
                title: const Text('Privacy Policy'),
                trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const PrivacyPolicyScreen(),
                    ),
                  );
                },
              ),
              const ListTile(
                title: Text('App Version'),
                subtitle: Text('2.85'),
                onTap: null,
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.grey,
        ),
      ),
    );
  }

  void _showLanguageDialog() {
    final List<String> languages = ['English', 'Spanish', 'French', 'German'];

    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          title: const Text('Select Language'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: languages.length,
              itemBuilder: (context, index) {
                final language = languages[index];
                return RadioListTile<String>(
                  title: Text(language),
                  value: language,
                  groupValue: _settingsHandler.language,
                  onChanged: (value) async {
                    if (value == null) return;

                    // Close the dialog first
                    Navigator.of(dialogContext).pop();

                    // Perform async operation
                    await _settingsHandler.setLanguage(value);

                    // Update UI only if widget is still mounted
                    if (mounted) {
                      setState(() {});
                    }
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  void _showRefreshIntervalDialog() {
    final List<int> refreshIntervals = [5, 15, 30, 60];

    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          title: const Text('Data Refresh Interval'),
          content: SizedBox(
            width: double.maxFinite,
            child: ListView.builder(
              shrinkWrap: true,
              itemCount: refreshIntervals.length,
              itemBuilder: (context, index) {
                final interval = refreshIntervals[index];
                return RadioListTile<int>(
                  title: Text('$interval minutes'),
                  value: interval,
                  groupValue: _settingsHandler.dataRefreshInterval,
                  onChanged: (value) async {
                    if (value == null) return;

                    // Close the dialog first
                    Navigator.of(dialogContext).pop();

                    // Perform async operation
                    await _settingsHandler.setDataRefreshInterval(value);

                    // Update UI only if widget is still mounted
                    if (mounted) {
                      setState(() {});
                    }
                  },
                );
              },
            ),
          ),
        );
      },
    );
  }

  void _showClearCacheDialog() {
    showDialog(
      context: context,
      builder: (dialogContext) {
        return AlertDialog(
          title: const Text('Clear Cache'),
          content: const Text(
              'Are you sure you want to clear the cache? This will not delete any of your data.'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: const Text('CANCEL'),
            ),
            TextButton(
              onPressed: () async {
                // Store the current context and related objects
                final currentContext = context;
                final messenger = ScaffoldMessenger.of(currentContext);
                final navigator = Navigator.of(currentContext);
                
                // Close the initial dialog
                Navigator.of(dialogContext).pop();
                
                // Show loading indicator
                if (!mounted) return;
                final loadingDialog = await showDialog<bool>(
                  context: currentContext,
                  barrierDismissible: false,
                  builder: (context) {
                    return const Center(
                      child: CircularProgressIndicator(),
                    );
                  },
                );
                
                // Clear cache
                await _settingsHandler.clearCache();
                
                // Dismiss loading indicator
                if (!mounted) return;
                if (loadingDialog != null) {
                  navigator.pop();
                }
                
                // Show success message
                if (mounted) {
                  messenger.showSnackBar(
                    const SnackBar(content: Text('Cache cleared successfully')),
                  );
                }
              },
              child: const Text('CLEAR'),
            ),
          ],
        );
      },
    );
  }
}
