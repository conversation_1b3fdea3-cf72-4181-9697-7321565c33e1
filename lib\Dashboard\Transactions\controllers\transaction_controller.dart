import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../services/transactions_handler.dart';
import '../../widgets/filters/filters.dart';

/// Transaction Analytics Summary Model
class TransactionAnalyticsSummary {
  final double totalIncome;
  final double totalExpenses;
  final double netBalance;
  final int totalTransactions;
  final int incomeTransactions;
  final int expenseTransactions;
  final double averageIncome;
  final double averageExpense;
  final Map<String, double> categoryBreakdown;
  final Map<String, double> paymentMethodBreakdown;
  final DateTime? firstTransactionDate;
  final DateTime? lastTransactionDate;

  const TransactionAnalyticsSummary({
    required this.totalIncome,
    required this.totalExpenses,
    required this.netBalance,
    required this.totalTransactions,
    required this.incomeTransactions,
    required this.expenseTransactions,
    required this.averageIncome,
    required this.averageExpense,
    required this.categoryBreakdown,
    required this.paymentMethodBreakdown,
    this.firstTransactionDate,
    this.lastTransactionDate,
  });
}

/// Transaction Insights Data Model
class TransactionInsightsData {
  final String topIncomeCategory;
  final String topExpenseCategory;
  final String mostUsedPaymentMethod;
  final double monthlyAverageIncome;
  final double monthlyAverageExpense;
  final List<String> recommendations;
  final Map<String, double> monthlyTrends;

  const TransactionInsightsData({
    required this.topIncomeCategory,
    required this.topExpenseCategory,
    required this.mostUsedPaymentMethod,
    required this.monthlyAverageIncome,
    required this.monthlyAverageExpense,
    required this.recommendations,
    required this.monthlyTrends,
  });
}

/// Centralized controller for transaction management data
/// Eliminates redundant data fetching across tabs and provides single source of truth
class TransactionController extends ChangeNotifier {
  final TransactionsHandler _transactionsHandler;

  // Core data
  List<TransactionIsar> _allTransactions = [];
  List<CategoryIsar> _allCategories = [];
  Map<String, CategoryIsar> _categoryMap = {};

  // Filtered data for records tab
  List<TransactionIsar> _filteredTransactions = [];

  // Loading state
  bool _isLoading = false;
  String? _errorMessage;

  // Cached analytics data
  TransactionAnalyticsSummary? _analyticsSummary;
  TransactionInsightsData? _insightsData;

  // Getters
  List<TransactionIsar> get allTransactions => List.unmodifiable(_allTransactions);
  List<CategoryIsar> get allCategories => List.unmodifiable(_allCategories);
  Map<String, CategoryIsar> get categoryMap => Map.unmodifiable(_categoryMap);
  List<TransactionIsar> get filteredTransactions => List.unmodifiable(_filteredTransactions);
  

  
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get hasData => _allTransactions.isNotEmpty;
  bool get hasError => _errorMessage != null;

  // Analytics and insights getters
  TransactionAnalyticsSummary get analyticsSummary => _analyticsSummary ?? _calculateAnalyticsSummary();
  TransactionInsightsData get insightsData => _insightsData ?? _calculateInsightsData();

  TransactionController(this._transactionsHandler);

  /// Initialize and load all data
  Future<void> initialize() async {
    await loadData();
  }

  /// Load all transaction and category data from database
  Future<void> loadData() async {
    try {
      _setLoading(true);
      _clearError();

      // Load categories and transactions data
      final categories = await _transactionsHandler.getAllCategories();
      final transactions = await _transactionsHandler.getAllTransactions();

      // Create category map for quick lookup
      final categoryMap = <String, CategoryIsar>{};
      for (final category in categories) {
        categoryMap[category.name] = category;
      }

      _allCategories = categories;
      _categoryMap = categoryMap;
      _allTransactions = transactions;
      _filteredTransactions = transactions;

      // Initialize filtered transactions (filters will be applied externally)
      _filteredTransactions = List.from(_allTransactions);

      // Clear cached calculations to force recalculation
      _analyticsSummary = null;
      _insightsData = null;

      _setLoading(false);
    } catch (e) {
      _setError('Error loading transaction data: $e');
      _setLoading(false);
    }
  }

  /// Refresh data from database
  Future<void> refresh() async {
    await loadData();
  }

  /// Apply filters using the universal filter service
  void applyFilters(FilterController filterController) {
    List<TransactionIsar> result = List.from(_allTransactions);

    // Apply search filter
    result = FilterHandlers.applySearch<TransactionIsar>(
      items: result,
      searchQuery: filterController.searchQuery,
      searchFields: [
        (transaction) => transaction.description,
        (transaction) => transaction.category,
      ],
    );

    // Apply date range filter
    result = FilterHandlers.applyDateFilter<TransactionIsar>(
      items: result,
      startDate: filterController.startDate,
      endDate: filterController.endDate,
      dateExtractor: (transaction) => transaction.date,
    );

    // Apply sorting
    result = FilterHandlers.applySort<TransactionIsar>(
      items: result,
      sortField: filterController.sortBy,
      isAscending: filterController.isAscending,
      sortComparators: {
        'date': FilterHandlers.dateComparator<TransactionIsar>((transaction) => transaction.date),
        'amount': FilterHandlers.numericComparator<TransactionIsar>((transaction) => transaction.amount),
        'description': FilterHandlers.stringComparator<TransactionIsar>((transaction) => transaction.description),
        'category': FilterHandlers.stringComparator<TransactionIsar>((transaction) => transaction.category),
      },
    );

    _filteredTransactions = result;

    // Clear cached calculations to force recalculation
    _analyticsSummary = null;
    _insightsData = null;

    notifyListeners();
  }

  /// Legacy method for backward compatibility - now handled by applyFilters
  @Deprecated('Use applyFilters(FilterController) instead')
  void setDateRange(DateTime? startDate, DateTime? endDate) {
    // This method is deprecated but kept for backward compatibility
    // The filtering is now handled by the universal filter system
  }

  /// Legacy method for backward compatibility - now handled by applyFilters
  @Deprecated('Use applyFilters(FilterController) instead')
  void clearFilters() {
    // This method is deprecated but kept for backward compatibility
    // The filtering is now handled by the universal filter system
  }

  /// Legacy getters for backward compatibility
  @Deprecated('Filter state is now managed by FilterController')
  DateTime? get startDate => null;

  @Deprecated('Filter state is now managed by FilterController')
  DateTime? get endDate => null;











  /// Get category by name
  CategoryIsar? getCategory(String name) {
    return _categoryMap[name];
  }

  /// Private helper methods
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  void _setError(String error) {
    _errorMessage = error;
    notifyListeners();
  }

  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }

  /// Calculate analytics summary from filtered transactions
  TransactionAnalyticsSummary _calculateAnalyticsSummary() {
    if (_filteredTransactions.isEmpty) {
      return const TransactionAnalyticsSummary(
        totalIncome: 0,
        totalExpenses: 0,
        netBalance: 0,
        totalTransactions: 0,
        incomeTransactions: 0,
        expenseTransactions: 0,
        averageIncome: 0,
        averageExpense: 0,
        categoryBreakdown: {},
        paymentMethodBreakdown: {},
      );
    }

    double totalIncome = 0;
    double totalExpenses = 0;
    int incomeCount = 0;
    int expenseCount = 0;
    Map<String, double> categoryBreakdown = {};
    Map<String, double> paymentMethodBreakdown = {};
    DateTime? firstDate;
    DateTime? lastDate;

    for (final transaction in _filteredTransactions) {
      final amount = transaction.amount;
      final isIncome = transaction.categoryType.toLowerCase() == 'income';

      if (isIncome) {
        totalIncome += amount;
        incomeCount++;
      } else {
        totalExpenses += amount;
        expenseCount++;
      }

      // Category breakdown
      categoryBreakdown[transaction.category] =
        (categoryBreakdown[transaction.category] ?? 0) + amount;

      // Payment method breakdown
      paymentMethodBreakdown[transaction.paymentMethod] =
        (paymentMethodBreakdown[transaction.paymentMethod] ?? 0) + amount;

      // Date tracking
      if (firstDate == null || transaction.date.isBefore(firstDate)) {
        firstDate = transaction.date;
      }
      if (lastDate == null || transaction.date.isAfter(lastDate)) {
        lastDate = transaction.date;
      }
    }

    final netBalance = totalIncome - totalExpenses;
    final averageIncome = incomeCount > 0 ? (totalIncome / incomeCount).toDouble() : 0.0;
    final averageExpense = expenseCount > 0 ? (totalExpenses / expenseCount).toDouble() : 0.0;

    _analyticsSummary = TransactionAnalyticsSummary(
      totalIncome: totalIncome,
      totalExpenses: totalExpenses,
      netBalance: netBalance,
      totalTransactions: _filteredTransactions.length,
      incomeTransactions: incomeCount,
      expenseTransactions: expenseCount,
      averageIncome: averageIncome,
      averageExpense: averageExpense,
      categoryBreakdown: categoryBreakdown,
      paymentMethodBreakdown: paymentMethodBreakdown,
      firstTransactionDate: firstDate,
      lastTransactionDate: lastDate,
    );

    return _analyticsSummary!;
  }

  /// Calculate insights data from filtered transactions
  TransactionInsightsData _calculateInsightsData() {
    if (_filteredTransactions.isEmpty) {
      return const TransactionInsightsData(
        topIncomeCategory: 'No data',
        topExpenseCategory: 'No data',
        mostUsedPaymentMethod: 'No data',
        monthlyAverageIncome: 0,
        monthlyAverageExpense: 0,
        recommendations: [],
        monthlyTrends: {},
      );
    }

    final summary = analyticsSummary;

    // Find top categories
    String topIncomeCategory = 'No income';
    String topExpenseCategory = 'No expenses';
    double maxIncomeAmount = 0;
    double maxExpenseAmount = 0;

    for (final transaction in _filteredTransactions) {
      final amount = transaction.amount;
      final isIncome = transaction.categoryType.toLowerCase() == 'income';

      if (isIncome && amount > maxIncomeAmount) {
        maxIncomeAmount = amount;
        topIncomeCategory = transaction.category;
      } else if (!isIncome && amount > maxExpenseAmount) {
        maxExpenseAmount = amount;
        topExpenseCategory = transaction.category;
      }
    }

    // Find most used payment method
    String mostUsedPaymentMethod = 'No data';
    int maxPaymentCount = 0;
    Map<String, int> paymentCounts = {};

    for (final transaction in _filteredTransactions) {
      paymentCounts[transaction.paymentMethod] =
        (paymentCounts[transaction.paymentMethod] ?? 0) + 1;
    }

    for (final entry in paymentCounts.entries) {
      if (entry.value > maxPaymentCount) {
        maxPaymentCount = entry.value;
        mostUsedPaymentMethod = entry.key;
      }
    }

    // Calculate monthly trends and averages
    Map<String, double> monthlyIncome = {};
    Map<String, double> monthlyExpenses = {};

    for (final transaction in _filteredTransactions) {
      final monthKey = '${transaction.date.year}-${transaction.date.month.toString().padLeft(2, '0')}';
      final amount = transaction.amount;
      final isIncome = transaction.categoryType.toLowerCase() == 'income';

      if (isIncome) {
        monthlyIncome[monthKey] = (monthlyIncome[monthKey] ?? 0) + amount;
      } else {
        monthlyExpenses[monthKey] = (monthlyExpenses[monthKey] ?? 0) + amount;
      }
    }

    final monthlyAverageIncome = monthlyIncome.values.isNotEmpty
      ? (monthlyIncome.values.reduce((a, b) => a + b) / monthlyIncome.length).toDouble()
      : 0.0;
    final monthlyAverageExpense = monthlyExpenses.values.isNotEmpty
      ? (monthlyExpenses.values.reduce((a, b) => a + b) / monthlyExpenses.length).toDouble()
      : 0.0;

    // Generate recommendations
    List<String> recommendations = [];
    if (summary.netBalance < 0) {
      recommendations.add('Consider reducing expenses in $topExpenseCategory category');
    }
    if (summary.totalExpenses > summary.totalIncome * 0.8) {
      recommendations.add('Expenses are high relative to income - review budget');
    }
    if (monthlyAverageExpense > monthlyAverageIncome) {
      recommendations.add('Monthly expenses exceed income - consider cost reduction');
    }

    _insightsData = TransactionInsightsData(
      topIncomeCategory: topIncomeCategory,
      topExpenseCategory: topExpenseCategory,
      mostUsedPaymentMethod: mostUsedPaymentMethod,
      monthlyAverageIncome: monthlyAverageIncome,
      monthlyAverageExpense: monthlyAverageExpense,
      recommendations: recommendations,
      monthlyTrends: {...monthlyIncome, ...monthlyExpenses},
    );

    return _insightsData!;
  }

  /// Delete a transaction
  Future<void> deleteTransaction(String transactionId) async {
    try {
      _setLoading(true);
      _clearError();

      await _transactionsHandler.deleteTransaction(transactionId);
      await loadData(); // Reload data after deletion
    } catch (e) {
      _setError('Failed to delete transaction: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  @override
  void dispose() {
    // No timers to cancel since we removed the search debounce timer
    super.dispose();
  }
}
