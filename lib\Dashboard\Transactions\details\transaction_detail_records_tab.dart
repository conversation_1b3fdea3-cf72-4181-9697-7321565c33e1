import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../controllers/transaction_detail_controller.dart';
import '../models/transaction_isar.dart';
import '../dialogs/transaction_form_dialog.dart';
import '../../../utils/message_utils.dart';
import '../../widgets/index.dart';

class TransactionDetailRecordsTab extends StatefulWidget {
  final TransactionDetailController controller;

  const TransactionDetailRecordsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<TransactionDetailRecordsTab> createState() => _TransactionDetailRecordsTabState();
}

class _TransactionDetailRecordsTabState extends State<TransactionDetailRecordsTab>
    with AutomaticKeepAliveClientMixin, UniversalScreenState, UniversalDataRefresh, UniversalPullToRefresh {

  late FilterController _filterController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    // Initialize filter controller
    _filterController = FilterController();
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }



  /// Handle filter changes and update transaction controller
  void _onFiltersChanged() {
    // Apply filters using the universal filter service
    widget.controller.applyFilters(_filterController);
  }

  /// Manual refresh method for pull-to-refresh
  Future<void> _handleRefresh() async {
    await performRefreshWithRetry(
      () => widget.controller.refresh(),
      maxRetries: 3,
      retryDelay: const Duration(seconds: 2),
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin

    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        if (widget.controller.isLoading) {
          return UniversalLoadingIndicator.transactions();
        }

        if (widget.controller.error != null) {
          return UniversalErrorIndicator.transactions(
            message: widget.controller.error!,
            onRetry: () => widget.controller.refresh(),
          );
        }

        return Column(
          children: [
            // Filter and Search Section
            UniversalFilterLayout(
              controller: _filterController,
              theme: FilterTheme.transaction,
              sortFields: [...SortField.commonFields, ...SortField.transactionFields],
              searchHint: 'Search transaction records...',
              totalCount: widget.controller.allRecords.length,
              filteredCount: widget.controller.filteredRecords.length,
            ),
            
            // Filter Status Bar
            FilterStatusBar(
              controller: _filterController,
            ),
            
            // Transaction Records List
            Expanded(
              child: _buildRecordsList(),
            ),
          ],
        );
      },
    );
  }

  Widget _buildRecordsList() {
    if (!widget.controller.hasData) {
      return UniversalEmptyState.transactions(
        title: 'No Transaction Records',
        message: 'No transaction records found for the selected filters.',
        action: EmptyStateActions.addFirstRecord(
          onPressed: _showAddTransactionDialog,
          text: 'Add Transaction',
          backgroundColor: UniversalEmptyStateTheme.transactions,
        ),
      );
    }

    if (!widget.controller.hasFilteredData) {
      return UniversalEmptyState.transactions(
        title: 'No Results Found',
        message: 'No transaction records match your current filters.',
        type: EmptyStateType.noResults,
        action: EmptyStateActions.clearFilters(
          onPressed: () {
            debugPrint('🔍 TransactionDetailRecordsTab - Clear filters from empty state');
            _filterController.clearAllApplied();
            widget.controller.clearFilters();
          },
        ),
      );
    }

    return buildRefreshableListBuilder(
      itemCount: widget.controller.filteredRecords.length,
      itemBuilder: (context, index) {
        final transaction = widget.controller.filteredRecords[index];
        
        // Determine if income or expense for dynamic colors
        final isIncome = transaction.categoryType.toLowerCase() == 'income';
        final amountColor = isIncome ? Colors.green : Colors.red;
        final formattedAmount = '${isIncome ? '+' : '-'}\$${transaction.amount.toStringAsFixed(2)}';
        final formattedDate = DateFormat('MMM dd, yyyy').format(transaction.date);

        // Use the universal record card
        return UniversalRecordCard(
          row1Left: formattedDate,
          row1Right: formattedAmount,
          row1LeftIcon: Icons.calendar_today_outlined,
          row1RightIcon: isIncome ? Icons.arrow_upward : Icons.arrow_downward,
          row2Left: transaction.category,
          row2Right: transaction.paymentMethod,
          row2LeftIcon: transaction.icon,
          row2RightIcon: _getPaymentMethodIcon(transaction.paymentMethod),
          notes: transaction.description.isNotEmpty ? transaction.description : null,
          primaryColor: UniversalRecordTheme.transactionPrimary,
          row1RightColor: amountColor,
          onEdit: () => _editTransaction(transaction),
          onDelete: () => _deleteTransaction(transaction),
          onTap: () => _editTransaction(transaction),
          onLongPress: () => _showTransactionOptions(transaction),
        );
      },
      onRefresh: _handleRefresh,
      padding: const EdgeInsets.all(16.0),
      color: UniversalEmptyStateTheme.transactions,
    );
  }

  IconData _getPaymentMethodIcon(String paymentMethod) {
    switch (paymentMethod.toLowerCase()) {
      case 'cash':
        return Icons.money;
      case 'credit card':
        return Icons.credit_card;
      case 'debit card':
        return Icons.payment;
      case 'bank transfer':
        return Icons.account_balance;
      case 'mobile payment':
        return Icons.phone_android;
      case 'check':
        return Icons.receipt;
      default:
        return Icons.payment;
    }
  }

  Future<void> _showAddTransactionDialog() async {
    await showDialog(
      context: context,
      builder: (context) => TransactionFormDialog(
        categories: widget.controller.categories,
        onTransactionAdded: () => widget.controller.refresh(),
      ),
    );
  }

  Future<void> _editTransaction(TransactionIsar transaction) async {
    await showDialog(
      context: context,
      builder: (context) => TransactionFormDialog(
        transaction: transaction,
        categories: widget.controller.categories,
        onTransactionAdded: () => widget.controller.refresh(),
      ),
    );
  }

  Future<void> _deleteTransaction(TransactionIsar transaction) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Transaction'),
        content: Text('Are you sure you want to delete this transaction?\n\n'
            'Category: ${transaction.category}\n'
            'Amount: \$${transaction.amount.toStringAsFixed(2)}\n'
            'Date: ${DateFormat('MMM dd, yyyy').format(transaction.date)}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // TODO: Implement delete functionality through controller
        // await widget.controller.deleteTransaction(transaction);
        if (mounted) {
          FinancialMessageUtils.showSuccess(context, 'Transaction deleted successfully');
        }
      } catch (e) {
        if (mounted) {
          FinancialMessageUtils.showError(context, 'Error deleting transaction: $e');
        }
      }
    }
  }

  void _showTransactionOptions(TransactionIsar transaction) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            leading: const Icon(Icons.edit),
            title: const Text('Edit Transaction'),
            onTap: () {
              Navigator.of(context).pop();
              _editTransaction(transaction);
            },
          ),
          ListTile(
            leading: const Icon(Icons.delete, color: Colors.red),
            title: const Text('Delete Transaction'),
            onTap: () {
              Navigator.of(context).pop();
              _deleteTransaction(transaction);
            },
          ),
          ListTile(
            leading: const Icon(Icons.share),
            title: const Text('Share Transaction'),
            onTap: () {
              Navigator.of(context).pop();
              // TODO: Implement share functionality
              FinancialMessageUtils.showInfo(context, 'Share feature coming soon!');
            },
          ),
        ],
      ),
    );
  }
}
