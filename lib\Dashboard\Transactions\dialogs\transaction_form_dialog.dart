import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/transaction_isar.dart';
import '../models/category_isar.dart';
import '../../widgets/index.dart'; // Import Universal Components

class TransactionFormDialog extends StatefulWidget {
  final List<CategoryIsar> categories;
  final TransactionIsar? transaction;
  final VoidCallback? onTransactionAdded;

  const TransactionFormDialog({
    Key? key,
    required this.categories,
    this.transaction,
    this.onTransactionAdded,
  }) : super(key: key);

  @override
  State<TransactionFormDialog> createState() => _TransactionFormDialogState();
}

class _TransactionFormDialogState extends State<TransactionFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late DateTime _selectedDate;
  late String _selectedType;
  String? _selectedCategory;
  late TextEditingController _amountController;
  late TextEditingController _descriptionController;
  String? _selectedPaymentMethod;
  bool _isSaving = false;

  final List<String> _paymentMethods = [
    'Cash',
    'Credit Card',
    'Debit Card',
    'Bank Transfer',
    'Mobile Payment',
    'Check',
    'Other'
  ];

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.transaction?.date ?? DateTime.now();
    _selectedType = widget.transaction?.categoryType ?? 'Income';
    _selectedCategory = widget.transaction?.category;
    _amountController = TextEditingController(
      text: widget.transaction?.amount.toString() ?? '',
    );
    _descriptionController = TextEditingController(
      text: widget.transaction?.description ?? '',
    );
    _selectedPaymentMethod =
        widget.transaction?.paymentMethod ?? _paymentMethods[0];
  }

  @override
  void dispose() {
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  List<String> _getCategoriesForType(String type) {
    return widget.categories
        .where((category) => category.type == type)
        .map((category) => category.name)
        .toList();
  }



  void _handleTypeChange(String? newValue) {
    if (newValue != null) {
      setState(() {
        _selectedType = newValue;
        _selectedCategory = null;
      });
    }
  }

  Future<void> _handleSubmit() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isSaving = true);

      try {
        final transaction = widget.transaction ?? TransactionIsar();

        // Update transaction fields
        transaction
          ..date = _selectedDate
          ..categoryType = _selectedType
          ..category = _selectedCategory ?? ''
          ..title = _selectedCategory ?? 'Transaction' // Set title to category or default
          ..amount = double.parse(_amountController.text)
          ..description = _descriptionController.text
          ..paymentMethod = _selectedPaymentMethod ?? ''
          ..updatedAt = DateTime.now();

        // Set created date for new transactions
        if (widget.transaction == null) {
          transaction.createdAt = DateTime.now();
        }

        // Save transaction (you'll need to implement this)
        // await _saveTransaction(transaction);

        if (mounted) {
          Navigator.of(context).pop();
          widget.onTransactionAdded?.call();
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error saving transaction: $e')),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isSaving = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final categoriesForType = _getCategoriesForType(_selectedType);
    final isEditing = widget.transaction != null;

    return StandardFormDialog(
      title: isEditing ? 'Edit Transaction' : 'Add Transaction',
      width: 500,
      height: 650,
      content: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Date Field
            ModuleFormFieldBuilders.transactionsTextField(
              label: 'Date',
              initialValue: DateFormat('yyyy-MM-dd').format(_selectedDate),
              prefixIcon: const Icon(Icons.calendar_today),
              onChanged: (value) {
                // Handle date parsing if needed
              },
              validationRules: [ValidationRule.required()],
            ),

            const SizedBox(height: 16),

            // Transaction Type Dropdown
            UniversalFormFieldBuilder.buildDropdownField<String>(
              label: 'Transaction Type',
              value: _selectedType,
              items: ['Income', 'Expense'].map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: _handleTypeChange,
              prefixIcon: Icon(
                _selectedType == 'Income' ? Icons.arrow_upward : Icons.arrow_downward,
                color: _selectedType == 'Income' ? Colors.green : Colors.red,
              ),
              validationRules: [ValidationRule.required()],
              moduleColor: UniversalEmptyStateTheme.transactions,
            ),

            const SizedBox(height: 16),

            // Category Dropdown
            UniversalFormFieldBuilder.buildDropdownField<String>(
              label: 'Category',
              value: _selectedCategory,
              items: categoriesForType.map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedCategory = newValue;
                });
              },
              prefixIcon: const Icon(Icons.category),
              validationRules: [ValidationRule.required()],
              moduleColor: UniversalEmptyStateTheme.transactions,
            ),

            const SizedBox(height: 16),

            // Amount Field
            ModuleFormFieldBuilders.transactionsTextField(
              label: 'Amount',
              controller: _amountController,
              keyboardType: TextInputType.number,
              prefixIcon: const Icon(Icons.attach_money),
              validationRules: [
                ValidationRule.required(),
                ValidationRule.decimal(min: 0.01),
              ],
            ),

            const SizedBox(height: 16),

            // Payment Method Dropdown
            UniversalFormFieldBuilder.buildDropdownField<String>(
              label: 'Payment Method',
              value: _selectedPaymentMethod,
              items: _paymentMethods.map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _selectedPaymentMethod = newValue;
                });
              },
              prefixIcon: const Icon(Icons.payment),
              validationRules: [ValidationRule.required()],
              moduleColor: UniversalEmptyStateTheme.transactions,
            ),

            const SizedBox(height: 16),

            // Description Field
            ModuleFormFieldBuilders.transactionsTextField(
              label: 'Description',
              controller: _descriptionController,
              prefixIcon: const Icon(Icons.description),
              validationRules: [ValidationRule.required()],
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isSaving ? null : () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isSaving ? null : _handleSubmit,
          style: ElevatedButton.styleFrom(
            backgroundColor: UniversalEmptyStateTheme.transactions,
            foregroundColor: Colors.white,
          ),
          child: _isSaving
            ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : Text(isEditing ? 'Update' : 'Save'),
        ),
      ],
    );
  }
}
