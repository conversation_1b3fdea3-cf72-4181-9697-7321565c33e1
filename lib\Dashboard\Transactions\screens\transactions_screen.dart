import 'package:flutter/material.dart';

import '../../../constants/app_tabs.dart';
import '../controllers/transaction_controller.dart';
import '../services/transactions_handler.dart';
import '../tabs/transaction_records_tab.dart';
import '../tabs/transaction_analytics_tab.dart';
import '../tabs/transaction_insights_tab.dart';
import '../dialogs/transaction_form_dialog.dart';
import '../../../utils/message_utils.dart';
import '../../widgets/index.dart'; // Import Universal Components

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({Key? key}) : super(key: key);

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen>
    with TickerProviderStateMixin, UniversalScreenState, UniversalDataRefresh, UniversalDataLoader {
  late TransactionController _transactionController;
  late Tab<PERSON>ontroller _tabController;
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();

    // Define tabs using the reusable widget configuration with multicolor
    _tabs = TabConfigurations.threeTabModule(
      tab1Label: 'Analytics',
      tab1Icon: Icons.analytics,
      tab1Color: UniversalEmptyStateTheme.transactions, // Blue for analytics
      tab2Label: 'Records',
      tab2Icon: Icons.list,
      tab2Color: const Color(0xFF388E3C), // Green for records
      tab3Label: 'Insights',
      tab3Icon: Icons.insights,
      tab3Color: Colors.purple, // Purple for insights
    );

    // Initialize tab controller with dynamic length
    _tabController = TabController(length: _tabs.length, vsync: this);

    // Add listener to rebuild FAB and tabs when tab changes
    _tabController.addListener(() {
      if (mounted) {
        safeSetState(() {}); // Use safe setState from UniversalScreenState
      }
    });

    // Initialize transaction controller and load data using Universal Data Loader
    _transactionController = TransactionController(TransactionsHandler.instance);
    loadDataOnInit(() => _transactionController.initialize());

    // Enable periodic refresh for transaction data
    enablePeriodicRefresh(
      interval: const Duration(minutes: 5),
      startImmediately: false,
    );
  }

  /// Get screen state from controller state for universal state management
  ScreenState _getScreenStateFromController() {
    if (_transactionController.isLoading) return ScreenState.loading;
    if (_transactionController.hasError) return ScreenState.error;
    return ScreenState.loaded;
  }

  void _showAddTransactionDialog() {
    showDialog(
      context: context,
      builder: (context) => TransactionFormDialog(
        categories: _transactionController.allCategories,
        onTransactionAdded: () async {
          try {
            await _transactionController.loadData();

            // Check mounted status after await
            if (!mounted) return;

            // Use standardized success message
            if (context.mounted) {
              FinancialMessageUtils.showSuccess(context,
                  FinancialMessageUtils.expenseRecorded());
            }

          } catch (e) {
            debugPrint('Error adding transaction: $e');

            // Check mounted status again in catch block
            if (!mounted) return;

            // Use standardized error message
            if (context.mounted) {
              FinancialMessageUtils.showError(context, 'Error adding transaction');
            }
          }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Transaction Management'),
        backgroundColor: UniversalEmptyStateTheme.transactions, // Primary blue
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => performRefresh(() => _transactionController.refresh()),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar with multicolor support
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
            useMulticolor: true,
            indicatorColor: UniversalEmptyStateTheme.transactions, // Blue indicator
          ),
          // Tab Views with Universal State Management
          Expanded(
            child: ListenableBuilder(
              listenable: _transactionController,
              builder: (context, child) {
                // Sync controller state with screen state
                final controllerState = _getScreenStateFromController();

                return UniversalStateBuilder(
                  state: controllerState,
                  errorMessage: _transactionController.errorMessage,
                  onRetry: () => performRefresh(() => _transactionController.refresh()),
                  moduleColor: UniversalEmptyStateTheme.transactions,
                  loadingWidget: UniversalLoadingIndicator.transactions(),
                  errorWidget: UniversalErrorIndicator.transactions(
                    message: _transactionController.errorMessage ?? 'Failed to load transaction data',
                    onRetry: () => performRefresh(() => _transactionController.refresh()),
                  ),
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      TransactionAnalyticsTab(controller: _transactionController),
                      TransactionRecordsTab(controller: _transactionController),
                      TransactionInsightsTab(controller: _transactionController),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddTransactionDialog,
        backgroundColor: UniversalEmptyStateTheme.transactions,
        child: const Icon(Icons.add),
      ),
    );
  }



  @override
  void dispose() {
    _tabController.dispose();
    _transactionController.dispose();
    super.dispose();
  }
}
