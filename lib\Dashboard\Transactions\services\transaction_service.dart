import '../models/transaction_isar.dart';
import 'package:get_it/get_it.dart';
import 'transactions_handler.dart';

class TransactionService {
  late final TransactionsHandler _transactionHandler;

  TransactionService() {
    _initialize();
  }

  void _initialize() {
    _transactionHandler = GetIt.instance<TransactionsHandler>();
  }

  Future<List<TransactionIsar>> getTransactions() async {
    return await _transactionHandler.getAllTransactions();
  }

  Future<void> addTransaction(TransactionIsar transaction) async {
    await _transactionHandler.addTransaction(transaction);
  }

  Future<void> updateTransaction(TransactionIsar transaction) async {
    await _transactionHandler.updateTransaction(transaction);
  }

  Future<void> deleteTransaction(String transactionId) async {
    await _transactionHandler.deleteTransaction(transactionId);
  }

  Future<List<TransactionIsar>> getTransactionsByType(
      String categoryType) async {
    return await _transactionHandler.getTransactionsByType(categoryType);
  }

  Future<List<TransactionIsar>> getTransactionsByCategory(
      String category) async {
    return await _transactionHandler.getTransactionsByCategory(category);
  }

  Future<List<TransactionIsar>> getTransactionsByDateRange(
      DateTime startDate, DateTime endDate) async {
    return await _transactionHandler.getTransactionsForDateRange(
        startDate, endDate);
  }
}
