import 'package:flutter/material.dart';

import '../../../constants/app_tabs.dart';
import '../../../constants/app_tabs.dart';
import '../controllers/weight_controller.dart';
import '../dialogs/weight_form_dialog.dart';
import '../tabs/weight_records_tab.dart';
import '../tabs/weight_analytics_tab.dart';
import '../tabs/weight_insights_tab.dart';
import '../../widgets/index.dart'; // Import Universal Components


class WeightScreen extends StatefulWidget {
  const WeightScreen({Key? key}) : super(key: key);

  @override
  State<WeightScreen> createState() => _WeightScreenState();
}

class _WeightScreenState extends State<WeightScreen>
    with TickerProviderStateMixin, UniversalScreenState, UniversalDataRefresh, UniversalDataLoader {
  late WeightController _weightController;
  late TabController _tabController;
  late List<TabItem> _tabs;

  @override
  void initState() {
    super.initState();

    // Define tabs using the reusable widget configuration with multicolor
    _tabs = TabConfigurations.threeTabModule(
      tab1Label: 'Analytics',
      tab1Icon: Icons.analytics,
      tab1Color: Colors.blue, // Blue for analytics
      tab2Label: 'Records',
      tab2Icon: Icons.list,
      tab2Color: const Color(0xFF2E7D32), // Green for records
      tab3Label: 'Insights',
      tab3Icon: Icons.insights,
      tab3Color: Colors.purple, // Purple for insights
    );

    // Initialize tab controller with dynamic length
    _tabController = TabController(length: _tabs.length, vsync: this);

    // Add listener to rebuild FAB and tabs when tab changes
    _tabController.addListener(() {
      if (mounted) {
        safeSetState(() {}); // Use safe setState from UniversalScreenState
      }
    });

    // Initialize weight controller and load data using Universal Data Loader
    _weightController = WeightController();
    loadDataOnInit(() => _weightController.initialize());

    // Enable periodic refresh for weight data
    enablePeriodicRefresh(
      interval: const Duration(minutes: 5),
      startImmediately: false,
    );
  }



  @override
  void dispose() {
    _tabController.dispose();
    _weightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Weight Management'),
        backgroundColor: const Color(0xFF2E7D32), // Primary green
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => performRefresh(() => _weightController.refresh()),
            tooltip: 'Refresh',
          ),
        ],
      ),
      body: Column(
        children: [
          // Tab Bar with multicolor support
          ReusableTabBar.controlled(
            controller: _tabController,
            tabs: _tabs,
            useMulticolor: true,
            indicatorColor: const Color(0xFF2E7D32), // Green indicator
          ),
          // Tab Views with Universal State Management
          Expanded(
            child: ListenableBuilder(
              listenable: _weightController,
              builder: (context, child) {
                // Sync controller state with screen state
                final controllerState = _getScreenStateFromController();

                return UniversalStateBuilder(
                  state: controllerState,
                  errorMessage: _weightController.errorMessage,
                  onRetry: () => performRefresh(() => _weightController.refresh()),
                  moduleColor: UniversalEmptyStateTheme.weight,
                  loadingWidget: UniversalLoadingIndicator.weight(),
                  errorWidget: UniversalErrorIndicator.weight(
                    message: _weightController.errorMessage ?? 'Failed to load weight data',
                    onRetry: () => performRefresh(() => _weightController.refresh()),
                  ),
                  child: TabBarView(
                    controller: _tabController,
                    children: [
                      WeightAnalyticsTab(controller: _weightController),
                      WeightRecordsTab(controller: _weightController),
                      WeightInsightsTab(controller: _weightController),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: AnimatedBuilder(
        animation: _tabController,
        builder: (context, child) {
          return _buildFloatingActionButton() ?? const SizedBox.shrink();
        },
      ),
    );
  }

  Widget? _buildFloatingActionButton() {
    switch (_tabController.index) {
      case 1: // Records tab
        return UniversalFAB.add(
          onPressed: _addWeightRecord,
          tooltip: 'Add first record',
        );
      default:
        return null;
    }
  }

  // Helper method to sync controller state with Universal Screen State
  ScreenState _getScreenStateFromController() {
    if (_weightController.isLoading) {
      return ScreenState.loading;
    } else if (_weightController.hasError) {
      return ScreenState.error;
    } else {
      return ScreenState.loaded;
    }
  }

  void _addWeightRecord() {
    showDialog(
      context: context,
      builder: (context) => WeightFormDialog(
        cattle: _weightController.allCattle,
        onRecordAdded: () => performRefresh(() => _weightController.refresh()),
      ),
    );
  }
}