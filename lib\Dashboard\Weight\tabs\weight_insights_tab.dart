import 'package:flutter/material.dart';
import '../controllers/weight_controller.dart';
import '../models/weight_insights_models.dart';
import '../../widgets/index.dart';

class WeightInsightsTab extends StatefulWidget {
  final WeightController controller;
  
  const WeightInsightsTab({
    Key? key,
    required this.controller,
  }) : super(key: key);

  @override
  State<WeightInsightsTab> createState() => _WeightInsightsTabState();
}

class _WeightInsightsTabState extends State<WeightInsightsTab> 
    with AutomaticKeepAliveClientMixin {

  @override
  bool get wantKeepAlive => true;

  // Color palette for insights
  static const _performanceColors = [
    Color(0xFF1976D2), // Blue
    Color(0xFF27AE60), // Green
    Color(0xFF8E44AD), // Purple
  ];

  static const _healthColors = [
    Color(0xFF16A085), // Teal
    Color(0xFF00796B), // Dark Teal
    Color(0xFFD32F2F), // Red
  ];

  static const _trendColors = [
    Color(0xFF7B1FA2), // Deep Purple
    Color(0xFF303F9F), // Indigo
    Color(0xFF388E3C), // Dark Green
  ];

  static const _recommendationColors = [
    Color(0xFF1976D2), // Blue
    Color(0xFF27AE60), // Green
    Color(0xFF8E44AD), // Purple
    Color(0xFF16A085), // Teal
  ];

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    
    return ListenableBuilder(
      listenable: widget.controller,
      builder: (context, child) {
        if (!widget.controller.hasData) {
          return _buildEmptyState();
        }

        final insightsData = widget.controller.insightsData;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInsightsHeader(),
              const SizedBox(height: 16),
              _buildPerformanceInsights(insightsData.performance),
              const SizedBox(height: 16),
              _buildHealthInsights(insightsData.health),
              const SizedBox(height: 16),
              _buildTrendInsights(insightsData.trends),
              const SizedBox(height: 16),
              _buildRecommendations(insightsData.recommendations),
            ],
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return EmptyState.custom(
      icon: Icons.insights,
      title: 'No Insights Available',
      message: 'Add more weight records to generate insights',
    );
  }

  Widget _buildInsightsHeader() {
    final analyticsSummary = widget.controller.analyticsSummary;
    
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF8E44AD), Color(0xFF9B59B6)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Row(
            children: [
              Icon(Icons.insights, color: Colors.white, size: 28),
              SizedBox(width: 12),
              Text(
                'Weight Insights',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            'Analysis based on ${analyticsSummary.totalRecords} weight records across ${analyticsSummary.totalCattle} cattle',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Last updated: ${DateTime.now().day}/${DateTime.now().month}/${DateTime.now().year}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceInsights(PerformanceInsights performance) {
    return _buildInsightSection(
      'Performance Insights',
      Icons.trending_up,
      _performanceColors[0],
      [
        _buildInsightCard(
          'Top Performer',
          performance.topPerformerName,
          'Highest weight gain rate',
          _performanceColors[0],
        ),
        _buildInsightCard(
          'Average Daily Gain',
          '${performance.averageDailyGain.toStringAsFixed(2)} kg/day',
          'Across all cattle',
          _performanceColors[1],
        ),
        _buildInsightCard(
          'Growth Trend',
          performance.growthTrend,
          performance.growthTrendDescription,
          _performanceColors[2],
        ),
      ],
    );
  }

  Widget _buildHealthInsights(HealthInsights health) {
    return _buildInsightSection(
      'Health Insights',
      Icons.health_and_safety,
      _healthColors[0],
      [
        _buildInsightCard(
          'Body Condition',
          health.averageBodyCondition.toStringAsFixed(1),
          'Average BCS score',
          _healthColors[0],
        ),
        _buildInsightCard(
          'Weight Consistency',
          health.consistencyRating,
          health.consistencyDescription,
          _healthColors[1],
        ),
        _buildInsightCard(
          'Health Alerts',
          health.healthAlerts.toString(),
          'Cattle needing attention',
          _healthColors[2],
        ),
      ],
    );
  }

  Widget _buildTrendInsights(TrendInsights trends) {
    return _buildInsightSection(
      'Trend Analysis',
      Icons.show_chart,
      _trendColors[0],
      [
        _buildInsightCard(
          'Overall Trend',
          trends.overallTrend,
          trends.trendDescription,
          _trendColors[0],
        ),
        _buildInsightCard(
          'Seasonal Pattern',
          trends.seasonalPattern,
          trends.seasonalDescription,
          _trendColors[1],
        ),
        _buildInsightCard(
          'Trend Percentage',
          '${trends.trendPercentage.toStringAsFixed(1)}%',
          'Change over time',
          _trendColors[2],
        ),
      ],
    );
  }

  Widget _buildRecommendations(List<WeightRecommendation> recommendations) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: _recommendationColors[0].withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb, color: _recommendationColors[0], size: 24),
              const SizedBox(width: 8),
              Text(
                'Recommendations',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: _recommendationColors[0],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (recommendations.isEmpty)
            const Text('No specific recommendations at this time.')
          else
            ...recommendations.map((rec) => _buildRecommendationCard(rec)),
        ],
      ),
    );
  }

  Widget _buildInsightSection(String title, IconData icon, Color color, List<Widget> cards) {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: cards,
          ),
        ],
      ),
    );
  }

  Widget _buildInsightCard(String title, String value, String subtitle, Color color) {
    return Container(
      width: 120,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 10,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationCard(WeightRecommendation recommendation) {
    Color priorityColor;
    switch (recommendation.priority.toLowerCase()) {
      case 'high':
        priorityColor = Colors.red;
        break;
      case 'medium':
        priorityColor = Colors.orange;
        break;
      case 'low':
        priorityColor = Colors.green;
        break;
      default:
        priorityColor = Colors.blue;
    }

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: priorityColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: priorityColor.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: priorityColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  recommendation.priority.toUpperCase(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  recommendation.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            recommendation.description,
            style: const TextStyle(fontSize: 12),
          ),
          if (recommendation.actionItems.isNotEmpty) ...[
            const SizedBox(height: 8),
            ...recommendation.actionItems.map((action) => Padding(
              padding: const EdgeInsets.only(top: 4),
              child: Row(
                children: [
                  Icon(Icons.check_circle_outline, size: 16, color: priorityColor),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      action,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }
}
