import 'package:flutter/material.dart';
// import '../Farm Setup/models/farm.dart'; // TODO: Create this file
import '../Farm Setup/screens/farm_info_screen.dart';
import '../Reports/screens/reports_screen.dart';
import '../Notifications/screens/notifications_screen.dart';
// import '../../services/database_helper.dart'; // TODO: Create this file
// import '../../utils/responsive_helper.dart'; // TODO: Create this file
// import '../../utils/responsive_layout.dart'; // TODO: Create this file
// import '../../theme/responsive_theme.dart'; // TODO: Create this file

class FarmSelectionDrawer extends StatefulWidget {
  const FarmSelectionDrawer({Key? key}) : super(key: key);

  @override
  State<FarmSelectionDrawer> createState() => _FarmSelectionDrawerState();
}

class _FarmSelectionDrawerState extends State<FarmSelectionDrawer> {
  // TODO: Implement when DatabaseHelper and Farm classes are created
  // final DatabaseHelper _dbHelper = DatabaseHelper.instance;
  // List<Farm> _farms = [];
  // Farm? _selectedFarm;
  // bool _isLoading = true; // TODO: Use when needed

  @override
  void initState() {
    super.initState();
    // TODO: Implement when DatabaseHelper is created
    // _loadFarms();
    // _setupFarmChangeListener();
    // setState(() {
    //   _isLoading = false;
    // });
  }

  @override
  void dispose() {
    // TODO: Implement when DatabaseHelper is created
    // _dbHelper.removeListener(_onFarmChanged);
    super.dispose();
  }

  // TODO: Implement these methods when DatabaseHelper and Farm classes are created
  // void _setupFarmChangeListener() {
  //   _dbHelper.addListener(_onFarmChanged);
  // }

  // void _onFarmChanged() {
  //   _loadFarms();
  // }

  // Future<void> _loadFarms() async {
  //   try {
  //     final farms = await _dbHelper.getFarms();
  //     final selectedFarmId = await _dbHelper.getSelectedFarmId();

  //     if (!mounted) return;

  //     setState(() {
  //       _farms = farms;
  //       if (selectedFarmId != null && farms.isNotEmpty) {
  //         _selectedFarm = farms.firstWhere(
  //           (farm) => farm.id == selectedFarmId,
  //           orElse: () => farms.first,
  //         );
  //       } else if (farms.isNotEmpty) {
  //         _selectedFarm = farms.first;
  //       } else {
  //         _selectedFarm = null;
  //       }
  //       _isLoading = false;
  //     });
  //   } catch (e) {
  //     debugPrint('Error loading farms: $e');
  //     if (mounted) {
  //       setState(() => _isLoading = false);
  //     }
  //   }
  // }

  // Future<void> _selectFarm(Farm farm) async {
  //   try {
  //     setState(() => _isLoading = true);
  //     await _dbHelper.setSelectedFarmId(farm.id);
  //     setState(() {
  //       _selectedFarm = farm;
  //       _isLoading = false;
  //     });
  //     if (mounted) {
  //       Navigator.pop(context);
  //     }
  //   } catch (e) {
  //     debugPrint('Error selecting farm: $e');
  //     if (mounted) {
  //       setState(() => _isLoading = false);
  //     }
  //   }
  // }

  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    Color? iconColor,
    bool showDivider = false,
  }) {
    return Column(
      children: [
        ListTile(
          leading: Icon(
            icon,
            color: iconColor ?? const Color(0xFF4CAF50),
          ),
          title: Text(title),
          onTap: onTap,
        ),
        if (showDivider) const Divider(height: 1),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.only(
                top: MediaQuery.of(context).padding.top + 16,
                bottom: 16,
              ),
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Color(0xFF4CAF50),
                    Color(0xFF2E7D32),
                  ],
                ),
              ),
              child: const Column(
                children: [
                  CircleAvatar(
                    radius: 40,
                    backgroundColor: Colors.white,
                    child: Icon(
                      Icons.person,
                      size: 40,
                      color: Colors.black54,
                    ),
                  ),
                  SizedBox(height: 12),
                  Text(
                    'Farm Selection', // TODO: Use _selectedFarm?.name when Farm class is created
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    'Manage Your Farm',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  const SizedBox(height: 8),
                  ExpansionTile(
                    leading:
                        const Icon(Icons.business, color: Color(0xFF2E7D32)),
                    title: const Text(
                      'My Farms',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    children: [
                      // TODO: Implement when Farm class is created
                      ListTile(
                        leading: const Icon(Icons.agriculture),
                        title: const Text('Default Farm'),
                        onTap: () {
                          Navigator.pop(context);
                        },
                      ),
                    ],
                  ),
                  const Divider(height: 1),
                  _buildDrawerItem(
                    icon: Icons.dashboard,
                    title: 'Dashboard',
                    onTap: () => Navigator.pop(context),
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.settings,
                    title: 'Farm Setup',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => const FarmInfoScreen()),
                      );
                    },
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.analytics,
                    title: 'Reports & Analytics',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const ReportsScreen(),
                        ),
                      );
                    },
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.notifications,
                    title: 'Notifications',
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const NotificationsScreen(),
                        ),
                      );
                    },
                    showDivider: true,
                  ),
                  _buildDrawerItem(
                    icon: Icons.help,
                    title: 'Help & Support',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Add Help screen
                    },
                    showDivider: true,
                  ),
                  const Divider(height: 1),
                  const Padding(
                    padding: EdgeInsets.all(16.0),
                    child: Text(
                      'Account',
                      style: TextStyle(
                        color: Colors.grey,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  _buildDrawerItem(
                    icon: Icons.person,
                    title: 'Profile',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Add Profile screen
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.settings,
                    title: 'Settings',
                    onTap: () {
                      Navigator.pop(context);
                      // TODO: Add Settings screen
                    },
                  ),
                  _buildDrawerItem(
                    icon: Icons.logout,
                    title: 'Logout',
                    onTap: () {
                      // Handle logout
                    },
                    iconColor: Colors.red,
                  ),
                ],
              ),
            ),
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'Version 1.0.0',
                style: TextStyle(
                  color: Colors.grey,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
