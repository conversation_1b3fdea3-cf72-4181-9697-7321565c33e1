/// Dynamic Filter Data Service
///
/// This service loads filter options dynamically from the database and settings
/// instead of using static data.

import 'package:flutter/material.dart';
import '../../../services/database/database_helper.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Farm Setup/models/breed_category_isar.dart';
import '../../Cattle/models/cattle_isar.dart';
import 'filters.dart';
import 'filter_constants.dart';

/// Service for loading dynamic filter data from database
class FilterDataService {
  static final FilterDataService _instance = FilterDataService._internal();
  static FilterDataService get instance => _instance;
  FilterDataService._internal();

  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;

  // Cache for loaded data to avoid repeated database calls
  List<AnimalTypeIsar>? _cachedAnimalTypes;
  List<BreedCategoryIsar>? _cachedBreeds;
  List<CattleIsar>? _cachedCattle;
  DateTime? _lastCacheUpdate;

  // Future cache to prevent race conditions
  Future<void>? _loadingFuture;

  // Cache duration (5 minutes)
  static const Duration _cacheDuration = Duration(minutes: 5);

  /// Check if cache is still valid
  bool get _isCacheValid {
    if (_lastCacheUpdate == null) return false;
    return DateTime.now().difference(_lastCacheUpdate!) < _cacheDuration;
  }

  /// Load all filter data from database
  /// Prevents race conditions by caching the loading Future
  Future<void> loadAllData() async {
    // If cache is still valid, return immediately
    if (_isCacheValid) return;

    // If already loading, return the existing Future to prevent race condition
    if (_loadingFuture != null) {
      return _loadingFuture!;
    }

    // Start new loading operation and cache the Future
    _loadingFuture = _performDataLoad();

    try {
      await _loadingFuture!;
    } finally {
      // Clear the loading Future when done (success or failure)
      _loadingFuture = null;
    }
  }

  /// Internal method that performs the actual data loading
  Future<void> _performDataLoad() async {
    try {
      // Load all data in parallel for better performance with type safety
      final animalTypesFuture = _databaseHelper.farmSetupHandler.getAllAnimalTypes();
      final breedsFuture = _databaseHelper.farmSetupHandler.getAllBreedCategories();
      final cattleFuture = _databaseHelper.cattleHandler.getAllCattle();

      // Wait for all futures to complete
      await Future.wait([animalTypesFuture, breedsFuture, cattleFuture]);

      // Now await each future individually for type safety
      _cachedAnimalTypes = await animalTypesFuture;
      _cachedBreeds = await breedsFuture;
      _cachedCattle = await cattleFuture;
      _lastCacheUpdate = DateTime.now();
    } catch (e) {
      debugPrint('Error loading filter data: $e');
      // Keep existing cache if available, otherwise use empty lists
      _cachedAnimalTypes ??= [];
      _cachedBreeds ??= [];
      _cachedCattle ??= [];
    }
  }



  /// Get animal type filter options
  Future<List<FilterOption>> getAnimalTypeOptions() async {
    await loadAllData();
    
    final options = <FilterOption>[
      const FilterOption(value: CommonFilterValues.all, label: 'All Animal Types'),
    ];

    for (final animalType in _cachedAnimalTypes ?? []) {
      if (animalType.name != null && animalType.businessId != null) {
        options.add(FilterOption(
          value: animalType.businessId!,
          label: animalType.name!,
          icon: animalType.iconCodePoint != null 
              ? IconData(animalType.iconCodePoint!, fontFamily: animalType.iconFontFamily)
              : null,
        ));
      }
    }

    return options;
  }

  /// Get breed filter options (all breeds or filtered by animal type)
  Future<List<FilterOption>> getBreedOptions({String? animalTypeId}) async {
    await loadAllData();
    
    final options = <FilterOption>[
      const FilterOption(value: CommonFilterValues.all, label: 'All Breeds'),
    ];

    List<BreedCategoryIsar> breedsToShow = _cachedBreeds ?? [];
    
    // Filter breeds by animal type if specified
    if (animalTypeId != null && animalTypeId != CommonFilterValues.all) {
      breedsToShow = breedsToShow.where((breed) => breed.animalTypeId == animalTypeId).toList();
    }

    for (final breed in breedsToShow) {
      if (breed.name != null && breed.businessId != null) {
        options.add(FilterOption(
          value: breed.businessId!,
          label: breed.name!,
          icon: breed.iconCodePoint != null 
              ? IconData(breed.iconCodePoint!, fontFamily: breed.iconFontFamily)
              : null,
          description: breed.description,
        ));
      }
    }

    return options;
  }

  /// Get cattle selection options
  Future<List<FilterOption>> getCattleOptions({String? animalTypeId, String? breedId}) async {
    await loadAllData();
    
    final options = <FilterOption>[
      const FilterOption(value: CommonFilterValues.all, label: 'All Cattle'),
    ];

    List<CattleIsar> cattleToShow = _cachedCattle ?? [];
    
    // Filter cattle by animal type if specified
    if (animalTypeId != null && animalTypeId != CommonFilterValues.all) {
      cattleToShow = cattleToShow.where((cattle) => cattle.animalTypeId == animalTypeId).toList();
    }

    // Filter cattle by breed if specified
    if (breedId != null && breedId != CommonFilterValues.all) {
      cattleToShow = cattleToShow.where((cattle) => cattle.breedId == breedId).toList();
    }

    for (final cattle in cattleToShow) {
      if (cattle.name != null && cattle.tagId != null) {
        final label = cattle.name!.isNotEmpty 
            ? '${cattle.name} (${cattle.tagId})'
            : cattle.tagId!;
            
        options.add(FilterOption(
          value: cattle.businessId ?? cattle.tagId!,
          label: label,
          description: 'Gender: ${cattle.gender ?? "Unknown"}, Age: ${_calculateAge(cattle)}',
        ));
      }
    }

    // Sort cattle by name for better UX
    final sortedOptions = options.skip(1).toList()..sort((a, b) => a.label.compareTo(b.label));
    return [options.first, ...sortedOptions];
  }

  /// Get gender filter options
  List<FilterOption> getGenderOptions() {
    return const [
      FilterOption(value: GenderFilterValues.all, label: FilterLabels.allGenders),
      FilterOption(value: GenderFilterValues.male, label: FilterLabels.male),
      FilterOption(value: GenderFilterValues.female, label: FilterLabels.female),
    ];
  }

  /// Get age group filter options for cattle
  List<FilterOption> getCattleAgeGroupOptions() {
    return const [
      FilterOption(value: AgeGroupFilterValues.all, label: FilterLabels.allAges),
      FilterOption(value: AgeGroupFilterValues.zeroToSixMonths, label: FilterLabels.zeroToSixMonths),
      FilterOption(value: AgeGroupFilterValues.sixMonthsToTwoYears, label: FilterLabels.sixMonthsToTwoYears),
      FilterOption(value: AgeGroupFilterValues.twoToEightYears, label: FilterLabels.twoToEightYears),
      FilterOption(value: AgeGroupFilterValues.eightPlusYears, label: FilterLabels.eightPlusYears),
    ];
  }

  /// Get status filter options for cattle
  List<FilterOption> getCattleStatusOptions() {
    return const [
      FilterOption(value: StatusFilterValues.all, label: FilterLabels.allStatus),
      FilterOption(value: StatusFilterValues.active, label: FilterLabels.active),
      FilterOption(value: StatusFilterValues.sold, label: FilterLabels.sold),
      FilterOption(value: StatusFilterValues.deceased, label: FilterLabels.deceased),
      FilterOption(value: StatusFilterValues.transferred, label: FilterLabels.transferred),
    ];
  }

  /// Get animal type name by ID
  Future<String?> getAnimalTypeName(String animalTypeId) async {
    await loadAllData();
    
    final animalType = _cachedAnimalTypes?.firstWhere(
      (type) => type.businessId == animalTypeId,
      orElse: () => AnimalTypeIsar(),
    );
    
    return animalType?.name;
  }

  /// Get breed name by ID
  Future<String?> getBreedName(String breedId) async {
    await loadAllData();
    
    final breed = _cachedBreeds?.firstWhere(
      (breed) => breed.businessId == breedId,
      orElse: () => BreedCategoryIsar(),
    );
    
    return breed?.name;
  }

  /// Get cattle name by ID
  Future<String?> getCattleName(String cattleId) async {
    await loadAllData();
    
    final cattle = _cachedCattle?.firstWhere(
      (cattle) => cattle.businessId == cattleId || cattle.tagId == cattleId,
      orElse: () => CattleIsar(),
    );
    
    return cattle?.name;
  }

  /// Clear cache to force reload on next access
  void clearCache() {
    _cachedAnimalTypes = null;
    _cachedBreeds = null;
    _cachedCattle = null;
    _lastCacheUpdate = null;
    _loadingFuture = null;
  }

  /// Calculate age string for cattle
  String _calculateAge(CattleIsar cattle) {
    final now = DateTime.now();
    final birthDate = cattle.dateOfBirth ?? 
                     cattle.purchaseDate?.add(const Duration(days: 365)) ?? // Assume 1 year old if purchased
                     now;
    
    final ageInDays = now.difference(birthDate).inDays;
    
    if (ageInDays < 30) {
      return '${ageInDays}d';
    } else if (ageInDays < 365) {
      final months = (ageInDays / 30.44).round();
      return '${months}m';
    } else {
      final years = (ageInDays / 365.25).round();
      return '${years}y';
    }
  }

  /// Get breeds for specific animal type (for dependent filtering)
  Future<List<BreedCategoryIsar>> getBreedsForAnimalType(String animalTypeId) async {
    if (animalTypeId == CommonFilterValues.all) {
      await loadAllData();
      return _cachedBreeds ?? [];
    }

    try {
      return await _databaseHelper.farmSetupHandler.getBreedCategoriesForAnimalType(animalTypeId);
    } catch (e) {
      debugPrint('Error loading breeds for animal type $animalTypeId: $e');
      return [];
    }
  }
}
