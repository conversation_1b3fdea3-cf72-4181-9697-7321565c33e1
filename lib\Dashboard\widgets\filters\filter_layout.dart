/// Universal Filter System - Layout and Dialog Management
///
/// This file contains the main filter layout with consistent button and dialog designs.

import 'package:flutter/material.dart';
import 'filters.dart';
import 'filter_widgets.dart';
import 'filter_data_service.dart';
import '../../../constants/app_layout.dart';


/// Layout configuration for filter components
class FilterLayoutConfig {
  final bool compact;
  final bool showSearch;
  final bool showStatusBar;
  final EdgeInsets? padding;
  final double? buttonSpacing;

  const FilterLayoutConfig({
    this.compact = false,
    this.showSearch = true,
    this.showStatusBar = true,
    this.padding,
    this.buttonSpacing,
  });

  static const FilterLayoutConfig standard = FilterLayoutConfig();
  static const FilterLayoutConfig compactConfig = FilterLayoutConfig(compact: true);
}

/// Universal filter layout widget that combines all filter components
class UniversalFilterLayout extends StatelessWidget {
  final FilterController controller;
  final FilterTheme theme;
  final String? moduleName;
  final List<SortField> sortFields;
  final String searchHint;
  final int? totalCount;
  final int? filteredCount;
  final FilterLayoutConfig config;

  const UniversalFilterLayout({
    Key? key,
    required this.controller,
    required this.theme,
    this.moduleName,
    required this.sortFields,
    required this.searchHint,
    this.totalCount,
    this.filteredCount,
    this.config = FilterLayoutConfig.standard,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller,
      builder: (context, child) {
        final hasActiveFilters = controller.isAnyFilterActive;

        return Container(
          padding: config.padding ?? const EdgeInsets.all(16),
          child: Column(
            children: [
              // Filter Buttons Row
              _buildFilterButtonsRow(context),

              // Search Widget - Only show when no filters are active
              if (config.showSearch && !hasActiveFilters) ...[
                SizedBox(height: config.compact ? 8 : 12),
                UniversalSearchWidget(
                  controller: controller,
                  hintText: searchHint,
                  theme: theme,
                  config: config.compact ? WidgetConfig.compactConfig : WidgetConfig.standard,
                ),
              ],

              // Active Filters Row - Show when filters are active
              if (hasActiveFilters) ...[
                SizedBox(height: config.compact ? 8 : 12),
                _buildActiveFiltersRow(context),
              ],

              // Filter Status Bar - Show count and clear all
              if (config.showStatusBar && hasActiveFilters)
                _buildFilterStatusBar(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFilterButtonsRow(BuildContext context) {
    final buttonSpacing = config.buttonSpacing ?? (config.compact ? 6.0 : 8.0);
    
    return Row(
      children: [
        // Filters Button
        Expanded(
          child: _buildFilterButton(
            context: context,
            icon: Icons.tune,
            label: 'Filters',
            isActive: controller.hasActiveFilters,
            onPressed: () => _showFiltersDialog(context),
          ),
        ),
        SizedBox(width: buttonSpacing),
        
        // Date Button
        Expanded(
          child: _buildFilterButton(
            context: context,
            icon: Icons.date_range,
            label: 'Date',
            isActive: controller.startDate != null,
            onPressed: () => _showDateDialog(context),
          ),
        ),
        SizedBox(width: buttonSpacing),
        
        // Sort Button
        Expanded(
          child: _buildFilterButton(
            context: context,
            icon: Icons.sort,
            label: 'Sort',
            isActive: controller.sortBy != null,
            onPressed: () => _showSortDialog(context),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(
        icon,
        size: config.compact ? 14 : 16,
        color: isActive ? Colors.white : Colors.purple,
      ),
      label: Text(
        label,
        style: TextStyle(
          fontSize: config.compact ? 12 : 14,
          fontWeight: FontWeight.w500,
          color: isActive ? Colors.white : Colors.purple,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: isActive ? Colors.purple : Colors.white,
        foregroundColor: isActive ? Colors.white : Colors.purple,
        side: const BorderSide(color: Colors.purple, width: 1.5),
        padding: EdgeInsets.symmetric(
          horizontal: config.compact ? 12 : 16,
          vertical: config.compact ? 8 : 12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(25), // Rounded pill shape
        ),
        elevation: 0,
        minimumSize: Size(0, config.compact ? 36 : 44),
      ),
    );
  }



  /// Build active filters display row with sort chip fixed on right
  Widget _buildActiveFiltersRow(BuildContext context) {
    final activeFilters = <Widget>[];
    final hasSort = controller.sortBy != null;

    // Search filter chip
    if (controller.searchQuery.isNotEmpty) {
      activeFilters.add(_buildFilterChip(
        label: 'Search: "${controller.searchQuery}"',
        icon: Icons.search,
        color: Colors.purple,
        onRemove: () {
          controller.setSearchQuery('');
          controller.applyFilters();
        },
      ));
    }

    // Date filter chip
    if (controller.startDate != null) {
      final dateText = controller.endDate != null
          ? '${_formatDate(controller.startDate!)} - ${_formatDate(controller.endDate!)}'
          : 'From ${_formatDate(controller.startDate!)}';
      activeFilters.add(_buildFilterChip(
        label: 'Date: $dateText',
        icon: Icons.date_range,
        color: _getColorForFilterKey('date'),
        onRemove: () {
          controller.setDateRange(null, null);
          controller.applyFilters();
        },
      ));
    }

    // Sort filter chip (WITHOUT arrow - just the label)
    if (hasSort) {
      activeFilters.add(_buildFilterChip(
        label: 'Sort: ${controller.sortBy}',
        icon: Icons.sort,
        color: Colors.purple,
        onRemove: () {
          controller.setSortBy('');
          controller.applyFilters();
        },
      ));
    }

    // Global filters
    controller.globalFilters.forEach((key, value) {
      if (value != null && value != 'All') {
        activeFilters.add(_buildFilterChipWithDisplayName(
          key: key,
          value: value,
          onRemove: () {
            controller.updateGlobalFilter(key, null);
            controller.applyFilters();
          },
        ));
      }
    });

    // Module-specific filters
    controller.activeFilters.forEach((key, value) {
      if (value != null && value != 'All') {
        activeFilters.add(_buildFilterChipWithDisplayName(
          key: key,
          value: value,
          onRemove: () {
            controller.updateFilter(key, null);
            controller.applyFilters();
          },
        ));
      }
    });

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // Left side: Active filter chips (expandable)
          Expanded(
            child: _buildFilterChipsLayout(activeFilters),
          ),

          // Right side: Sort arrow chip only (fixed position)
          if (hasSort) ...[
            const SizedBox(width: 8),
            _buildSortArrowChip(),
          ],
        ],
      ),
    );
  }

  /// Build layout for filter chips with 2-row wrapping when 4+ filters
  Widget _buildFilterChipsLayout(List<Widget> filterChips) {
    if (filterChips.isEmpty) {
      return const SizedBox.shrink();
    }

    // If 4 or more filters, use 2 rows
    if (filterChips.length >= 4) {
      final firstRowCount = (filterChips.length / 2).ceil();
      final firstRow = filterChips.take(firstRowCount).toList();
      final secondRow = filterChips.skip(firstRowCount).toList();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 8,
            children: firstRow,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: secondRow,
          ),
        ],
      );
    } else {
      // Single row for 3 or fewer filters
      return Wrap(
        spacing: 8,
        children: filterChips,
      );
    }
  }

  /// Build sort arrow chip (arrow only, no label)
  Widget _buildSortArrowChip() {
    return GestureDetector(
      onTap: () {
        controller.setSortDirectionAndApply(!controller.isAscending);
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.purple,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.purple,
            width: 1
          ),
        ),
        child: Icon(
          controller.isAscending ? Icons.arrow_upward : Icons.arrow_downward,
          size: 16,
          color: Colors.white,
        ),
      ),
    );
  }

  /// Build unified styled filter chip with consistent design
  Widget _buildStyledFilterChip({
    required String label,
    required IconData icon,
    required Color color,
    VoidCallback? onRemove,
    VoidCallback? onTap,
    bool flexible = true,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: color, width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 16, color: color),
            const SizedBox(width: 4),
            if (flexible)
              Flexible(
                child: Text(
                  label,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              )
            else
              Text(
                label,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            if (onRemove != null) ...[
              const SizedBox(width: 4),
              GestureDetector(
                onTap: onRemove,
                child: Icon(
                  Icons.close,
                  size: 16,
                  color: color,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Build individual filter chip (wrapper for styled chip)
  Widget _buildFilterChip({
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback onRemove,
  }) {
    return _buildStyledFilterChip(
      label: label,
      icon: icon,
      color: color,
      onRemove: onRemove,
      flexible: true,
    );
  }

  /// Build filter chip with proper display names (handles async conversion)
  Widget _buildFilterChipWithDisplayName({
    required String key,
    required String value,
    required VoidCallback onRemove,
  }) {
    return FutureBuilder<String>(
      future: _getFilterDisplayValue(key, value),
      builder: (context, snapshot) {
        final displayValue = snapshot.data ?? value;
        final keyLabel = _getFilterKeyLabel(key);

        return _buildStyledFilterChip(
          label: '$keyLabel: $displayValue',
          icon: _getIconForFilterKey(key),
          color: _getColorForFilterKey(key),
          onRemove: onRemove,
          flexible: true,
        );
      },
    );
  }

  /// Build improved status bar with count and clear all (chip style)
  Widget _buildFilterStatusBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Record count chip
          if (totalCount != null && filteredCount != null)
            _buildStyledFilterChip(
              label: 'Showing $filteredCount of $totalCount records',
              icon: Icons.filter_list,
              color: Colors.purple,
              flexible: false,
            )
          else
            _buildStyledFilterChip(
              label: 'Filters active',
              icon: Icons.filter_list,
              color: Colors.purple,
              flexible: false,
            ),

          // Clear all chip
          _buildStyledFilterChip(
            label: 'Clear All',
            icon: Icons.clear_all,
            color: Colors.purple,
            flexible: false,
            onTap: () {
              controller.clearAllApplied();
            },
          ),
        ],
      ),
    );
  }

  /// Helper method to format dates
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Get icon for filter key
  IconData _getIconForFilterKey(String key) {
    switch (key.toLowerCase()) {
      case 'animal_type':
      case 'animaltype':
        return Icons.pets;
      case 'breed':
        return Icons.category;
      case 'gender':
        return Icons.wc;
      case 'cattle':
        return Icons.agriculture;
      case 'status':
        return Icons.info;
      case 'category':
        return Icons.label;
      default:
        return Icons.filter_alt;
    }
  }

  /// Get color for filter key
  Color _getColorForFilterKey(String key) {
    // All filter chips use purple color for consistency
    return Colors.purple;
  }

  /// Get user-friendly label for filter key
  String _getFilterKeyLabel(String key) {
    switch (key.toLowerCase()) {
      case 'animal_type':
      case 'animaltype':
        return 'Type';
      case 'breed':
        return 'Breed';
      case 'gender':
        return 'Gender';
      case 'cattle':
        return 'Cattle';
      case 'agegroup':
        return 'Age Group';
      case 'status':
        return 'Status';
      case 'category':
        return 'Category';
      default:
        return key.replaceAll('_', ' ').split(' ').map((word) =>
          word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : word
        ).join(' ');
    }
  }

  /// Get user-friendly display value for filter
  Future<String> _getFilterDisplayValue(String key, String value) async {
    switch (key.toLowerCase()) {
      case 'animal_type':
      case 'animaltype':
        final name = await FilterDataService.instance.getAnimalTypeName(value);
        return name ?? value;
      case 'breed':
        final name = await FilterDataService.instance.getBreedName(value);
        return name ?? value;
      case 'cattle':
        // For cattle, get the display name in format "Name (TagID)"
        final displayName = await FilterDataService.instance.getCattleDisplayName(value);
        return displayName ?? value;
      case 'sort':
        // Convert sort field keys to user-friendly labels
        return _getSortFieldLabel(value);
      case 'date':
        // Date values are already formatted nicely
        return value;
      case 'gender':
        // Gender values are already user-friendly
        return value;
      case 'agegroup':
        // Age group values are already user-friendly
        return value;
      default:
        return value;
    }
  }

  /// Get user-friendly label for sort field key
  String _getSortFieldLabel(String sortKey) {
    switch (sortKey.toLowerCase()) {
      // Common fields
      case 'name':
        return 'Name';
      case 'createdat':
        return 'Date Added';

      // Cattle fields
      case 'tagid':
        return 'Tag ID';
      case 'dateofbirth':
        return 'Date of Birth';
      case 'purchasedate':
        return 'Purchase Date';

      // Transaction fields
      case 'date':
        return 'Transaction Date';
      case 'amount':
        return 'Amount';
      case 'category':
        return 'Category';
      case 'description':
        return 'Description';

      // Weight fields
      case 'measurementdate':
        return 'Measurement Date';
      case 'weight':
        return 'Weight';
      case 'weightgain':
        return 'Weight Gain';
      case 'measurementmethod':
        return 'Measurement Method';

      // Breeding fields
      case 'breedingdate':
        return 'Breeding Date';
      case 'expecteddeliverydate':
        return 'Expected Delivery';
      case 'method':
        return 'Method';
      case 'status':
        return 'Status';

      // Health fields
      case 'treatmentdate':
        return 'Treatment Date';
      case 'treatmenttype':
        return 'Treatment Type';
      case 'veterinarian':
        return 'Veterinarian';

      // Milk fields
      case 'milkingdate':
        return 'Milking Date';
      case 'quantity':
        return 'Quantity';
      case 'session':
        return 'Session';

      default:
        // Fallback: capitalize first letter and replace underscores with spaces
        return sortKey.replaceAll('_', ' ').split(' ').map((word) =>
          word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : word
        ).join(' ');
    }
  }

  void _showFiltersDialog(BuildContext context) {
    debugPrint('🔍 _showFiltersDialog - Module: $moduleName');

    if (moduleName != null) {
      // Create a GlobalKey to access the AppFilterWidget's state for clear functionality
      final GlobalKey<State<AppFilterWidget>> appFilterKey = GlobalKey<State<AppFilterWidget>>();

      FilterDialogManager.showFilterDialog(
        context: context,
        title: 'Filter Options',
        icon: Icons.filter_alt,
        content: AppFilterWidget(
          key: appFilterKey,
          filterController: controller,
          moduleName: moduleName!,
          compact: config.compact,
        ),
        onApply: () {
          debugPrint('🔍 _showFiltersDialog - Apply button pressed');
          controller.applyFilters();
        },
        onClear: () {
          debugPrint('🔍 _showFiltersDialog - Clear button pressed');
          // Reset all filters to initial state and update UI
          (appFilterKey.currentState as AppFilterWidgetController?)?.resetFilters();
        },
        onClose: () {
          debugPrint('🔍 _showFiltersDialog - Close button pressed');
        },
        requiresApply: true,
        initializePending: true,
        controller: controller,
      );
    } else {
      debugPrint('🔍 _showFiltersDialog - Module name is null, cannot show dialog');
    }
  }

  void _showDateDialog(BuildContext context) {
    debugPrint('🔍 _showDateDialog called');

    FilterDialogManager.showFilterDialog(
      context: context,
      title: 'Select Date Range',
      icon: Icons.date_range,
      content: UniversalDateFilterWidget(
        controller: controller,
        theme: theme,
        config: config.compact ? WidgetConfig.compactConfig : WidgetConfig.standard,
      ),
      onClose: () {
        debugPrint('🔍 _showDateDialog - Close button pressed');
      },
      requiresApply: false, // Date filters apply immediately
      initializePending: true,
      controller: controller,
    );
  }

  void _showSortDialog(BuildContext context) {
    debugPrint('🔍 _showSortDialog called');

    FilterDialogManager.showFilterDialog(
      context: context,
      title: 'Sort Options',
      icon: Icons.sort,
      content: UniversalSortWidget(
        controller: controller,
        sortFields: sortFields,
        theme: theme,
        config: config.compact ? WidgetConfig.compactConfig : WidgetConfig.standard,
      ),
      onClose: () {
        debugPrint('🔍 _showSortDialog - Close button pressed');
      },
      requiresApply: false, // Sort filters apply immediately
      initializePending: true,
      controller: controller,
    );
  }
}

/// Centralized dialog manager for all filter dialogs using UniversalFormDialog
class FilterDialogManager {
  /// Show filter dialog with consistent theming and button configurations
  static Future<void> showFilterDialog({
    required BuildContext context,
    required String title,
    required IconData icon,
    required Widget content,
    VoidCallback? onApply,
    VoidCallback? onClear,
    VoidCallback? onClose,
    bool requiresApply = false,
    bool initializePending = false,
    FilterController? controller,
    double? maxWidth,
    double? maxHeight,
  }) {
    debugPrint('🔍 FilterDialogManager.showFilterDialog - Title: $title');
    debugPrint('🔍 FilterDialogManager - RequiresApply: $requiresApply, InitializePending: $initializePending');

    // Initialize pending state if required
    if (initializePending && controller != null) {
      controller.initializePendingState();
    }

    return showDialog(
      context: context,
      builder: (context) => UniversalFormDialog(
        title: title,
        headerIcon: icon,
        formContent: content,
        actionButtons: _buildActionButtons(
          context: context,
          onApply: onApply,
          onClear: onClear,
          onClose: onClose,
          requiresApply: requiresApply,
        ),
        maxWidth: maxWidth,
        maxHeight: maxHeight,
      ),
    );
  }

  /// Build appropriate action buttons based on dialog type
  static Widget _buildActionButtons({
    required BuildContext context,
    VoidCallback? onApply,
    VoidCallback? onClear,
    VoidCallback? onClose,
    bool requiresApply = false,
  }) {
    if (requiresApply && onApply != null) {
      // Filter dialog with Close, Clear, Apply buttons
      return Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                onClose?.call();
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF1976D2), // Blue - Close
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 14),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                elevation: 2,
              ),
              child: const Text('Close', style: TextStyle(fontWeight: FontWeight.w600)),
            ),
          ),
          if (onClear != null) ...[
            const SizedBox(width: 8),
            Expanded(
              child: ElevatedButton(
                onPressed: onClear,
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF7B1FA2), // Purple - Clear
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 14),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  elevation: 2,
                ),
                child: const Text('Clear All', style: TextStyle(fontWeight: FontWeight.w600)),
              ),
            ),
          ],
          const SizedBox(width: 8),
          Expanded(
            child: ElevatedButton(
              onPressed: () {
                onApply();
                Navigator.of(context).pop();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2E7D32), // Green - Apply
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 14),
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                elevation: 2,
              ),
              child: const Text('Apply', style: TextStyle(fontWeight: FontWeight.w600)),
            ),
          ),
        ],
      );
    } else {
      // Simple dialog with just Close button (for date/sort dialogs that apply immediately)
      return ElevatedButton(
        onPressed: () {
          onClose?.call();
          Navigator.of(context).pop();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF1976D2), // Blue - Close
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
          elevation: 2,
        ),
        child: const Text('Close', style: TextStyle(fontWeight: FontWeight.w600)),
      );
    }
  }
}
