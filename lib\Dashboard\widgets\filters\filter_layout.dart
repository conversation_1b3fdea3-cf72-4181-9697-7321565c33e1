/// Universal Filter System - Layout and Dialog Management
///
/// This file contains the main filter layout with consistent button and dialog designs.

import 'package:flutter/material.dart';
import 'filters.dart';
import 'filter_widgets.dart';
import '../../../constants/app_layout.dart';


/// Layout configuration for filter components
class FilterLayoutConfig {
  final bool compact;
  final bool showSearch;
  final bool showStatusBar;
  final EdgeInsets? padding;
  final double? buttonSpacing;

  const FilterLayoutConfig({
    this.compact = false,
    this.showSearch = true,
    this.showStatusBar = true,
    this.padding,
    this.buttonSpacing,
  });

  static const FilterLayoutConfig standard = FilterLayoutConfig();
  static const FilterLayoutConfig compactConfig = FilterLayoutConfig(compact: true);
}

/// Universal filter layout widget that combines all filter components
class UniversalFilterLayout extends StatelessWidget {
  final FilterController controller;
  final FilterTheme theme;
  final String? moduleName;
  final List<SortField> sortFields;
  final String searchHint;
  final int? totalCount;
  final int? filteredCount;
  final FilterLayoutConfig config;

  const UniversalFilterLayout({
    Key? key,
    required this.controller,
    required this.theme,
    this.moduleName,
    required this.sortFields,
    required this.searchHint,
    this.totalCount,
    this.filteredCount,
    this.config = FilterLayoutConfig.standard,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: controller,
      builder: (context, child) {
        final hasActiveFilters = controller.isAnyFilterActive;

        return Container(
          padding: config.padding ?? const EdgeInsets.all(16),
          child: Column(
            children: [
              // Filter Buttons Row
              _buildFilterButtonsRow(context),

              // Search Widget - Only show when no filters are active
              if (config.showSearch && !hasActiveFilters) ...[
                SizedBox(height: config.compact ? 8 : 12),
                UniversalSearchWidget(
                  controller: controller,
                  hintText: searchHint,
                  theme: theme,
                  config: config.compact ? WidgetConfig.compactConfig : WidgetConfig.standard,
                ),
              ],

              // Active Filters Row - Show when filters are active
              if (hasActiveFilters) ...[
                SizedBox(height: config.compact ? 8 : 12),
                _buildActiveFiltersRow(context),
              ],

              // Filter Status Bar - Show count and clear all
              if (config.showStatusBar && hasActiveFilters)
                _buildFilterStatusBar(context),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFilterButtonsRow(BuildContext context) {
    final buttonSpacing = config.buttonSpacing ?? (config.compact ? 6.0 : 8.0);
    
    return Row(
      children: [
        // Filters Button
        Expanded(
          child: _buildFilterButton(
            context: context,
            icon: Icons.tune,
            label: 'Filters',
            isActive: _hasActiveFilters(),
            onPressed: () => _showFiltersDialog(context),
          ),
        ),
        SizedBox(width: buttonSpacing),
        
        // Date Button
        Expanded(
          child: _buildFilterButton(
            context: context,
            icon: Icons.date_range,
            label: 'Date',
            isActive: controller.startDate != null,
            onPressed: () => _showDateDialog(context),
          ),
        ),
        SizedBox(width: buttonSpacing),
        
        // Sort Button
        Expanded(
          child: _buildFilterButton(
            context: context,
            icon: Icons.sort,
            label: 'Sort',
            isActive: controller.sortBy != null,
            onPressed: () => _showSortDialog(context),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterButton({
    required BuildContext context,
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onPressed,
  }) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(
        icon,
        size: config.compact ? 14 : 16,
        color: isActive ? Colors.white : Colors.purple,
      ),
      label: Text(
        label,
        style: TextStyle(
          fontSize: config.compact ? 12 : 14,
          fontWeight: FontWeight.w500,
          color: isActive ? Colors.white : Colors.purple,
        ),
      ),
      style: ElevatedButton.styleFrom(
        backgroundColor: isActive ? Colors.purple : Colors.white,
        foregroundColor: isActive ? Colors.white : Colors.purple,
        side: const BorderSide(color: Colors.purple, width: 1.5),
        padding: EdgeInsets.symmetric(
          horizontal: config.compact ? 12 : 16,
          vertical: config.compact ? 8 : 12,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(25), // Rounded pill shape
        ),
        elevation: 0,
        minimumSize: Size(0, config.compact ? 36 : 44),
      ),
    );
  }

  bool _hasActiveFilters() {
    return controller.hasActiveFilters;
  }

  /// Build active filters display row with sort chip fixed on right
  Widget _buildActiveFiltersRow(BuildContext context) {
    final activeFilters = <Widget>[];
    final hasSort = controller.sortBy != null;

    // Search filter chip
    if (controller.searchQuery.isNotEmpty) {
      activeFilters.add(_buildFilterChip(
        label: 'Search: "${controller.searchQuery}"',
        icon: Icons.search,
        color: Colors.blue,
        onRemove: () {
          controller.setSearchQuery('');
          controller.applyFilters();
        },
      ));
    }

    // Date filter chip
    if (controller.startDate != null) {
      final dateText = controller.endDate != null
          ? '${_formatDate(controller.startDate!)} - ${_formatDate(controller.endDate!)}'
          : 'From ${_formatDate(controller.startDate!)}';
      activeFilters.add(_buildFilterChip(
        label: 'Date: $dateText',
        icon: Icons.date_range,
        color: Colors.green,
        onRemove: () {
          controller.setDateRange(null, null);
          controller.applyFilters();
        },
      ));
    }

    // Global filters
    controller.globalFilters.forEach((key, value) {
      if (value != null && value != 'All') {
        activeFilters.add(_buildFilterChip(
          label: '$key: $value',
          icon: _getIconForFilterKey(key),
          color: _getColorForFilterKey(key),
          onRemove: () {
            controller.updateGlobalFilter(key, null);
            controller.applyFilters();
          },
        ));
      }
    });

    // Module-specific filters
    controller.activeFilters.forEach((key, value) {
      if (value != null && value != 'All') {
        activeFilters.add(_buildFilterChip(
          label: '$key: $value',
          icon: _getIconForFilterKey(key),
          color: _getColorForFilterKey(key),
          onRemove: () {
            controller.updateFilter(key, null);
            controller.applyFilters();
          },
        ));
      }
    });

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          // Left side: Active filter chips (expandable)
          Expanded(
            child: _buildFilterChipsLayout(activeFilters),
          ),

          // Right side: Sort chip (fixed position)
          if (hasSort) ...[
            const SizedBox(width: 8),
            _buildSortFilterChip(),
          ],
        ],
      ),
    );
  }

  /// Build layout for filter chips with 2-row wrapping when 4+ filters
  Widget _buildFilterChipsLayout(List<Widget> filterChips) {
    if (filterChips.isEmpty) {
      return const SizedBox.shrink();
    }

    // If 4 or more filters, use 2 rows
    if (filterChips.length >= 4) {
      final firstRowCount = (filterChips.length / 2).ceil();
      final firstRow = filterChips.take(firstRowCount).toList();
      final secondRow = filterChips.skip(firstRowCount).toList();

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Wrap(
            spacing: 8,
            children: firstRow,
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: secondRow,
          ),
        ],
      );
    } else {
      // Single row for 3 or fewer filters
      return Wrap(
        spacing: 8,
        children: filterChips,
      );
    }
  }

  /// Build sort filter chip with interactive arrow
  Widget _buildSortFilterChip() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.purple.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.purple, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.sort, size: 16, color: Colors.purple),
          const SizedBox(width: 4),
          Text(
            'Sort: ${controller.sortBy}',
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Colors.purple,
            ),
          ),
          const SizedBox(width: 8),
          // Interactive sort arrow
          GestureDetector(
            onTap: () {
              controller.setSortDirectionAndApply(!controller.isAscending);
            },
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: controller.isAscending ? Colors.green : Colors.orange,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Icon(
                controller.isAscending ? Icons.arrow_upward : Icons.arrow_downward,
                size: 14,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(width: 4),
          // Remove sort filter
          GestureDetector(
            onTap: () {
              controller.setSortBy(null);
              controller.applyFilters();
            },
            child: const Icon(
              Icons.close,
              size: 16,
              color: Colors.purple,
            ),
          ),
        ],
      ),
    );
  }

  /// Build individual filter chip
  Widget _buildFilterChip({
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback onRemove,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: color, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 16, color: color),
          const SizedBox(width: 4),
          Flexible(
            child: Text(
              label,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: color,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 4),
          GestureDetector(
            onTap: onRemove,
            child: Icon(
              Icons.close,
              size: 16,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// Build improved status bar with count and clear all
  Widget _buildFilterStatusBar(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Record count
          if (totalCount != null && filteredCount != null)
            Text(
              'Showing $filteredCount of $totalCount records',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            )
          else
            const Text(
              'Filters active',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),

          // Clear all button
          TextButton.icon(
            onPressed: () {
              controller.clearAllApplied();
            },
            icon: const Icon(Icons.clear_all, size: 16, color: Colors.red),
            label: const Text(
              'Clear All',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.red,
              ),
            ),
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      ),
    );
  }

  /// Helper method to format dates
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// Get icon for filter key
  IconData _getIconForFilterKey(String key) {
    switch (key.toLowerCase()) {
      case 'animal_type':
      case 'animaltype':
        return Icons.pets;
      case 'breed':
        return Icons.category;
      case 'gender':
        return Icons.wc;
      case 'cattle':
        return Icons.agriculture;
      case 'status':
        return Icons.info;
      case 'category':
        return Icons.label;
      default:
        return Icons.filter_alt;
    }
  }

  /// Get color for filter key
  Color _getColorForFilterKey(String key) {
    switch (key.toLowerCase()) {
      case 'animal_type':
      case 'animaltype':
        return Colors.green;
      case 'breed':
        return Colors.blue;
      case 'gender':
        return Colors.pink;
      case 'cattle':
        return Colors.brown;
      case 'status':
        return Colors.orange;
      case 'category':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  void _showFiltersDialog(BuildContext context) {
    debugPrint('🔍 _showFiltersDialog - Module: $moduleName');

    if (moduleName != null) {
      // Initialize pending state before showing dialog
      debugPrint('🔍 _showFiltersDialog - Initializing pending state');
      controller.initializePendingState();

      // Create a GlobalKey to access the AppFilterWidget's state
      final GlobalKey<State<AppFilterWidget>> appFilterKey = GlobalKey<State<AppFilterWidget>>();

      debugPrint('🔍 _showFiltersDialog - Creating AppFilterWidget');
      showDialog(
        context: context,
        builder: (context) => UniversalFormDialog(
          title: 'Filter Options',
          headerIcon: Icons.filter_alt,
          formContent: AppFilterWidget(
            key: appFilterKey,
            filterController: controller,
            moduleName: moduleName!,
            compact: config.compact,
          ),
          actionButtons: Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF1976D2), // Blue - Close (dark color)
                    foregroundColor: Colors.white, // White text on dark color
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 14),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    elevation: 2,
                  ),
                  child: const Text('Close', style: TextStyle(fontWeight: FontWeight.w600)),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    debugPrint('🔍 _showFiltersDialog - Clear button pressed');
                    // Reset all filters to initial state and update UI
                    (appFilterKey.currentState as AppFilterWidgetController?)?.resetFilters();
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF7B1FA2), // Purple - Clear (dark color)
                    foregroundColor: Colors.white, // White text on dark color
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 14),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    elevation: 2,
                  ),
                  child: const Text('Clear All', style: TextStyle(fontWeight: FontWeight.w600)),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    debugPrint('🔍 _showFiltersDialog - Apply button pressed');
                    controller.applyFilters();
                    Navigator.of(context).pop(); // Close dialog
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2E7D32), // Green - Apply (dark color)
                    foregroundColor: Colors.white, // White text on dark color
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 14),
                    shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                    elevation: 2,
                  ),
                  child: const Text('Apply', style: TextStyle(fontWeight: FontWeight.w600)),
                ),
              ),
            ],
          ),
        ),
      );
    } else {
      debugPrint('🔍 _showFiltersDialog - Module name is null, cannot show dialog');
    }
  }

  void _showDateDialog(BuildContext context) {
    // Initialize pending state before showing dialog
    controller.initializePendingState();

    showDialog(
      context: context,
      builder: (context) => UniversalFormDialog(
        title: 'Select Date Range',
        headerIcon: Icons.date_range,
        formContent: UniversalDateFilterWidget(
          controller: controller,
          theme: theme,
          config: config.compact ? WidgetConfig.compactConfig : WidgetConfig.standard,
        ),
        actionButtons: ElevatedButton(
          onPressed: () => Navigator.of(context).pop(),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF1976D2), // Blue - Close
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            elevation: 2,
          ),
          child: const Text('Close', style: TextStyle(fontWeight: FontWeight.w600)),
        ),
      ),
    );
  }

  void _showSortDialog(BuildContext context) {
    // Initialize pending state before showing dialog
    controller.initializePendingState();

    final fields = sortFields;

    showDialog(
      context: context,
      builder: (context) => UniversalFormDialog(
        title: 'Sort Options',
        headerIcon: Icons.sort,
        formContent: UniversalSortWidget(
          controller: controller,
          sortFields: fields,
          theme: theme,
          config: config.compact ? WidgetConfig.compactConfig : WidgetConfig.standard,
        ),
        actionButtons: ElevatedButton(
          onPressed: () => Navigator.of(context).pop(),
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF1976D2), // Blue - Close
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
            elevation: 2,
          ),
          child: const Text('Close', style: TextStyle(fontWeight: FontWeight.w600)),
        ),
      ),
    );
  }
}

/// Dialog manager for consistent dialog presentations
class FilterDialogManager {
  /// Show filter dialog with consistent theming
  static Future<void> showFilterDialog({
    required BuildContext context,
    required String title,
    required Widget content,
    required FilterTheme theme,
    double? maxWidth,
    double? maxHeight,
    VoidCallback? onClear,
    VoidCallback? onApply,
  }) {
    debugPrint('🔍 FilterDialogManager.showFilterDialog - Title: $title');
    debugPrint('🔍 FilterDialogManager - MaxWidth: $maxWidth, MaxHeight: $maxHeight');
    debugPrint('🔍 FilterDialogManager - Content type: ${content.runtimeType}');
    return showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          constraints: BoxConstraints(
            maxWidth: maxWidth ?? 400,
            maxHeight: maxHeight ?? 600,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Fixed Header with solid color background and white text/icons
              Container(
                padding: const EdgeInsets.all(16),
                decoration: const BoxDecoration(
                  color: Colors.green, // Primary color
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        _getIconForTitle(title),
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close, color: Colors.white),
                    ),
                  ],
                ),
              ),
              // Scrollable Content
              Flexible(
                child: Builder(
                  builder: (context) {
                    debugPrint('🔍 FilterDialogManager - Building content in Flexible');
                    return content;
                  },
                ),
              ),
              // Fixed Footer with colored action buttons
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[50],
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    // Close Button (Blue)
                    Expanded(
                      child: ElevatedButton(
                        onPressed: () => Navigator.of(context).pop(),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text(
                          'Close',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                    if (onClear != null) ...[
                      const SizedBox(width: 12),
                      // Clear Button (Purple)
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            onClear();
                            // Don't close dialog - let user see cleared state and apply if desired
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.purple,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            'Clear All',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                    if (onApply != null) ...[
                      const SizedBox(width: 12),
                      // Apply Button (Green)
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () {
                            onApply();
                            Navigator.of(context).pop();
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text(
                            'Apply',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Get appropriate icon for dialog title
  static IconData _getIconForTitle(String title) {
    final titleLower = title.toLowerCase();
    if (titleLower.contains('filter')) return Icons.tune;
    if (titleLower.contains('date')) return Icons.date_range;
    if (titleLower.contains('sort')) return Icons.sort;
    if (titleLower.contains('search')) return Icons.search;
    return Icons.settings;
  }
}
