import 'package:flutter/material.dart';

/// Centralized helper class for filter visual elements (colors, icons, labels)
/// This provides a single source of truth for all filter presentation logic
class FilterVisuals {
  FilterVisuals._(); // Private constructor to prevent instantiation

  /// Get icon for filter key
  static IconData getIconForFilter<PERSON>ey(String key) {
    switch (key.toLowerCase()) {
      case 'animal_type':
      case 'animaltype':
        return Icons.pets;
      case 'breed':
        return Icons.category;
      case 'cattle':
        return Icons.agriculture;
      case 'gender':
        return Icons.wc;
      case 'agegroup':
        return Icons.cake;
      case 'date':
        return Icons.date_range;
      case 'sort':
        return Icons.sort;
      case 'search':
        return Icons.search;
      default:
        return Icons.filter_alt;
    }
  }

  /// Get color for filter key
  static Color getColorForFilterKey(String key) {
    // All filter chips use purple color as per user preference
    return Colors.purple;
  }

  /// Get user-friendly label for filter key
  static String getFilterKeyLabel(String key) {
    switch (key.toLowerCase()) {
      case 'animal_type':
      case 'animaltype':
        return 'Type';
      case 'breed':
        return 'Breed';
      case 'cattle':
        return 'Cattle';
      case 'gender':
        return 'Gender';
      case 'agegroup':
        return 'Age Group';
      case 'date':
        return 'Date';
      case 'sort':
        return 'Sort';
      case 'search':
        return 'Search';
      default:
        // Fallback: capitalize and format
        return key.replaceAll('_', ' ').split(' ').map((word) =>
          word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : word
        ).join(' ');
    }
  }

  /// Get user-friendly label for sort field key
  static String getSortFieldLabel(String sortKey) {
    switch (sortKey.toLowerCase()) {
      // Common fields
      case 'name':
        return 'Name';
      case 'createdat':
        return 'Date Added';
      
      // Cattle fields
      case 'tagid':
        return 'Tag ID';
      case 'dateofbirth':
        return 'Date of Birth';
      case 'purchasedate':
        return 'Purchase Date';
      
      // Transaction fields
      case 'date':
        return 'Transaction Date';
      case 'amount':
        return 'Amount';
      case 'category':
        return 'Category';
      case 'description':
        return 'Description';
      
      // Weight fields
      case 'measurementdate':
        return 'Measurement Date';
      case 'weight':
        return 'Weight';
      case 'weightgain':
        return 'Weight Gain';
      case 'measurementmethod':
        return 'Measurement Method';
      
      // Breeding fields
      case 'breedingdate':
        return 'Breeding Date';
      case 'expecteddeliverydate':
        return 'Expected Delivery';
      case 'method':
        return 'Method';
      case 'status':
        return 'Status';
      
      // Health fields
      case 'treatmentdate':
        return 'Treatment Date';
      case 'treatmenttype':
        return 'Treatment Type';
      case 'veterinarian':
        return 'Veterinarian';
      
      // Milk fields
      case 'milkingdate':
        return 'Milking Date';
      case 'quantity':
        return 'Quantity';
      case 'session':
        return 'Session';
      
      default:
        // Fallback: capitalize and format
        return sortKey.replaceAll('_', ' ').split(' ').map((word) =>
          word.isNotEmpty ? word[0].toUpperCase() + word.substring(1) : word
        ).join(' ');
    }
  }
}
