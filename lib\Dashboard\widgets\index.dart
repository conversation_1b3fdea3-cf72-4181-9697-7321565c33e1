/// Universal Widget System Components
///
/// This package provides complete, universal widget systems for consistent
/// UI patterns across all modules in the Cattle Manager App.

// Core widgets
export 'app_drawer.dart';

// Universal Filter System
export 'filters/filters.dart';
export 'filters/filter_widgets.dart';
export 'filters/filter_layout.dart';

// Universal Record Card System
export 'universal_record_card.dart';

// Universal Empty State System
// export 'empty_state.dart'; // Removed - use UniversalTabEmptyState from app_tabs.dart

// Universal Screen State Management System
export 'state_indicators/index.dart';

// Universal Navigation Card System
export 'universal_navigation_card.dart';

// Universal Data Refresh System
export 'refresh_indicators/index.dart';

// Universal List Builder System
export 'universal_list_builder.dart';

// Universal Tab Screen System
export 'universal_tab_screen.dart';

// Universal Card Header System
export 'universal_card_header.dart';

// Universal Info Card System
export 'universal_info_card.dart';

// Universal Service Layer Patterns
export 'services/index.dart';

// Universal Form Field Builders
export 'form_fields/index.dart';

// Universal Form System
export 'dialogs/standard_form_dialog.dart';
export 'dialogs/standard_form_builder.dart';
