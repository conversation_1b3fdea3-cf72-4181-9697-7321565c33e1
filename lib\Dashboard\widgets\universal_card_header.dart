import 'package:flutter/material.dart';
import 'empty_state.dart';

/// Universal Card Header System
/// 
/// Provides consistent card header patterns across all modules with:
/// - Multiple layout options (standard, compact, featured, minimal)
/// - Module-specific theming and colors
/// - Action buttons and trailing widgets
/// - Badge and notification support
/// - Consistent styling and interactions

/// Card header layout types
enum CardHeaderLayout {
  standard,    // Full header with icon, title, subtitle, actions
  compact,     // Smaller header with icon and title only
  featured,    // Large header with prominent styling
  minimal,     // Text-only header with minimal styling
  inline,      // Inline header for embedded cards
}

/// Card header style types
enum CardHeaderStyle {
  filled,      // Filled background with module color
  outlined,    // Outlined border with transparent background
  elevated,    // Elevated with shadow
  flat,        // Flat with no background or border
}

/// Universal Card Header Widget
/// 
/// Replaces all existing card header implementations with a single,
/// comprehensive widget that handles all card header scenarios.
class UniversalCardHeader extends StatelessWidget {
  final String title;
  final String? subtitle;
  final IconData? icon;
  final Color? color;
  final Widget? trailing;
  final List<Widget>? actions;
  final VoidCallback? onTap;
  final CardHeaderLayout layout;
  final CardHeaderStyle style;
  final EdgeInsets? padding;
  final double? height;
  final bool showBadge;
  final String? badgeText;
  final Color? badgeColor;
  final bool showDivider;
  final BorderRadius? borderRadius;

  const UniversalCardHeader({
    super.key,
    required this.title,
    this.subtitle,
    this.icon,
    this.color,
    this.trailing,
    this.actions,
    this.onTap,
    this.layout = CardHeaderLayout.standard,
    this.style = CardHeaderStyle.filled,
    this.padding,
    this.height,
    this.showBadge = false,
    this.badgeText,
    this.badgeColor,
    this.showDivider = false,
    this.borderRadius,
  });

  /// Factory constructors for module-specific headers

  /// Weight module card header
  factory UniversalCardHeader.weight({
    required String title,
    String? subtitle,
    IconData? icon,
    Widget? trailing,
    List<Widget>? actions,
    VoidCallback? onTap,
    CardHeaderLayout layout = CardHeaderLayout.standard,
    CardHeaderStyle style = CardHeaderStyle.filled,
    bool showBadge = false,
    String? badgeText,
  }) {
    return UniversalCardHeader(
      title: title,
      subtitle: subtitle,
      icon: icon ?? Icons.monitor_weight,
      color: UniversalEmptyStateTheme.weight,
      trailing: trailing,
      actions: actions,
      onTap: onTap,
      layout: layout,
      style: style,
      showBadge: showBadge,
      badgeText: badgeText,
      badgeColor: UniversalEmptyStateTheme.weight,
    );
  }

  /// Health module card header
  factory UniversalCardHeader.health({
    required String title,
    String? subtitle,
    IconData? icon,
    Widget? trailing,
    List<Widget>? actions,
    VoidCallback? onTap,
    CardHeaderLayout layout = CardHeaderLayout.standard,
    CardHeaderStyle style = CardHeaderStyle.filled,
    bool showBadge = false,
    String? badgeText,
  }) {
    return UniversalCardHeader(
      title: title,
      subtitle: subtitle,
      icon: icon ?? Icons.health_and_safety,
      color: UniversalEmptyStateTheme.health,
      trailing: trailing,
      actions: actions,
      onTap: onTap,
      layout: layout,
      style: style,
      showBadge: showBadge,
      badgeText: badgeText,
      badgeColor: UniversalEmptyStateTheme.health,
    );
  }

  /// Breeding module card header
  factory UniversalCardHeader.breeding({
    required String title,
    String? subtitle,
    IconData? icon,
    Widget? trailing,
    List<Widget>? actions,
    VoidCallback? onTap,
    CardHeaderLayout layout = CardHeaderLayout.standard,
    CardHeaderStyle style = CardHeaderStyle.filled,
    bool showBadge = false,
    String? badgeText,
  }) {
    return UniversalCardHeader(
      title: title,
      subtitle: subtitle,
      icon: icon ?? Icons.favorite,
      color: UniversalEmptyStateTheme.breeding,
      trailing: trailing,
      actions: actions,
      onTap: onTap,
      layout: layout,
      style: style,
      showBadge: showBadge,
      badgeText: badgeText,
      badgeColor: UniversalEmptyStateTheme.breeding,
    );
  }

  /// Milk module card header
  factory UniversalCardHeader.milk({
    required String title,
    String? subtitle,
    IconData? icon,
    Widget? trailing,
    List<Widget>? actions,
    VoidCallback? onTap,
    CardHeaderLayout layout = CardHeaderLayout.standard,
    CardHeaderStyle style = CardHeaderStyle.filled,
    bool showBadge = false,
    String? badgeText,
  }) {
    return UniversalCardHeader(
      title: title,
      subtitle: subtitle,
      icon: icon ?? Icons.water_drop,
      color: UniversalEmptyStateTheme.milk,
      trailing: trailing,
      actions: actions,
      onTap: onTap,
      layout: layout,
      style: style,
      showBadge: showBadge,
      badgeText: badgeText,
      badgeColor: UniversalEmptyStateTheme.milk,
    );
  }

  /// Transactions module card header
  factory UniversalCardHeader.transactions({
    required String title,
    String? subtitle,
    IconData? icon,
    Widget? trailing,
    List<Widget>? actions,
    VoidCallback? onTap,
    CardHeaderLayout layout = CardHeaderLayout.standard,
    CardHeaderStyle style = CardHeaderStyle.filled,
    bool showBadge = false,
    String? badgeText,
  }) {
    return UniversalCardHeader(
      title: title,
      subtitle: subtitle,
      icon: icon ?? Icons.account_balance_wallet,
      color: UniversalEmptyStateTheme.transactions,
      trailing: trailing,
      actions: actions,
      onTap: onTap,
      layout: layout,
      style: style,
      showBadge: showBadge,
      badgeText: badgeText,
      badgeColor: UniversalEmptyStateTheme.transactions,
    );
  }

  /// Events module card header
  factory UniversalCardHeader.events({
    required String title,
    String? subtitle,
    IconData? icon,
    Widget? trailing,
    List<Widget>? actions,
    VoidCallback? onTap,
    CardHeaderLayout layout = CardHeaderLayout.standard,
    CardHeaderStyle style = CardHeaderStyle.filled,
    bool showBadge = false,
    String? badgeText,
  }) {
    return UniversalCardHeader(
      title: title,
      subtitle: subtitle,
      icon: icon ?? Icons.event,
      color: UniversalEmptyStateTheme.events,
      trailing: trailing,
      actions: actions,
      onTap: onTap,
      layout: layout,
      style: style,
      showBadge: showBadge,
      badgeText: badgeText,
      badgeColor: UniversalEmptyStateTheme.events,
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveColor = color ?? theme.primaryColor;

    Widget content = _buildContent(context, theme, effectiveColor);

    // Apply style wrapper
    content = _applyStyle(content, theme, effectiveColor);

    // Add tap functionality if provided
    if (onTap != null) {
      content = InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        child: content,
      );
    }

    return content;
  }

  /// Build the main content based on layout
  Widget _buildContent(BuildContext context, ThemeData theme, Color effectiveColor) {
    switch (layout) {
      case CardHeaderLayout.standard:
        return _buildStandardLayout(context, theme, effectiveColor);
      case CardHeaderLayout.compact:
        return _buildCompactLayout(context, theme, effectiveColor);
      case CardHeaderLayout.featured:
        return _buildFeaturedLayout(context, theme, effectiveColor);
      case CardHeaderLayout.minimal:
        return _buildMinimalLayout(context, theme, effectiveColor);
      case CardHeaderLayout.inline:
        return _buildInlineLayout(context, theme, effectiveColor);
    }
  }

  /// Build standard layout (full header with icon, title, subtitle, actions)
  Widget _buildStandardLayout(BuildContext context, ThemeData theme, Color effectiveColor) {
    return Container(
      height: height,
      padding: padding ?? const EdgeInsets.all(16),
      child: Row(
        children: [
          // Icon with badge
          if (icon != null) _buildIconWithBadge(effectiveColor),
          if (icon != null) const SizedBox(width: 12),

          // Title and subtitle
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _getTextColor(effectiveColor),
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: _getTextColor(effectiveColor).withValues(alpha: 0.7),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Trailing widget
          if (trailing != null) ...[
            const SizedBox(width: 8),
            trailing!,
          ],

          // Action buttons
          if (actions != null && actions!.isNotEmpty) ...[
            const SizedBox(width: 8),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: actions!,
            ),
          ],
        ],
      ),
    );
  }

  /// Build compact layout (smaller header with icon and title only)
  Widget _buildCompactLayout(BuildContext context, ThemeData theme, Color effectiveColor) {
    return Container(
      height: height ?? 48,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        children: [
          // Smaller icon
          if (icon != null) _buildIconWithBadge(effectiveColor, size: 16, radius: 16),
          if (icon != null) const SizedBox(width: 8),

          // Title only
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: _getTextColor(effectiveColor),
              ),
            ),
          ),

          // Trailing widget
          if (trailing != null) trailing!,

          // Action buttons (smaller)
          if (actions != null && actions!.isNotEmpty) ...[
            const SizedBox(width: 4),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: actions!.map((action) {
                return Padding(
                  padding: const EdgeInsets.only(left: 4),
                  child: action,
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
  }

  /// Build featured layout (large header with prominent styling)
  Widget _buildFeaturedLayout(BuildContext context, ThemeData theme, Color effectiveColor) {
    return Container(
      height: height ?? 80,
      padding: padding ?? const EdgeInsets.all(20),
      child: Row(
        children: [
          // Large icon
          if (icon != null) _buildIconWithBadge(effectiveColor, size: 28, radius: 28),
          if (icon != null) const SizedBox(width: 16),

          // Title and subtitle
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: _getTextColor(effectiveColor),
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 6),
                  Text(
                    subtitle!,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: _getTextColor(effectiveColor).withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // Trailing widget
          if (trailing != null) ...[
            const SizedBox(width: 12),
            trailing!,
          ],

          // Action buttons
          if (actions != null && actions!.isNotEmpty) ...[
            const SizedBox(width: 12),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: actions!,
            ),
          ],
        ],
      ),
    );
  }

  /// Build minimal layout (text-only header with minimal styling)
  Widget _buildMinimalLayout(BuildContext context, ThemeData theme, Color effectiveColor) {
    return Container(
      height: height,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          // Title only, no icon
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: effectiveColor,
              ),
            ),
          ),

          // Trailing widget
          if (trailing != null) trailing!,

          // Action buttons
          if (actions != null && actions!.isNotEmpty) ...[
            const SizedBox(width: 8),
            Row(
              mainAxisSize: MainAxisSize.min,
              children: actions!,
            ),
          ],
        ],
      ),
    );
  }

  /// Build inline layout (for embedded cards)
  Widget _buildInlineLayout(BuildContext context, ThemeData theme, Color effectiveColor) {
    return Container(
      height: height,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      child: Row(
        children: [
          // Small icon
          if (icon != null) ...[
            Icon(
              icon,
              size: 16,
              color: effectiveColor,
            ),
            const SizedBox(width: 8),
          ],

          // Title
          Expanded(
            child: Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: effectiveColor,
              ),
            ),
          ),

          // Trailing widget
          if (trailing != null) trailing!,
        ],
      ),
    );
  }

  /// Build icon with optional badge
  Widget _buildIconWithBadge(Color effectiveColor, {double size = 20, double radius = 20}) {
    Widget iconWidget = CircleAvatar(
      radius: radius,
      backgroundColor: effectiveColor.withValues(alpha: 0.2),
      child: Icon(
        icon,
        color: effectiveColor,
        size: size,
      ),
    );

    if (showBadge && badgeText != null) {
      iconWidget = Stack(
        children: [
          iconWidget,
          Positioned(
            right: 0,
            top: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: badgeColor ?? effectiveColor,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 16,
                minHeight: 16,
              ),
              child: Text(
                badgeText!,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      );
    }

    return iconWidget;
  }

  /// Apply style wrapper to content
  Widget _applyStyle(Widget content, ThemeData theme, Color effectiveColor) {
    switch (style) {
      case CardHeaderStyle.filled:
        return Container(
          decoration: BoxDecoration(
            color: effectiveColor.withValues(alpha: 0.1),
            borderRadius: borderRadius ?? const BorderRadius.only(
              topLeft: Radius.circular(12),
              topRight: Radius.circular(12),
            ),
          ),
          child: Column(
            children: [
              content,
              if (showDivider)
                Divider(
                  height: 1,
                  color: effectiveColor.withValues(alpha: 0.2),
                ),
            ],
          ),
        );

      case CardHeaderStyle.outlined:
        return Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: effectiveColor.withValues(alpha: 0.3),
              width: 1,
            ),
            borderRadius: borderRadius ?? BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              content,
              if (showDivider)
                Divider(
                  height: 1,
                  color: effectiveColor.withValues(alpha: 0.2),
                ),
            ],
          ),
        );

      case CardHeaderStyle.elevated:
        return Container(
          decoration: BoxDecoration(
            color: theme.cardColor,
            borderRadius: borderRadius ?? BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            children: [
              content,
              if (showDivider)
                Divider(
                  height: 1,
                  color: effectiveColor.withValues(alpha: 0.2),
                ),
            ],
          ),
        );

      case CardHeaderStyle.flat:
        return Column(
          children: [
            content,
            if (showDivider)
              Divider(
                height: 1,
                color: effectiveColor.withValues(alpha: 0.2),
              ),
          ],
        );
    }
  }

  /// Get appropriate text color based on background
  Color _getTextColor(Color backgroundColor) {
    // Use white text on dark backgrounds, dark text on light backgrounds
    return backgroundColor.computeLuminance() > 0.5
        ? backgroundColor.withValues(alpha: 0.8)
        : Colors.white;
  }
}
