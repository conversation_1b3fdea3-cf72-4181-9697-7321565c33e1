# Global Filter System

**⚠️ MOVED: Filter files have been relocated to `lib/Dashboard/widgets/filters/custom_filters/`**

This documentation describes the centralized filter system for the entire application, providing consistent UI, colors, and layout across all modules.

## New File Locations

### `lib/Dashboard/widgets/filters/custom_filters/app_filters.dart`
- **Purpose**: Defines the 4 global filter options and filter state management
- **Global Filters**: Animal Type, Breed, Gender, Cattle (all with "All" options)
- **Features**: Dynamic data fetching, dependency management, database integration
- **Data Sources**: Farm Setup database, Cattle database

### `lib/Dashboard/widgets/filters/custom_filters/app_filter_widget.dart`
- **Purpose**: Provides the UI components for the filter system
- **Components**: Filter button, filter dialog, active filter display
- **Features**: Consistent colors, responsive design, clean interface

## Additional Filter Components

### `lib/Dashboard/widgets/filters/search_widget.dart`
- **Purpose**: Universal search functionality with debouncing
- **Features**: Real-time search, configurable debounce delay, consistent theming

### `lib/Dashboard/widgets/filters/sort_widget.dart`
- **Purpose**: Universal sort functionality with field selection and direction toggle
- **Features**: Dropdown sort field selection, ascending/descending toggle

### `lib/Dashboard/widgets/filters/filter_status_bar.dart`
- **Purpose**: Display active filters and record counts
- **Features**: Active filter chips, clear all functionality, record count display

### `lib/Dashboard/widgets/filters/date_range/`
- **Purpose**: Complete date range filtering system
- **Components**: Date range picker, presets, themes, constants

## Usage

### Basic Implementation

```dart
import '../../Dashboard/widgets/filters/custom_filters/app_filters.dart';
import '../../Dashboard/widgets/filters/custom_filters/app_filter_widget.dart';

class MyRecordsTab extends StatefulWidget {
  @override
  State<MyRecordsTab> createState() => _MyRecordsTabState();
}

class _MyRecordsTabState extends State<MyRecordsTab> {
  late FilterController _filterController;

  @override
  void initState() {
    super.initState();
    _filterController = FilterController();
    _filterController.addListener(_onFiltersChanged);
  }

  @override
  void dispose() {
    _filterController.removeListener(_onFiltersChanged);
    _filterController.dispose();
    super.dispose();
  }

  void _onFiltersChanged() {
    setState(() {
      // Trigger rebuild when filters change
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Add the universal filter layout
        UniversalFilterLayout(
          controller: _filterController,
          theme: FilterTheme.cattle, // Use appropriate theme
          moduleName: 'mymodule',
          sortFields: [...SortField.commonFields, ...SortField.cattleFields], // Required parameter
          searchHint: 'Search records...',
          totalCount: totalRecords,
          filteredCount: filteredRecords,
        ),

        // Your content here
        Expanded(
          child: _buildFilteredContent(),
        ),
      ],
    );
  }

  Widget _buildFilteredContent() {
    // Apply filters to your data
    final activeFilters = _filterState.activeFilters;
    final filteredData = _applyFilters(yourData, activeFilters);
    
    return ListView.builder(
      itemCount: filteredData.length,
      itemBuilder: (context, index) {
        return YourItemWidget(item: filteredData[index]);
      },
    );
  }
}
```

### Filter Application

```dart
List<YourDataType> _applyFilters(List<YourDataType> data, Map<String, dynamic> filters) {
  List<YourDataType> result = List.from(data);

  // Apply animal type filter
  if (filters['animalType'] != null) {
    result = result.where((item) {
      // Your filtering logic here
      return item.animalType == filters['animalType'];
    }).toList();
  }

  // Apply breed filter
  if (filters['breed'] != null) {
    result = result.where((item) {
      // Your filtering logic here
      return item.breed == filters['breed'];
    }).toList();
  }

  // Apply gender filter
  if (filters['gender'] != null) {
    result = result.where((item) {
      return item.gender == filters['gender'];
    }).toList();
  }

  // Apply cattle filter
  if (filters['cattle'] != null) {
    result = result.where((item) {
      // Your filtering logic here
      final cattleDisplay = '${item.name} (${item.tagId})';
      return cattleDisplay == filters['cattle'];
    }).toList();
  }

  return result;
}
```

## Adding Module-Specific Filters

To add module-specific filters later, extend the `ModuleFilters` class in `app_filters.dart`:

```dart
// In app_filters.dart
static const Map<String, GlobalFilterConfig> yourModuleSpecific = {
  'customFilter': GlobalFilterConfig(
    key: 'customFilter',
    label: 'Custom Filter',
    placeholder: 'Select option',
    icon: Icons.your_icon,
    color: AppColors.yourColor,
    type: FilterType.dropdown,
    options: ['Option 1', 'Option 2', 'Option 3'],
    dependsOn: [],
  ),
};
```

## Benefits

1. **Consistency**: Same UI, colors, and behavior across all modules
2. **Maintainability**: Single source of truth for filter logic
3. **Extensibility**: Easy to add module-specific filters later
4. **Performance**: Efficient state management and filtering
5. **User Experience**: Clean, intuitive interface with dependency handling

## Global Filter Order

The 4 global filters are always displayed in this order:
1. **Animal Type** (independent) - Fetched from Farm Setup database + "All Types"
2. **Breed** (depends on Animal Type) - Fetched from Farm Setup database + "All Breeds"
3. **Gender** (independent) - Static options: "All Genders", "Male", "Female"
4. **Cattle** (depends on Animal Type, Breed, Gender) - Fetched from Cattle database + "All Cattle"

## Dynamic Data Features

### "All" Options
- **All Types**: Shows all cattle regardless of animal type
- **All Breeds**: Shows all cattle regardless of breed
- **All Genders**: Shows all cattle regardless of gender
- **All Cattle**: Shows all cattle (used when no specific cattle is selected)

### Database Integration
- **Animal Types**: Fetched from `FarmSetupHandler.getAllAnimalTypes()`
- **Breeds**: Fetched from `FarmSetupHandler.getBreedsByAnimalType()`
- **Cattle**: Fetched from `CattleHandler.getAllCattle()` with filtering applied

### Fallback System
- If database calls fail, the system falls back to predefined options
- Ensures the filter system always works even with connectivity issues

This order ensures proper dependency flow and consistent user experience.
