import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'app_bar.dart';
import 'app_constants.dart';
import 'app_dialog_buttons.dart';
import '../utils/responsive_helpers.dart';

/// Universal Layout System for the entire app
/// 
/// Provides consistent layout patterns across all screens with:
/// - Standardized AppBar integration
/// - Responsive design patterns
/// - Consistent spacing and padding
/// - Module-specific theming
/// - Built-in state management integration

/// Layout types for different screen patterns
enum LayoutType {
  dashboard,     // Main dashboard with grid layout
  tabScreen,     // Module screens with tabs
  listScreen,    // Simple list screens
  detailScreen,  // Detail/form screens
  dialogScreen,  // Modal/dialog screens
}

/// Layout configuration for different screen types
class LayoutConfig {
  final EdgeInsets padding;
  final EdgeInsets margin;
  final Color? backgroundColor;
  final bool safeArea;
  final bool resizeToAvoidBottomInset;
  final FloatingActionButtonLocation? fabLocation;

  const LayoutConfig({
    this.padding = const EdgeInsets.all(kPaddingMedium),
    this.margin = EdgeInsets.zero,
    this.backgroundColor,
    this.safeArea = true,
    this.resizeToAvoidBottomInset = true,
    this.fabLocation,
  });

  // Predefined configurations for different layout types
  static const dashboard = LayoutConfig(
    padding: EdgeInsets.all(kPaddingMedium),
    backgroundColor: Color(0xFFF5F5F5),
    safeArea: true,
  );

  static const tabScreen = LayoutConfig(
    padding: EdgeInsets.zero,
    backgroundColor: Colors.white,
    safeArea: false,
  );

  static const listScreen = LayoutConfig(
    padding: EdgeInsets.symmetric(horizontal: kPaddingMedium),
    backgroundColor: Colors.white,
    safeArea: true,
  );

  static const detailScreen = LayoutConfig(
    padding: EdgeInsets.all(kPaddingMedium),
    backgroundColor: Colors.white,
    safeArea: true,
    resizeToAvoidBottomInset: true,
  );

  static const dialogScreen = LayoutConfig(
    padding: EdgeInsets.all(kPaddingLarge),
    backgroundColor: Colors.white,
    safeArea: true,
    resizeToAvoidBottomInset: true,
  );
}

/// Universal Form Dialog Layout System
///
/// Provides consistent form dialog layout with:
/// - Fixed header with solid green background and white text/icons
/// - Scrollable form content area
/// - Fixed footer with action buttons
/// - Responsive sizing and constraints
class UniversalFormDialog extends StatelessWidget {
  final String title;
  final IconData headerIcon;
  final Widget formContent;
  final Widget actionButtons;
  final double? maxWidth;
  final double? maxHeight;
  final EdgeInsets? contentPadding;
  final bool scrollable;

  const UniversalFormDialog({
    Key? key,
    required this.title,
    required this.headerIcon,
    required this.formContent,
    required this.actionButtons,
    this.maxWidth,
    this.maxHeight,
    this.contentPadding,
    this.scrollable = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.8,
          maxWidth: maxWidth ?? MediaQuery.of(context).size.width * 0.9,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Fixed Header with solid green background
            Container(
              padding: const EdgeInsets.all(kPaddingMedium),
              decoration: const BoxDecoration(
                color: Color(0xFF2E7D32), // Solid green background
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16),
                  topRight: Radius.circular(16),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center, // Center the content
                children: [
                  CircleAvatar(
                    backgroundColor: Colors.white, // White circle background
                    radius: 20,
                    child: Icon(
                      headerIcon,
                      color: const Color(0xFF2E7D32), // Green icon on white background
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: kPaddingMedium),
                  Text(
                    title,
                    style: const TextStyle(
                      color: Colors.white, // White text
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),

            // Scrollable Form Content
            Flexible(
              child: scrollable
                  ? SingleChildScrollView(
                      padding: contentPadding ?? const EdgeInsets.all(kPaddingMedium),
                      child: formContent,
                    )
                  : Padding(
                      padding: contentPadding ?? const EdgeInsets.all(kPaddingMedium),
                      child: formContent,
                    ),
            ),

            // Fixed Footer with action buttons
            Container(
              padding: const EdgeInsets.all(kPaddingMedium),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerHighest, // Match Material 3 form field background
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(16),
                  bottomRight: Radius.circular(16),
                ),
              ),
              child: actionButtons,
            ),
          ],
        ),
      ),
    );
  }

  /// Factory constructor for Add forms
  factory UniversalFormDialog.add({
    required String title,
    required Widget formContent,
    required VoidCallback onCancel,
    required VoidCallback onAdd,
    String cancelText = 'Cancel',
    String addText = 'Add',
    bool isAdding = false,
    double? maxWidth,
    double? maxHeight,
    EdgeInsets? contentPadding,
    bool scrollable = true,
  }) {
    return UniversalFormDialog(
      title: title,
      headerIcon: Icons.add_circle,
      formContent: formContent,
      actionButtons: UniversalDialogButtons.cancelAddRow(
        onCancel: onCancel,
        onAdd: onAdd,
        cancelText: cancelText,
        addText: addText,
        isAdding: isAdding,
      ),
      maxWidth: maxWidth,
      maxHeight: maxHeight,
      contentPadding: contentPadding,
      scrollable: scrollable,
    );
  }

  /// Factory constructor for Edit forms
  factory UniversalFormDialog.edit({
    required String title,
    required Widget formContent,
    required VoidCallback onCancel,
    required VoidCallback onUpdate,
    String cancelText = 'Cancel',
    String updateText = 'Update',
    bool isUpdating = false,
    double? maxWidth,
    double? maxHeight,
    EdgeInsets? contentPadding,
    bool scrollable = true,
  }) {
    return UniversalFormDialog(
      title: title,
      headerIcon: Icons.edit,
      formContent: formContent,
      actionButtons: UniversalDialogButtons.cancelUpdateRow(
        onCancel: onCancel,
        onUpdate: onUpdate,
        cancelText: cancelText,
        updateText: updateText,
        isUpdating: isUpdating,
      ),
      maxWidth: maxWidth,
      maxHeight: maxHeight,
      contentPadding: contentPadding,
      scrollable: scrollable,
    );
  }
}

/// Universal Layout Builder - The main layout system
class UniversalLayout extends StatelessWidget {
  final String title;
  final Widget body;
  final LayoutType type;
  final LayoutConfig? config;
  final List<Widget>? appBarActions;
  final Widget? floatingActionButton;
  final Widget? drawer;
  final Widget? bottomNavigationBar;
  final PreferredSizeWidget? bottom;
  final VoidCallback? onRefresh;
  final bool showBackButton;
  final VoidCallback? onBackPressed;

  const UniversalLayout({
    Key? key,
    required this.title,
    required this.body,
    required this.type,
    this.config,
    this.appBarActions,
    this.floatingActionButton,
    this.drawer,
    this.bottomNavigationBar,
    this.bottom,
    this.onRefresh,
    this.showBackButton = false,
    this.onBackPressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final layoutConfig = config ?? _getDefaultConfig(type);
    
    return Scaffold(
      appBar: _buildAppBar(context),
      drawer: drawer,
      backgroundColor: layoutConfig.backgroundColor,
      resizeToAvoidBottomInset: layoutConfig.resizeToAvoidBottomInset,
      body: _buildBody(context, layoutConfig),
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: layoutConfig.fabLocation,
      bottomNavigationBar: bottomNavigationBar,
    );
  }

  /// Build the appropriate AppBar based on layout type
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    switch (type) {
      case LayoutType.dashboard:
        // Only dashboard screens use withDrawer
        return AppBarConfig.withDrawer(
          title: title,
          context: context,
          actions: appBarActions,
          bottom: bottom,
        );
      default:
        // All other screens use withBack
        return AppBarConfig.withBack(
          title: title,
          context: context,
          actions: appBarActions,
          onBack: onBackPressed,
          bottom: bottom,
        );
    }
  }

  /// Build the body with appropriate layout configuration
  Widget _buildBody(BuildContext context, LayoutConfig layoutConfig) {
    Widget bodyWidget = body;

    // Add refresh capability if specified
    if (onRefresh != null) {
      bodyWidget = RefreshIndicator(
        onRefresh: () async => onRefresh!(),
        child: bodyWidget,
      );
    }

    // Apply padding and margin
    if (layoutConfig.padding != EdgeInsets.zero) {
      bodyWidget = Padding(
        padding: layoutConfig.padding,
        child: bodyWidget,
      );
    }

    if (layoutConfig.margin != EdgeInsets.zero) {
      bodyWidget = Container(
        margin: layoutConfig.margin,
        child: bodyWidget,
      );
    }

    // Apply safe area if needed
    if (layoutConfig.safeArea) {
      bodyWidget = SafeArea(child: bodyWidget);
    }

    return bodyWidget;
  }

  /// Get default configuration for layout type
  LayoutConfig _getDefaultConfig(LayoutType type) {
    switch (type) {
      case LayoutType.dashboard:
        return LayoutConfig.dashboard;
      case LayoutType.tabScreen:
        return LayoutConfig.tabScreen;
      case LayoutType.listScreen:
        return LayoutConfig.listScreen;
      case LayoutType.detailScreen:
        return LayoutConfig.detailScreen;
      case LayoutType.dialogScreen:
        return LayoutConfig.dialogScreen;
    }
  }

  // Factory constructors for common layout patterns

  /// Dashboard layout with grid and special AppBar
  factory UniversalLayout.dashboard({
    required String title,
    required Widget body,
    List<Widget>? actions,
    Widget? drawer,
    VoidCallback? onRefresh,
  }) {
    return UniversalLayout(
      title: title,
      body: body,
      type: LayoutType.dashboard,
      appBarActions: actions,
      drawer: drawer,
      onRefresh: onRefresh,
    );
  }

  /// Tab screen layout for module screens
  factory UniversalLayout.tabScreen({
    required String title,
    required Widget body,
    List<Widget>? actions,
    Widget? floatingActionButton,
    PreferredSizeWidget? bottom,
    VoidCallback? onRefresh,
  }) {
    return UniversalLayout(
      title: title,
      body: body,
      type: LayoutType.tabScreen,
      appBarActions: actions,
      floatingActionButton: floatingActionButton,
      bottom: bottom,
      onRefresh: onRefresh,
    );
  }

  /// List screen layout for simple list views
  factory UniversalLayout.listScreen({
    required String title,
    required Widget body,
    List<Widget>? actions,
    Widget? floatingActionButton,
    VoidCallback? onRefresh,
  }) {
    return UniversalLayout(
      title: title,
      body: body,
      type: LayoutType.listScreen,
      appBarActions: actions,
      floatingActionButton: floatingActionButton,
      onRefresh: onRefresh,
    );
  }

  /// Detail screen layout with back button
  factory UniversalLayout.detailScreen({
    required String title,
    required Widget body,
    List<Widget>? actions,
    VoidCallback? onBackPressed,
    VoidCallback? onRefresh,
  }) {
    return UniversalLayout(
      title: title,
      body: body,
      type: LayoutType.detailScreen,
      appBarActions: actions,
      showBackButton: true,
      onBackPressed: onBackPressed,
      onRefresh: onRefresh,
    );
  }

  /// Dialog screen layout for modal screens
  factory UniversalLayout.dialogScreen({
    required String title,
    required Widget body,
    List<Widget>? actions,
    VoidCallback? onBackPressed,
  }) {
    return UniversalLayout(
      title: title,
      body: body,
      type: LayoutType.dialogScreen,
      appBarActions: actions,
      showBackButton: true,
      onBackPressed: onBackPressed,
    );
  }
}

/// Responsive Grid Layout Helper
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final int? maxCrossAxisCount;
  final double? childAspectRatio;
  final EdgeInsets padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const ResponsiveGrid({
    Key? key,
    required this.children,
    this.spacing = kSpacingMedium,
    this.runSpacing = kSpacingMedium,
    this.maxCrossAxisCount,
    this.childAspectRatio,
    this.padding = const EdgeInsets.all(kPaddingMedium),
    this.shrinkWrap = false,
    this.physics,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Use LayoutBuilder instead of MediaQuery for tab compatibility
    // LayoutBuilder provides the actual available width, not the full screen width
    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;

        // Determine cross axis count based on available width
        int crossAxisCount;
        if (maxCrossAxisCount != null) {
          crossAxisCount = maxCrossAxisCount!;
        } else {
          if (availableWidth > 1200) {
            crossAxisCount = 4; // Desktop
          } else if (availableWidth > 800) {
            crossAxisCount = 3; // Tablet landscape
          } else if (availableWidth > 600) {
            crossAxisCount = 2; // Tablet portrait
          } else {
            crossAxisCount = 2; // Mobile
          }
        }

        return Padding(
          padding: padding,
          child: GridView.count(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: spacing,
            mainAxisSpacing: runSpacing,
            childAspectRatio: childAspectRatio ?? 1.0,
            shrinkWrap: shrinkWrap,
            physics: physics,
            children: children,
          ),
        );
      },
    );
  }

  /// Factory for dashboard grid (2-3 columns)
  factory ResponsiveGrid.dashboard({
    required List<Widget> children,
    EdgeInsets padding = const EdgeInsets.all(kPaddingMedium),
  }) {
    return ResponsiveGrid(
      children: children,
      spacing: 12.0,
      runSpacing: 12.0,
      childAspectRatio: 1.2,
      padding: padding,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
    );
  }

  /// Factory for card grid (2-4 columns) - optimized for tab usage
  factory ResponsiveGrid.cards({
    required List<Widget> children,
    EdgeInsets padding = EdgeInsets.zero, // No padding by default for tab usage
  }) {
    return ResponsiveGrid(
      children: children,
      spacing: kSpacingMedium,
      runSpacing: kSpacingMedium,
      childAspectRatio: 1.2, // Better aspect ratio for cards
      padding: padding,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
    );
  }
}

/// Responsive Column Layout Helper
class ResponsiveColumn extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final EdgeInsets padding;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisAlignment mainAxisAlignment;

  const ResponsiveColumn({
    Key? key,
    required this.children,
    this.spacing = kSpacingMedium,
    this.padding = const EdgeInsets.all(kPaddingMedium),
    this.crossAxisAlignment = CrossAxisAlignment.start,
    this.mainAxisAlignment = MainAxisAlignment.start,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: crossAxisAlignment,
        mainAxisAlignment: mainAxisAlignment,
        children: _buildChildrenWithSpacing(),
      ),
    );
  }

  List<Widget> _buildChildrenWithSpacing() {
    if (children.isEmpty) return [];

    final spacedChildren = <Widget>[];
    for (int i = 0; i < children.length; i++) {
      spacedChildren.add(children[i]);
      if (i < children.length - 1) {
        spacedChildren.add(SizedBox(height: spacing));
      }
    }
    return spacedChildren;
  }
}

/// Layout Spacing Helpers
class LayoutSpacing {
  static const Widget small = SizedBox(height: kSpacingSmall);
  static const Widget medium = SizedBox(height: kSpacingMedium);
  static const Widget large = SizedBox(height: kSpacingLarge);
  static const Widget extraLarge = SizedBox(height: kSpacingXLarge);

  static const Widget smallHorizontal = SizedBox(width: kSpacingSmall);
  static const Widget mediumHorizontal = SizedBox(width: kSpacingMedium);
  static const Widget largeHorizontal = SizedBox(width: kSpacingLarge);
  static const Widget extraLargeHorizontal = SizedBox(width: kSpacingXLarge);

  /// Responsive spacing based on screen size
  static Widget responsive(BuildContext context) {
    return SizedBox(height: ResponsiveHelpers.getSpacing(context));
  }

  static Widget responsiveHorizontal(BuildContext context) {
    return SizedBox(width: ResponsiveHelpers.getSpacing(context));
  }
}

// ============================================================================
// UNIVERSAL FORM FIELD SYSTEM
// ============================================================================

/// Universal Form Field Configuration
///
/// Provides consistent form field styling, spacing, and behavior across all dialogs
/// with standardized layouts, validation patterns, and accessibility support.
class UniversalFormField {
  // ============================================================================
  // FORM FIELD CONSTANTS
  // ============================================================================

  /// Standard spacing between form fields
  static const double fieldSpacing = 16.0;

  /// Standard content padding for form fields
  static const EdgeInsets contentPadding = EdgeInsets.symmetric(horizontal: 12, vertical: 16);

  /// Standard constraints for form fields
  static const BoxConstraints fieldConstraints = BoxConstraints(minHeight: 60);

  /// Standard border radius for form fields
  static const BorderRadius borderRadius = BorderRadius.all(Radius.circular(16));

  // ============================================================================
  // TEXT FORM FIELDS
  // ============================================================================

  /// Creates a standard text form field with consistent styling
  static Widget textField({
    required String label,
    String? hint,
    String? initialValue,
    TextEditingController? controller,
    String? Function(String?)? validator,
    TextInputType? keyboardType,
    bool enabled = true,
    bool obscureText = false,
    int maxLines = 1,
    int? maxLength,
    IconData? prefixIcon,
    Color? prefixIconColor,
    Widget? suffixIcon,
    Function(String)? onChanged,
    VoidCallback? onTap,
    bool readOnly = false,
  }) {
    return TextFormField(
      controller: controller,
      initialValue: controller == null ? initialValue : null,
      validator: validator,
      keyboardType: keyboardType,
      enabled: enabled,
      obscureText: obscureText,
      maxLines: maxLines,
      maxLength: maxLength,
      onChanged: onChanged,
      onTap: onTap,
      readOnly: readOnly,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(borderRadius: borderRadius),
        contentPadding: contentPadding,
        constraints: fieldConstraints,
        prefixIcon: prefixIcon != null
            ? Icon(prefixIcon, color: prefixIconColor)
            : null,
        suffixIcon: suffixIcon,
      ),
    );
  }

  // ============================================================================
  // DROPDOWN FORM FIELDS
  // ============================================================================

  /// Creates a standard dropdown form field with consistent styling
  static Widget dropdownField<T>({
    required String label,
    String? hint,
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required Function(T?) onChanged,
    String? Function(T?)? validator,
    bool enabled = true,
    IconData? prefixIcon,
    Color? prefixIconColor,
    Color? labelColor, // Added label color support
    Widget? suffixIcon,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      items: items,
      onChanged: enabled ? onChanged : null,
      validator: validator,
      isExpanded: true,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(borderRadius: borderRadius),
        contentPadding: contentPadding,
        constraints: fieldConstraints,
        prefixIcon: prefixIcon != null
            ? Icon(prefixIcon, color: prefixIconColor)
            : null,
        suffixIcon: suffixIcon,
        labelStyle: labelColor != null
            ? TextStyle(color: labelColor)
            : null, // Make floating label use same color as icon
        floatingLabelStyle: labelColor != null
            ? TextStyle(color: labelColor)
            : null, // Make floating label use same color as icon
      ),
    );
  }

  // ============================================================================
  // DATE PICKER FIELDS
  // ============================================================================

  /// Creates a date picker field with consistent styling
  static Widget dateField({
    required BuildContext context,
    required String label,
    String? hint,
    required DateTime? value,
    required Function(DateTime?) onChanged,
    String? Function(DateTime?)? validator,
    DateTime? firstDate,
    DateTime? lastDate,
    bool enabled = true,
    IconData? prefixIcon,
    Color? prefixIconColor,
    String dateFormat = 'MMM dd, yyyy',
  }) {
    final controller = TextEditingController(
      text: value != null ? DateFormat(dateFormat).format(value) : '',
    );

    return TextFormField(
      controller: controller,
      enabled: enabled,
      readOnly: true,
      validator: validator != null ? (text) => validator(value) : null,
      onTap: enabled ? () async {
        final pickedDate = await showDatePicker(
          context: context,
          initialDate: value ?? DateTime.now(),
          firstDate: firstDate ?? DateTime(2000),
          lastDate: lastDate ?? DateTime.now().add(const Duration(days: 365)),
        );
        if (pickedDate != null) {
          onChanged(pickedDate);
          controller.text = DateFormat(dateFormat).format(pickedDate);
        }
      } : null,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        border: const OutlineInputBorder(borderRadius: borderRadius),
        contentPadding: contentPadding,
        constraints: fieldConstraints,
        prefixIcon: Icon(
          prefixIcon ?? Icons.calendar_today,
          color: prefixIconColor,
        ),
        suffixIcon: const Icon(Icons.arrow_drop_down),
      ),
    );
  }

  // ============================================================================
  // SPECIALIZED FORM FIELDS
  // ============================================================================

  /// Creates a number input field with consistent styling
  static Widget numberField({
    required String label,
    String? hint,
    String? initialValue,
    TextEditingController? controller,
    String? Function(String?)? validator,
    bool enabled = true,
    bool allowDecimals = false,
    double? min,
    double? max,
    IconData? prefixIcon,
    Color? prefixIconColor,
    String? suffix,
    Function(String)? onChanged,
  }) {
    return textField(
      label: label,
      hint: hint,
      initialValue: initialValue,
      controller: controller,
      validator: validator,
      keyboardType: allowDecimals
          ? const TextInputType.numberWithOptions(decimal: true)
          : TextInputType.number,
      enabled: enabled,
      prefixIcon: prefixIcon,
      prefixIconColor: prefixIconColor,
      onChanged: onChanged,
    );
  }

  /// Creates a multiline text field with consistent styling
  static Widget multilineField({
    required String label,
    String? hint,
    String? initialValue,
    TextEditingController? controller,
    String? Function(String?)? validator,
    bool enabled = true,
    int maxLines = 3,
    int? maxLength,
    IconData? prefixIcon,
    Color? prefixIconColor,
    Function(String)? onChanged,
  }) {
    return textField(
      label: label,
      hint: hint,
      initialValue: initialValue,
      controller: controller,
      validator: validator,
      enabled: enabled,
      maxLines: maxLines,
      maxLength: maxLength,
      prefixIcon: prefixIcon,
      prefixIconColor: prefixIconColor,
      onChanged: onChanged,
    );
  }

  // ============================================================================
  // FORM FIELD LAYOUTS
  // ============================================================================

  /// Creates a row with two form fields side by side
  static Widget fieldRow({
    required Widget leftField,
    required Widget rightField,
    double spacing = 12.0,
    int leftFlex = 1,
    int rightFlex = 1,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(flex: leftFlex, child: leftField),
        SizedBox(width: spacing),
        Expanded(flex: rightFlex, child: rightField),
      ],
    );
  }

  /// Creates a field with an inline toggle/switch
  static Widget fieldWithToggle({
    required Widget field,
    required String toggleLabel,
    required bool toggleValue,
    required Function(bool) onToggleChanged,
    double spacing = 8.0,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(child: field),
        SizedBox(width: spacing),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(toggleLabel, style: const TextStyle(fontSize: 14)),
            Switch(
              value: toggleValue,
              onChanged: onToggleChanged,
              activeColor: Colors.white,
              activeTrackColor: Colors.green,
            ),
          ],
        ),
      ],
    );
  }

  /// Creates a section header for grouping form fields
  static Widget sectionHeader({
    required String title,
    IconData? icon,
    Color? color,
    String? subtitle,
    EdgeInsets padding = const EdgeInsets.only(top: 24, bottom: 16),
    bool filled = false, // New parameter for filled background style
  }) {
    final content = Row(
      children: [
        if (icon != null) ...[
          Icon(
            icon,
            color: filled ? Colors.white : color,
            size: filled ? kIconSizeMedium : 20,
          ),
          SizedBox(width: filled ? kSpacingSmall : 8),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: filled ? FontWeight.bold : FontWeight.w600,
                  color: filled ? Colors.white : color,
                ),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );

    if (filled && color != null) {
      return Container(
        padding: const EdgeInsets.all(kPaddingMedium),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(kBorderRadius),
        ),
        child: content,
      );
    }

    return Padding(
      padding: padding,
      child: content,
    );
  }

  /// Creates standard spacing between form fields
  static Widget get spacing => const SizedBox(height: fieldSpacing);

  /// Creates larger spacing between form sections
  static Widget get sectionSpacing => const SizedBox(height: 24);

  // ============================================================================
  // FORM VALIDATION HELPERS
  // ============================================================================

  /// Standard required field validator
  static String? requiredValidator(String? value, [String? fieldName]) {
    if (value == null || value.trim().isEmpty) {
      return 'Please enter ${fieldName ?? 'a value'}';
    }
    return null;
  }

  /// Standard email validator
  static String? emailValidator(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Please enter an email address';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  /// Standard number validator
  static String? numberValidator(String? value, {double? min, double? max}) {
    if (value == null || value.trim().isEmpty) {
      return 'Please enter a number';
    }
    final number = double.tryParse(value);
    if (number == null) {
      return 'Please enter a valid number';
    }
    if (min != null && number < min) {
      return 'Value must be at least $min';
    }
    if (max != null && number > max) {
      return 'Value must be at most $max';
    }
    return null;
  }

  /// Standard dropdown validator
  static String? dropdownValidator<T>(T? value, [String? fieldName]) {
    if (value == null) {
      return 'Please select ${fieldName ?? 'an option'}';
    }
    return null;
  }
}
