import 'package:flutter/material.dart';
import 'app_colors.dart';
import 'app_constants.dart';

/// Universal Tab and FAB System for the entire app
///
/// Provides consistent tab bar and floating action button patterns with:
/// - Number-based tab configurations (2, 3, 4, or 5 tabs)
/// - Universal FAB behavior and positioning
/// - Flexible theming for any module
/// - Consistent interaction patterns

/// Tab configuration for any screen
class UniversalTabConfig {
  final String label;
  final IconData icon;
  final Color color;
  final bool showFAB;

  const UniversalTabConfig({
    required this.label,
    required this.icon,
    required this.color,
    this.showFAB = false,
  });
}

/// Universal tab configurations based on number of tabs
class UniversalTabConfigs {

  // ============================================================================
  // 2-TAB CONFIGURATIONS
  // ============================================================================

  /// Creates a 2-tab configuration
  /// Common pattern: Analytics + Records
  static List<UniversalTabConfig> twoTabs({
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
  }) {
    assert(labels.length == 2, 'Must provide exactly 2 labels');
    assert(icons.length == 2, 'Must provide exactly 2 icons');
    assert(colors.length == 2, 'Must provide exactly 2 colors');

    final fabStates = showFABs ?? [false, true]; // Default: FAB on second tab

    return [
      UniversalTabConfig(
        label: labels[0],
        icon: icons[0],
        color: colors[0],
        showFAB: fabStates[0],
      ),
      UniversalTabConfig(
        label: labels[1],
        icon: icons[1],
        color: colors[1],
        showFAB: fabStates[1],
      ),
    ];
  }

  // ============================================================================
  // 3-TAB CONFIGURATIONS
  // ============================================================================

  /// Creates a 3-tab configuration
  /// Common pattern: Analytics + Records + Insights
  static List<UniversalTabConfig> threeTabs({
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
  }) {
    assert(labels.length == 3, 'Must provide exactly 3 labels');
    assert(icons.length == 3, 'Must provide exactly 3 icons');
    assert(colors.length == 3, 'Must provide exactly 3 colors');

    final fabStates = showFABs ?? [false, true, false]; // Default: FAB on second tab

    return [
      UniversalTabConfig(
        label: labels[0],
        icon: icons[0],
        color: colors[0],
        showFAB: fabStates[0],
      ),
      UniversalTabConfig(
        label: labels[1],
        icon: icons[1],
        color: colors[1],
        showFAB: fabStates[1],
      ),
      UniversalTabConfig(
        label: labels[2],
        icon: icons[2],
        color: colors[2],
        showFAB: fabStates[2],
      ),
    ];
  }

  // ============================================================================
  // 4-TAB CONFIGURATIONS
  // ============================================================================

  /// Creates a 4-tab configuration
  /// Common pattern: Analytics + Records + Custom + Insights
  static List<UniversalTabConfig> fourTabs({
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
  }) {
    assert(labels.length == 4, 'Must provide exactly 4 labels');
    assert(icons.length == 4, 'Must provide exactly 4 icons');
    assert(colors.length == 4, 'Must provide exactly 4 colors');

    final fabStates = showFABs ?? [false, true, false, false]; // Default: FAB on second tab

    return [
      UniversalTabConfig(
        label: labels[0],
        icon: icons[0],
        color: colors[0],
        showFAB: fabStates[0],
      ),
      UniversalTabConfig(
        label: labels[1],
        icon: icons[1],
        color: colors[1],
        showFAB: fabStates[1],
      ),
      UniversalTabConfig(
        label: labels[2],
        icon: icons[2],
        color: colors[2],
        showFAB: fabStates[2],
      ),
      UniversalTabConfig(
        label: labels[3],
        icon: icons[3],
        color: colors[3],
        showFAB: fabStates[3],
      ),
    ];
  }

  // ============================================================================
  // 5-TAB CONFIGURATIONS
  // ============================================================================

  /// Creates a 5-tab configuration (maximum supported)
  /// Common pattern: Analytics + Records + Custom1 + Custom2 + Insights
  static List<UniversalTabConfig> fiveTabs({
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
  }) {
    assert(labels.length == 5, 'Must provide exactly 5 labels');
    assert(icons.length == 5, 'Must provide exactly 5 icons');
    assert(colors.length == 5, 'Must provide exactly 5 colors');

    final fabStates = showFABs ?? [false, true, false, false, false]; // Default: FAB on second tab

    return [
      UniversalTabConfig(
        label: labels[0],
        icon: icons[0],
        color: colors[0],
        showFAB: fabStates[0],
      ),
      UniversalTabConfig(
        label: labels[1],
        icon: icons[1],
        color: colors[1],
        showFAB: fabStates[1],
      ),
      UniversalTabConfig(
        label: labels[2],
        icon: icons[2],
        color: colors[2],
        showFAB: fabStates[2],
      ),
      UniversalTabConfig(
        label: labels[3],
        icon: icons[3],
        color: colors[3],
        showFAB: fabStates[3],
      ),
      UniversalTabConfig(
        label: labels[4],
        icon: icons[4],
        color: colors[4],
        showFAB: fabStates[4],
      ),
    ];
  }
}

/// Universal Tab Bar Widget
class UniversalTabBar extends StatelessWidget {
  final List<UniversalTabConfig> tabConfigs;
  final TabController controller;
  final Color? indicatorColor;
  final bool isScrollable;

  const UniversalTabBar({
    Key? key,
    required this.tabConfigs,
    required this.controller,
    this.indicatorColor,
    this.isScrollable = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, child) {
        final currentIndex = controller.index;
        return TabBar(
          controller: controller,
          isScrollable: isScrollable,
          indicatorColor: indicatorColor ?? AppColors.primary,
          labelColor: null, // We'll handle colors per tab
          unselectedLabelColor: null, // We'll handle colors per tab
          tabs: tabConfigs.asMap().entries.map((entry) {
            final index = entry.key;
            final config = entry.value;
            final isActive = currentIndex == index;
            final activeColor = config.color;
            final inactiveColor = Color.lerp(config.color, Colors.grey[400]!, 0.6) ?? 
                                 config.color.withValues(alpha: 0.4);

            return Tab(
              icon: Icon(
                config.icon,
                color: isActive ? activeColor : inactiveColor,
              ),
              child: Text(
                config.label,
                style: TextStyle(
                  color: isActive ? activeColor : inactiveColor,
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            );
          }).toList(),
        );
      },
    );
  }

  // Factory constructors based on number of tabs
  factory UniversalTabBar.twoTabs({
    required TabController controller,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabBar(
      tabConfigs: UniversalTabConfigs.twoTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      indicatorColor: indicatorColor,
      isScrollable: false,
    );
  }

  factory UniversalTabBar.threeTabs({
    required TabController controller,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabBar(
      tabConfigs: UniversalTabConfigs.threeTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      indicatorColor: indicatorColor,
      isScrollable: false,
    );
  }

  factory UniversalTabBar.fourTabs({
    required TabController controller,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabBar(
      tabConfigs: UniversalTabConfigs.fourTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      indicatorColor: indicatorColor,
      isScrollable: true, // 4+ tabs should be scrollable
    );
  }

  factory UniversalTabBar.fiveTabs({
    required TabController controller,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabBar(
      tabConfigs: UniversalTabConfigs.fiveTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      indicatorColor: indicatorColor,
      isScrollable: true, // 5 tabs should be scrollable
    );
  }
}

/// Universal FAB System - Completely module-agnostic
class UniversalFAB {

  /// Creates a standard add FAB with consistent styling
  static FloatingActionButton add({
    required VoidCallback onPressed,
    String tooltip = 'Add',
    Color? backgroundColor,
    bool mini = false,
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      mini: mini,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: const Icon(Icons.add),
    );
  }

  /// Creates a save FAB
  static FloatingActionButton save({
    required VoidCallback onPressed,
    String tooltip = 'Save',
    Color? backgroundColor,
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: const Icon(Icons.save),
    );
  }

  /// Creates an edit FAB
  static FloatingActionButton edit({
    required VoidCallback onPressed,
    String tooltip = 'Edit',
    Color? backgroundColor,
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: const Icon(Icons.edit),
    );
  }

  /// Creates a delete FAB
  static FloatingActionButton delete({
    required VoidCallback onPressed,
    String tooltip = 'Delete',
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: Colors.red,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: const Icon(Icons.delete),
    );
  }

  /// Creates an extended FAB
  static FloatingActionButton extended({
    required VoidCallback onPressed,
    required Widget label,
    required Widget icon,
    String? tooltip,
    Color? backgroundColor,
  }) {
    return FloatingActionButton.extended(
      onPressed: onPressed,
      label: label,
      icon: icon,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primary,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
    );
  }
}

/// Universal Tab Screen System
/// Combines tab bar and FAB management in one widget
class UniversalTabManager extends StatelessWidget {
  final List<UniversalTabConfig> tabConfigs;
  final TabController controller;
  final List<Widget> tabViews;
  final Color? indicatorColor;
  final bool isScrollable;

  const UniversalTabManager({
    Key? key,
    required this.tabConfigs,
    required this.controller,
    required this.tabViews,
    this.indicatorColor,
    this.isScrollable = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Universal Tab Bar
        UniversalTabBar(
          tabConfigs: tabConfigs,
          controller: controller,
          indicatorColor: indicatorColor,
          isScrollable: isScrollable,
        ),
        // Tab Views
        Expanded(
          child: TabBarView(
            controller: controller,
            children: tabViews,
          ),
        ),
      ],
    );
  }

  /// Get the current FAB based on selected tab
  /// Returns a FAB if the current tab should show one
  Widget? getCurrentFAB({
    VoidCallback? onPressed,
    String? tooltip,
    Color? backgroundColor,
  }) {
    if (controller.index < 0 || controller.index >= tabConfigs.length) {
      return null;
    }

    final currentConfig = tabConfigs[controller.index];
    if (!currentConfig.showFAB || onPressed == null) {
      return null;
    }

    return UniversalFAB.add(
      onPressed: onPressed,
      tooltip: tooltip ?? 'Add',
      backgroundColor: backgroundColor ?? currentConfig.color,
    );
  }

  // Factory constructors based on number of tabs
  factory UniversalTabManager.twoTabs({
    required TabController controller,
    required List<Widget> tabViews,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabManager(
      tabConfigs: UniversalTabConfigs.twoTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      tabViews: tabViews,
      indicatorColor: indicatorColor,
      isScrollable: false,
    );
  }

  factory UniversalTabManager.threeTabs({
    required TabController controller,
    required List<Widget> tabViews,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabManager(
      tabConfigs: UniversalTabConfigs.threeTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      tabViews: tabViews,
      indicatorColor: indicatorColor,
      isScrollable: false,
    );
  }

  factory UniversalTabManager.fourTabs({
    required TabController controller,
    required List<Widget> tabViews,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabManager(
      tabConfigs: UniversalTabConfigs.fourTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      tabViews: tabViews,
      indicatorColor: indicatorColor,
      isScrollable: true, // 4+ tabs should be scrollable
    );
  }

  factory UniversalTabManager.fiveTabs({
    required TabController controller,
    required List<Widget> tabViews,
    required List<String> labels,
    required List<IconData> icons,
    required List<Color> colors,
    List<bool>? showFABs,
    Color? indicatorColor,
  }) {
    return UniversalTabManager(
      tabConfigs: UniversalTabConfigs.fiveTabs(
        labels: labels,
        icons: icons,
        colors: colors,
        showFABs: showFABs,
      ),
      controller: controller,
      tabViews: tabViews,
      indicatorColor: indicatorColor,
      isScrollable: true, // 5 tabs should be scrollable
    );
  }
}
