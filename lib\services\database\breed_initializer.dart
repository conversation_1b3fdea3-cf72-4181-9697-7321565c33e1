import 'package:get_it/get_it.dart';
import 'package:logging/logging.dart';
import 'isar_initializer.dart';
import '../../Dashboard/Farm Setup/services/farm_setup_handler.dart';

/// A utility class for initializing breeds
/// This can be used from anywhere in the app to ensure breeds are properly initialized
class BreedInitializer {
  static final Logger _logger = Logger('BreedInitializer');
  
  /// Initialize default breeds if they don't exist
  static Future<bool> initializeDefaultBreeds() async {
    try {
      final getIt = GetIt.instance;
      final farmHandler = getIt<FarmSetupHandler>();
      
      // Check if breeds already exist
      final breeds = await farmHandler.getAllBreedCategories();
      if (breeds.isNotEmpty) {
        _logger.info('Breeds already exist, no need to initialize');
        return true;
      }
      
      // Check if animal types exist
      final animalTypes = await farmHandler.getAllAnimalTypes();
      if (animalTypes.isEmpty) {
        _logger.warning('No animal types found, cannot initialize breeds');
        return false;
      }
      
      // Initialize breeds
      _logger.info('Initializing default breeds...');
      await IsarInitializer.initializeDefaultBreeds();
      
      // Verify initialization
      final updatedBreeds = await farmHandler.getAllBreedCategories();
      _logger.info('Breed initialization complete. ${updatedBreeds.length} breeds created');
      
      return updatedBreeds.isNotEmpty;
    } catch (e) {
      _logger.severe('Error initializing default breeds: $e');
      return false;
    }
  }
}
