import 'package:isar/isar.dart';
import 'package:logging/logging.dart';
import 'isar_service.dart';
import '../streams/stream_service.dart';
// import '../services/notification_service.dart';
// import '../constants/record_keys.dart';
import 'package:get_it/get_it.dart';

import '../../Dashboard/Farm Setup/services/farm_setup_handler.dart';
import '../../Dashboard/Cattle/services/cattle_handler.dart';
import '../../Dashboard/Health/services/health_handler.dart';
import '../../Dashboard/Breeding/services/breeding_handler.dart';
import '../../Dashboard/Events/services/events_handler.dart';
import '../../Dashboard/Milk Records/services/milk_handler.dart';
import '../../Dashboard/Weight/services/weight_handler.dart';
import '../../Dashboard/Transactions/services/transactions_handler.dart';
import '../../Dashboard/Reports/services/reports_handler.dart';
import '../../Dashboard/Notifications/services/notifications_handler.dart';
import '../../Dashboard/Settings/services/settings_handler.dart';
// import '../../Dashboard/Farm Setup/services/milk_settings_handler.dart';
import '../../Dashboard/Farm Setup/models/animal_type_isar.dart';
import '../../Dashboard/Farm Setup/models/breed_category_isar.dart';

/// Main helper class to access all database operations and Isar functionality
/// Provides access to all handlers for different models and direct database access
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  static final Logger _logger = Logger('DatabaseHelper');

  // Provide a getter for the singleton instance
  static DatabaseHelper get instance => _instance;

  late IsarService _isarService;
  late StreamService _streamService;
  // late NotificationService _notificationService;

  // Module handlers
  late FarmSetupHandler farmSetupHandler;
  late CattleHandler cattleHandler;
  late HealthHandler healthHandler;
  late BreedingHandler breedingHandler;
  late EventsHandler eventsHandler;
  late MilkHandler milkHandler;
  late WeightHandler weightHandler;
  late TransactionsHandler transactionsHandler;
  late ReportsHandler reportsHandler;
  late NotificationsHandler notificationsHandler;
  late SettingsHandler settingsHandler;
  // late MilkSettingsHandler milkSettingsHandler;

  // Flag to track initialization state
  bool _isInitialized = false;

  // Private constructor for singleton pattern
  DatabaseHelper._internal();

  /// Initialize the database and all handlers
  Future<void> init() async {
    if (_isInitialized) {
      _logger.info('Database already initialized');
      return;
    }

    try {
      // Register self with GetIt if not already registered
      final getIt = GetIt.instance;
      if (!getIt.isRegistered<DatabaseHelper>()) {
        getIt.registerSingleton<DatabaseHelper>(this);
      }

      // Get the IsarService instance
      _isarService = await IsarService.instance;

      // Initialize stream service
      _streamService = StreamService();

      // Register StreamService with GetIt if not already registered
      if (!getIt.isRegistered<StreamService>()) {
        getIt.registerSingleton<StreamService>(_streamService);
      }

      // Initialize all handlers
      _initializeHandlers();

      _isInitialized = true;
      _logger.info('Database initialized successfully');
    } catch (e) {
      _logger.severe('Error initializing database: $e');
      rethrow;
    }
  }

  /// Initialize all the handlers
  void _initializeHandlers() {
    // Get singleton instances of all module handlers
    farmSetupHandler = FarmSetupHandler.instance;
    cattleHandler = CattleHandler.instance;
    healthHandler = HealthHandler.instance;
    breedingHandler = BreedingHandler.instance;
    eventsHandler = EventsHandler.instance;
    milkHandler = MilkHandler.instance;
    weightHandler = WeightHandler.instance;
    transactionsHandler = TransactionsHandler.instance;
    reportsHandler = ReportsHandler.instance;
    notificationsHandler = NotificationsHandler.instance;
    settingsHandler = SettingsHandler.instance;
    // milkSettingsHandler = MilkSettingsHandler.instance;

    // Register handlers with GetIt if needed
    final getIt = GetIt.instance;
    if (!getIt.isRegistered<BreedingHandler>()) {
      getIt.registerSingleton<BreedingHandler>(breedingHandler);
    }
  }

  // Stream getters
  Stream<Map<String, dynamic>> get breedingRecordStream => _streamService.breedingStream;
  Stream<Map<String, dynamic>> get pregnancyRecordStream => _streamService.pregnancyStream;
  Stream<Map<String, dynamic>> get healthRecordStream => _streamService.healthStream;
  Stream<Map<String, dynamic>> get milkRecordStream => _streamService.milkStream;
  Stream<Map<String, dynamic>> get eventRecordStream => _streamService.eventStream;
  Stream<Map<String, dynamic>> get cattleRecordStream => _streamService.cattleStream;
  Stream<Map<String, dynamic>> get farmRecordStream => _streamService.farmStream;
  Stream<Map<String, dynamic>> get deliveryRecordStream => _streamService.deliveryStream;

  /// Notify listeners about a record update
  void notifyRecordUpdate(String recordKey, String action, String cattleId, Map<String, dynamic> data) {
    // _notificationService.notifyListeners(recordKey, action, cattleId, data);
    // Simplified without notification service
    _logger.info('Record update: $recordKey - $action - $cattleId');
  }

  /// Generate a standardized record ID
  String generateStandardRecordId(String prefix, String cattleId, int sequenceNumber) {
    return '${prefix}_${cattleId}_${sequenceNumber.toString().padLeft(3, '0')}';
  }

  /// Get the next sequence number for a specific record type
  Future<int> getNextSequenceNumber(String cattleId, String recordType) async {
    switch (recordType) {
      case 'Breeding':
        final records = await breedingHandler.getBreedingRecordsForCattle(cattleId);
        return records.length + 1;
      case 'Pregnancy':
        final records = await breedingHandler.getPregnancyRecordsForCattle(cattleId);
        return records.length + 1;
      case 'Health':
        final records = await healthHandler.getHealthRecordsForCattle(cattleId);
        return records.length + 1;
      case 'Milk':
        final records = await milkHandler.getMilkRecordsForCattle(cattleId);
        return records.length + 1;
      default:
        return 1;
    }
  }

  /// Get the ID of the currently selected farm
  Future<String> getSelectedFarmId() async {
    try {
      final activeFarm = await farmSetupHandler.getActiveFarm();
      if (activeFarm != null) {
        return activeFarm.farmBusinessId ?? 'default_farm_id';
      }
      return 'default_farm_id';
    } catch (e) {
      _logger.warning('Error getting selected farm ID: $e');
      return 'default_farm_id';
    }
  }

  /// Get the Isar database instance
  Future<Isar> get database async {
    if (!_isarService.isInitialized) {
      await _isarService.initialize();
    }
    return _isarService.isar;
  }

  /// Get the Isar instance directly (for internal use)
  Isar get isar {
    if (!_isarService.isInitialized) {
      throw StateError('Isar database has not been initialized. Call database getter instead.');
    }
    return _isarService.isar;
  }

  /// Get breeding handler directly (alias for breedingHandler)
  BreedingHandler get isarBreedingHandler => breedingHandler;

  /// Get pregnancy handler directly (alias for breeding handler)
  BreedingHandler get isarPregnancyHandler => breedingHandler;

  /// Get cattle handler directly (alias for cattleHandler)
  CattleHandler get isarCattleHandler => cattleHandler;

  /// Write a transaction to the database
  Future<void> writeTxn(Future<void> Function(Isar isar) callback) async {
    try {
      final isar = await database;
      await isar.writeTxn(() async {
        await callback(isar);
      });
    } catch (e) {
      _logger.severe('Error executing write transaction: $e');
      rethrow;
    }
  }

  /// Execute a read operation on the database
  Future<T> read<T>(Future<T> Function(Isar isar) callback) async {
    try {
      final isar = await database;
      return await callback(isar);
    } catch (e) {
      _logger.severe('Error executing read operation: $e');
      rethrow;
    }
  }

  /// Clear the entire database
  Future<void> clearDatabase() async {
    try {
      final isar = await database;
      await isar.writeTxn(() async {
        await isar.clear();
      });
      _logger.info('Database cleared successfully');
    } catch (e) {
      _logger.severe('Error clearing database: $e');
      rethrow;
    }
  }

  /// Close the database connection
  Future<void> close() async {
    try {
      final isar = await database;
      await isar.close();
      _isInitialized = false;
      _logger.info('Database closed successfully');
    } catch (e) {
      _logger.severe('Error closing database: $e');
      rethrow;
    }
  }

  /// Getters for handlers
  CattleHandler get cattleHandlerInstance => cattleHandler;
  FarmSetupHandler get farmSetupHandlerInstance => farmSetupHandler;

  /// Get all animal types
  Future<List<AnimalTypeIsar>> getAnimalTypes() async {
    return await farmSetupHandler.getAllAnimalTypes();
  }

  /* Temporarily commented out until AnimalStageIsar is properly generated
  /// Get all animal stages
  Future<List<AnimalStageIsar>> getAnimalStages() async {
    return await farmSetupHandler.getAllAnimalStages();
  }

  /// Get animal stages for a specific animal type
  Future<List<AnimalStageIsar>> getAnimalStagesForType(String animalTypeId, {String? gender}) async {
    return await farmSetupHandler.getAnimalStagesForType(animalTypeId, gender: gender);
  }

  /// Calculate appropriate stage for an animal based on type, gender and age
  Future<String?> calculateAnimalStage(String animalTypeId, String gender, int ageInDays) async {
    return await farmSetupHandler.calculateAnimalStage(animalTypeId, gender, ageInDays);
  }
  */

  /// Get all breed categories
  Future<List<BreedCategoryIsar>> getBreedCategories() async {
    return await farmSetupHandler.getAllBreedCategories();
  }

  // The following methods reference undefined types and are commented out
  // until their implementations are complete

  /*
  /// Get a cattle by tag ID
  Future<CattleIsar?> getCattleByTagId(String tagId) async {
    return await cattleHandler.getCattleByTagId(tagId);
  }

  /// Get all cattle
  Future<List<CattleIsar>> getAllCattles() async {
    return await cattleHandler.getAllCattle();
  }

  /// Alias for getAllCattles
  Future<List<CattleIsar>> getAllCattle() async {
    return await getAllCattles();
  }

  /// Update a cattle
  Future<void> updateCattle(CattleIsar cattle) async {
    await cattleHandler.updateCattle(cattle);
  }

  /// Get all breeding records for a cattle as BreedingRecordIsar objects
  Future<List<BreedingRecordIsar>> getBreedingRecordsAsObjects(String cattleId) async {
    return await breedingHandler.getBreedingRecordsForCattle(cattleId);
  }

  /// Get all pregnancy records for a cattle as PregnancyRecordIsar objects
  Future<List<PregnancyRecordIsar>> getPregnancyRecordsAsObjects(String cattleId) async {
    return await breedingHandler.getPregnancyRecordsForCattle(cattleId);
  }

  /// Add or update a pregnancy record
  Future<void> addOrUpdatePregnancyRecord(PregnancyRecordIsar record) async {
    await breedingHandler.addOrUpdatePregnancyRecord(record);
  }

  /// Delete a pregnancy record
  Future<void> deletePregnancyRecord(String businessId) async {
    await breedingHandler.deletePregnancyRecord(businessId);
  }

  /// Add or update a breeding record
  Future<void> addOrUpdateBreedingRecord(BreedingRecordIsar record) async {
    if (record.businessId != null) {
      await breedingHandler.updateBreedingRecord(record);
    } else {
      await breedingHandler.addBreedingRecord(record);
    }
  }

  /// Notify about a breeding record update
  void notifyBreedingRecordUpdate(String action, String cattleId, Map<String, dynamic> data) {
    notifyRecordUpdate('breeding_records', action, cattleId, data);
  }

  /// Get breeding records for a specific cattle as Map objects
  Future<List<Map<String, dynamic>>> getBreedingRecordsForCattle(String cattleId) async {
    final records = await breedingHandler.getBreedingRecordsForCattle(cattleId);
    return records.map((record) => record.toMap()).toList();
  }

  /// Get pregnancy records for a specific cattle as Map objects
  Future<List<Map<String, dynamic>>> getPregnancyRecordsForCattle(String cattleId) async {
    final records = await breedingHandler.getPregnancyRecordsForCattle(cattleId);
    return records.map((record) => record.toMap()).toList();
  }

  /// Get delivery records for a specific cattle
  Future<List<Map<String, dynamic>>> getDeliveryRecordsForCattle(String cattleId) async {
    final records = await breedingHandler.getDeliveryRecordsForCattle(cattleId);
    return records.map((record) => record.toMap()).toList();
  }

  /// Create a pregnancy record from a breeding record
  Future<Map<String, dynamic>> createPregnancyRecordFromBreeding(Map<String, dynamic> breedingRecord) async {
    // Create a new pregnancy record from the breeding record
    final pregnancyRecord = {
      'cattleId': breedingRecord['cattleId'],
      'breedingRecordId': breedingRecord['id'],
      'startDate': breedingRecord['date'],
      'status': 'Active',
      'expectedCalvingDate': breedingRecord['expectedDate'],
      'notes': 'Created from breeding record: ${breedingRecord['id']}',
    };

    // Add the pregnancy record using the breeding handler
    final record = await breedingHandler.addPregnancyRecordFromMap(pregnancyRecord);
    return record.toMap();
  }

  /// Add a delivery record
  Future<void> addDeliveryRecord(Map<String, dynamic> deliveryRecord) async {
    await breedingHandler.addDeliveryRecordFromMap(deliveryRecord);
  }

  /// Update a delivery record
  Future<void> updateDeliveryRecord(Map<String, dynamic> deliveryRecord) async {
    await breedingHandler.updateDeliveryRecordFromMap(deliveryRecord);
  }

  /// Delete a delivery record
  Future<void> deleteDeliveryRecord(String recordId) async {
    await breedingHandler.deleteDeliveryRecord(recordId);
  }

  /// Create a new cattle record
  Future<void> createCattle(CattleIsar cattle) async {
    await cattleHandler.saveCattle(cattle);
  }

  /// Update a breeding record from a map
  Future<void> updateBreedingRecord(Map<String, dynamic> map, {bool updateLinkedPregnancyRecord = false}) async {
    await breedingHandler.updateBreedingRecordFromMap(map, updateLinkedPregnancyRecord: updateLinkedPregnancyRecord);
  }

  /// Update a pregnancy record from a map
  Future<void> updatePregnancyRecord(Map<String, dynamic> map, {bool updateLinkedBreedingRecord = false}) async {
    await breedingHandler.updatePregnancyRecordFromMap(map, updateLinkedBreedingRecord: updateLinkedBreedingRecord);
  }
  */
}