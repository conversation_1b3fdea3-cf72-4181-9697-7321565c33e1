import 'package:logging/logging.dart';
import '../../services/database/database_helper.dart';
import '../../services/database/exceptions/database_exceptions.dart';

/// Service for handling validation logic across database operations
class ValidationService {
  final Logger _logger = Logger('ValidationService');
  DatabaseHelper? _databaseHelper;

  // Allow setting the database helper for cross-record validations
  void setDatabaseHelper(DatabaseHelper databaseHelper) {
    _databaseHelper = databaseHelper;
  }

  // Validate required fields are present in a record
  void validateRequiredFields(Map<String, dynamic> record, List<String> requiredFields, {String recordType = 'Record'}) {
    final missingFields = requiredFields.where((field) => 
      !record.containsKey(field) || record[field] == null || record[field].toString().isEmpty
    ).toList();
    
    if (missingFields.isNotEmpty) {
      final missingFieldsStr = missingFields.join(', ');
      final errorMsg = '$recordType missing required fields: $missingFieldsStr';
      _logger.warning(errorMsg);
      throw ValidationException(errorMsg);
    }
  }
  
  // Log warnings for optional fields that are missing
  void logWarningForMissingOptionalFields(Map<String, dynamic> record, List<String> optionalFields, {String recordType = 'Record'}) {
    final missingFields = optionalFields.where((field) => 
      !record.containsKey(field) || record[field] == null || record[field].toString().isEmpty
    ).toList();
    
    if (missingFields.isNotEmpty) {
      final missingFieldsStr = missingFields.join(', ');
      _logger.warning('$recordType missing optional fields: $missingFieldsStr');
    }
  }
  
  // Validate breeding record
  void validateBreedingRecord(Map<String, dynamic> record) {
    // Required fields for a breeding record
    final requiredFields = ['cattleId', 'date'];
    validateRequiredFields(record, requiredFields, recordType: 'Breeding record');
    
    // Optional fields to log warnings for
    final optionalFields = ['method', 'bullId', 'notes'];
    logWarningForMissingOptionalFields(record, optionalFields, recordType: 'Breeding record');
    
    // Validate date format
    if (record.containsKey('date')) {
      try {
        DateTime.parse(record['date']);
      } catch (e) {
        final errorMsg = 'Invalid date format in breeding record: ${record['date']}';
        _logger.warning(errorMsg);
        throw ValidationException(errorMsg);
      }
    }
    
    // Cross-record validation: Check if cattle exists
    if (_databaseHelper != null && record.containsKey('cattleId')) {
      validateCattleExists(record['cattleId']);
    }
    
    // Cross-record validation: Check if bull exists if bullId is provided
    if (_databaseHelper != null && record.containsKey('bullId') && record['bullId'] != null && record['bullId'].toString().isNotEmpty) {
      validateCattleExists(record['bullId']);
    }
  }
  
  // Validate pregnancy record
  void validatePregnancyRecord(Map<String, dynamic> record) {
    // Required fields for a pregnancy record
    final requiredFields = ['cattleId', 'startDate', 'status'];
    validateRequiredFields(record, requiredFields, recordType: 'Pregnancy record');
    
    // Optional fields to log warnings for
    final optionalFields = ['notes', 'expectedCalvingDate', 'breedingRecordId'];
    logWarningForMissingOptionalFields(record, optionalFields, recordType: 'Pregnancy record');
    
    // Validate date formats
    if (record.containsKey('startDate')) {
      try {
        DateTime.parse(record['startDate']);
      } catch (e) {
        final errorMsg = 'Invalid startDate format in pregnancy record: ${record['startDate']}';
        _logger.warning(errorMsg);
        throw ValidationException(errorMsg);
      }
    }
    
    if (record.containsKey('expectedCalvingDate') && record['expectedCalvingDate'] != null) {
      try {
        DateTime.parse(record['expectedCalvingDate']);
      } catch (e) {
        final errorMsg = 'Invalid expectedCalvingDate format in pregnancy record: ${record['expectedCalvingDate']}';
        _logger.warning(errorMsg);
        throw ValidationException(errorMsg);
      }
    }
    
    // Validate status is a valid value
    if (record.containsKey('status')) {
      final validStatuses = ['confirmed', 'suspected', 'terminated', 'completed'];
      final status = record['status'].toString().toLowerCase();
      
      if (!validStatuses.contains(status)) {
        final errorMsg = 'Invalid pregnancy status: ${record['status']}. Must be one of: ${validStatuses.join(', ')}';
        _logger.warning(errorMsg);
        throw ValidationException(errorMsg);
      }
    }
    
    // Cross-record validation: Check if cattle exists
    if (_databaseHelper != null && record.containsKey('cattleId')) {
      validateCattleExists(record['cattleId']);
    }
    
    // Cross-record validation: Check if linked breeding record exists if provided
    if (_databaseHelper != null && 
        record.containsKey('breedingRecordId') && 
        record['breedingRecordId'] != null && 
        record['breedingRecordId'].toString().isNotEmpty) {
      validateBreedingRecordExists(record['cattleId'], record['breedingRecordId']);
    }
    
    // Cross-record validation: Check if cattle is female
    if (_databaseHelper != null && record.containsKey('cattleId')) {
      validateCattleIsFemale(record['cattleId']);
    }
  }
  
  // Validate cattle record
  void validateCattleRecord(Map<String, dynamic> record) {
    // Required fields for a cattle record
    final requiredFields = ['tagId', 'name', 'farmId', 'animalTypeId', 'categoryId'];
    validateRequiredFields(record, requiredFields, recordType: 'Cattle record');
    
    // Optional fields to log warnings for
    final optionalFields = ['birthDate', 'gender', 'lactating', 'color', 'breedId', 'notes'];
    logWarningForMissingOptionalFields(record, optionalFields, recordType: 'Cattle record');
    
    // Validate tag ID format
    if (record.containsKey('tagId') && record['tagId'] != null) {
      final tagId = record['tagId'].toString();
      if (tagId.contains(' ')) {
        final errorMsg = 'Tag ID should not contain spaces: $tagId';
        _logger.warning(errorMsg);
        throw ValidationException(errorMsg);
      }
    }
    
    // Validate gender is a valid value
    if (record.containsKey('gender') && record['gender'] != null) {
      final validGenders = ['male', 'female'];
      final gender = record['gender'].toString().toLowerCase();
      
      if (!validGenders.contains(gender)) {
        final errorMsg = 'Invalid gender: ${record['gender']}. Must be one of: ${validGenders.join(', ')}';
        _logger.warning(errorMsg);
        throw ValidationException(errorMsg);
      }
    }
    
    // Validate date formats
    if (record.containsKey('birthDate') && record['birthDate'] != null) {
      try {
        DateTime.parse(record['birthDate']);
      } catch (e) {
        final errorMsg = 'Invalid birthDate format in cattle record: ${record['birthDate']}';
        _logger.warning(errorMsg);
        throw ValidationException(errorMsg);
      }
    }
    
    // Cross-record validation: Check if farm exists
    if (_databaseHelper != null && record.containsKey('farmId')) {
      validateFarmExists(record['farmId']);
    }
    
    // Cross-record validation: Check if animal type exists
    if (_databaseHelper != null && record.containsKey('animalTypeId')) {
      validateAnimalTypeExists(record['animalTypeId']);
    }
    
    // Cross-record validation: Check if category exists
    if (_databaseHelper != null && record.containsKey('categoryId')) {
      validateCategoryExists(record['categoryId']);
    }
    
    // Cross-record validation: Check if breed exists
    if (_databaseHelper != null && record.containsKey('breedId') && record['breedId'] != null) {
      validateBreedExists(record['breedId']);
    }
  }
  
  // Health record validation
  void validateHealthRecord(Map<String, dynamic> record) {
    _validateRequiredFields(record, ['cattleId', 'date', 'condition', 'severity']);
    _validateStatus(record['severity'], ['low', 'medium', 'high', 'critical']);
    
    // Cross-record validation: Check if cattle exists
    if (_databaseHelper != null && record.containsKey('cattleId')) {
      validateCattleExists(record['cattleId']);
    }
  }

  // Milk record validation
  void validateMilkRecord(Map<String, dynamic> record) {
    _validateRequiredFields(record, ['cattleId', 'date', 'quantity', 'milkingTime']);
    _validateQuantity(record['quantity']);
    
    // Cross-record validation: Check if cattle exists and is female
    if (_databaseHelper != null && record.containsKey('cattleId')) {
      validateCattleExists(record['cattleId']);
      validateCattleIsFemale(record['cattleId']);
    }
  }

  // Event record validation
  void validateEventRecord(Map<String, dynamic> record) {
    _validateRequiredFields(record, ['cattleId', 'date', 'title', 'type']);
    _validateEventType(record['type']);
    
    // Cross-record validation: Check if cattle exists
    if (_databaseHelper != null && record.containsKey('cattleId')) {
      validateCattleExists(record['cattleId']);
    }
  }

  // Delivery record validation
  void validateDeliveryRecord(Map<String, dynamic> record) {
    _validateRequiredFields(record, ['motherTagId', 'date', 'outcome']);
    _validateStatus(record['outcome'], ['success', 'stillborn', 'complications']);
    
    // Cross-record validation: Check if mother exists and is female
    if (_databaseHelper != null && record.containsKey('motherTagId')) {
      validateCattleByTagIdExists(record['motherTagId']);
      validateCattleByTagIdIsFemale(record['motherTagId']);
    }
    
    // Cross-record validation: If linkedPregnancyId is provided, verify it exists
    if (_databaseHelper != null && 
        record.containsKey('pregnancyRecordId') && 
        record['pregnancyRecordId'] != null && 
        record['pregnancyRecordId'].toString().isNotEmpty) {
      validatePregnancyRecordExists(record['motherTagId'], record['pregnancyRecordId']);
    }
  }

  // Transaction validation
  void validateTransaction(Map<String, dynamic> record) {
    _validateRequiredFields(record, ['date', 'amount', 'type', 'categoryId']);
    _validateAmount(record['amount']);
    validateTransactionType(record['type']);
    
    // Cross-record validation: Check if category exists
    if (_databaseHelper != null && record.containsKey('categoryId')) {
      validateCategoryExists(record['categoryId']);
    }
  }

  // Common validation methods
  void _validateRequiredFields(Map<String, dynamic> record, List<String> fields) {
    validateRequiredFields(record, fields);
  }

  void _validateStatus(String? status, List<String> validStatuses) {
    if (status == null || !validStatuses.contains(status.toLowerCase())) {
      throw ValidationException('Invalid status: $status. Must be one of: ${validStatuses.join(', ')}');
    }
  }

  void _validateQuantity(dynamic quantity) {
    if (quantity == null) {
      throw ValidationException('Quantity is required');
    }
    
    final numValue = double.tryParse(quantity.toString());
    if (numValue == null || numValue <= 0) {
      throw ValidationException('Quantity must be a positive number');
    }
  }

  void _validateAmount(dynamic amount) {
    if (amount == null || amount is! num) {
      _logger.warning('Invalid amount: $amount. Must be a number');
      throw ValidationException('Invalid amount: $amount. Must be a number');
    }
  }

  void _validateEventType(String? type) {
    final validTypes = ['health', 'breeding', 'general', 'feed', 'movement', 'sale', 'purchase', 'vaccination'];
    if (type == null || !validTypes.contains(type.toLowerCase())) {
      throw ValidationException('Invalid event type: $type. Must be one of: ${validTypes.join(', ')}');
    }
  }

  void validateTransactionType(String type) {
    const allowedTypes = ['income', 'expense'];
    if (!allowedTypes.contains(type.toLowerCase())) {
      _logger.warning('Invalid transaction type: $type. Allowed values: ${allowedTypes.join(", ")}');
      throw ValidationException('Invalid transaction type: $type. Allowed values: ${allowedTypes.join(", ")}');
    }
  }

  // Date validation
  void validateDateRange(DateTime startDate, DateTime endDate) {
    if (startDate.isAfter(endDate)) {
      _logger.warning('Invalid date range: start date is after end date');
      throw ValidationException('Start date cannot be after end date');
    }
  }

  // ID validation
  void validateId(String? id) {
    if (id == null || id.isEmpty) {
      _logger.warning('Invalid ID: ID cannot be null or empty');
      throw ValidationException('ID cannot be null or empty');
    }
  }

  // Generic record validation
  void validateRecord(Map<String, dynamic> record, {bool requireId = true}) {
    if (requireId) {
      validateId(record['id']?.toString());
    }
  }
  
  // Cross-record validations
  Future<void> validateCattleExists(String cattleId) async {
    if (_databaseHelper == null) {
      _logger.warning('Database helper not set, skipping cattle validation');
      return;
    }
    
    try {
      // Simplified validation - log the attempt but don't throw since method is unimplemented
      _logger.info('Would check if cattle $cattleId exists');
    } catch (e) {
      if (e is ValidationException) rethrow;
      _logger.severe('Error validating cattle exists: $e');
      throw ValidationException('Error validating cattle exists: $e');
    }
  }
  
  Future<void> validateCattleByTagIdExists(String tagId) async {
    _logger.info('Would check if cattle with tag $tagId exists');
  }
  
  Future<void> validateCattleIsFemale(String cattleId) async {
    _logger.info('Would check if cattle $cattleId is female');
  }
  
  Future<void> validateCattleByTagIdIsFemale(String tagId) async {
    _logger.info('Would check if cattle with tag $tagId is female');
  }
  
  Future<void> validateBreedingRecordExists(String cattleId, String breedingRecordId) async {
    _logger.info('Would check if breeding record $breedingRecordId exists for cattle $cattleId');
  }
  
  Future<void> validatePregnancyRecordExists(String cattleId, String pregnancyRecordId) async {
    _logger.info('Would check if pregnancy record $pregnancyRecordId exists for cattle $cattleId');
  }
  
  Future<void> validateFarmExists(String farmId) async {
    _logger.info('Would check if farm $farmId exists');
  }
  
  Future<void> validateCategoryExists(String categoryId) async {
    _logger.info('Would check if category $categoryId exists');
  }
  
  Future<void> validateAnimalTypeExists(String animalTypeId) async {
    _logger.info('Would check if animal type $animalTypeId exists');
  }
  
  Future<void> validateBreedExists(String breedId) async {
    _logger.info('Would check if breed $breedId exists');
  }
}