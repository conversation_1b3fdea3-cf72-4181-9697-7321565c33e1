import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_constants.dart';

/// FAB styling utility class for consistent floating action buttons
class FabStyles {
  // Standard FAB style for ThemeData
  static FloatingActionButtonThemeData get standard =>
      FloatingActionButtonThemeData(
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
        elevation: kFabElevation,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(kFabBorderRadius),
        ),
      );

  // Creates a standard FAB with consistent styling
  static FloatingActionButton create({
    required VoidCallback onPressed,
    required Widget child,
    String? tooltip,
    bool mini = false,
    Color? backgroundColor,
  }) {
    return FloatingActionButton(
      onPressed: onPressed,
      tooltip: tooltip,
      mini: mini,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primaryColor,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
      child: child,
    );
  }

  // Creates a standard extended FAB with consistent styling
  static FloatingActionButton extended({
    required VoidCallback onPressed,
    required Widget label,
    required Widget icon,
    String? tooltip,
    Color? backgroundColor,
  }) {
    return FloatingActionButton.extended(
      onPressed: onPressed,
      label: label,
      icon: icon,
      tooltip: tooltip,
      elevation: kFabElevation,
      backgroundColor: backgroundColor ?? AppColors.primaryColor,
      foregroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(kFabBorderRadius),
      ),
    );
  }

  // Creates an add FAB (convenience method)
  static FloatingActionButton add({
    required VoidCallback onPressed,
    String tooltip = 'Add',
    Color? backgroundColor,
  }) {
    return create(
      onPressed: onPressed,
      child: const Icon(Icons.add),
      tooltip: tooltip,
      backgroundColor: backgroundColor,
    );
  }

  // Creates a delete FAB (convenience method)
  static FloatingActionButton delete({
    required VoidCallback onPressed,
    String tooltip = 'Delete',
  }) {
    return create(
      onPressed: onPressed,
      child: const Icon(Icons.delete),
      tooltip: tooltip,
      backgroundColor: Colors.red,
    );
  }

  // Creates a save FAB (convenience method)
  static FloatingActionButton save({
    required VoidCallback onPressed,
    String tooltip = 'Save',
  }) {
    return create(
      onPressed: onPressed,
      child: const Icon(Icons.save),
      tooltip: tooltip,
    );
  }

  // Creates an edit FAB (convenience method)
  static FloatingActionButton edit({
    required VoidCallback onPressed,
    String tooltip = 'Edit',
  }) {
    return create(
      onPressed: onPressed,
      child: const Icon(Icons.edit),
      tooltip: tooltip,
    );
  }
}
